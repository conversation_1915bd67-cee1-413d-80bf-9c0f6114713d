{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\AuthPages\\\\Evolune\\\\medcure\\\\src\\\\components\\\\auth\\\\LoginForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { TextField, Button, Typography, Box, Alert, Divider, Link } from '@mui/material';\nimport { useAuth } from '../../context/AuthContext';\nimport GoogleSignIn from './GoogleSignIn';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LoginForm = () => {\n  _s();\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n  const [error, setError] = useState('');\n  const [loading, setLoading] = useState(false);\n  const {\n    login,\n    googleLogin,\n    switchAuthMode\n  } = useAuth();\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n    const result = await login(email, password);\n    if (!result.success) {\n      setError(result.message);\n    }\n    setLoading(false);\n  };\n  const handleGoogleSuccess = async response => {\n    setLoading(true);\n    setError('');\n    const result = await googleLogin(response.credential);\n    if (!result.success) {\n      setError(result.message);\n    }\n    setLoading(false);\n  };\n  const handleGoogleError = () => {\n    setError('Google sign-in failed');\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      component: \"h1\",\n      variant: \"h4\",\n      align: \"center\",\n      gutterBottom: true,\n      children: \"Sign In\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 56,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      sx: {\n        mb: 2\n      },\n      children: String(error)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(GoogleSignIn, {\n      onSuccess: handleGoogleSuccess,\n      onError: handleGoogleError\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Divider, {\n      sx: {\n        my: 2\n      },\n      children: \"OR\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      component: \"form\",\n      onSubmit: handleSubmit,\n      children: [/*#__PURE__*/_jsxDEV(TextField, {\n        margin: \"normal\",\n        required: true,\n        fullWidth: true,\n        id: \"email\",\n        label: \"Email Address\",\n        name: \"email\",\n        autoComplete: \"email\",\n        autoFocus: true,\n        value: email,\n        onChange: e => setEmail(e.target.value)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TextField, {\n        margin: \"normal\",\n        required: true,\n        fullWidth: true,\n        name: \"password\",\n        label: \"Password\",\n        type: \"password\",\n        id: \"password\",\n        autoComplete: \"current-password\",\n        value: password,\n        onChange: e => setPassword(e.target.value)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"submit\",\n        fullWidth: true,\n        variant: \"contained\",\n        sx: {\n          mt: 3,\n          mb: 2\n        },\n        disabled: loading,\n        children: loading ? 'Signing In...' : 'Sign In'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        textAlign: \"center\",\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          component: \"button\",\n          variant: \"body2\",\n          onClick: e => {\n            e.preventDefault();\n            switchAuthMode('signup');\n          },\n          children: \"Don't have an account? Sign Up\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 103,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 69,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 55,\n    columnNumber: 5\n  }, this);\n};\n_s(LoginForm, \"DgH0lOfuM7q+aFRjlr9rtB160jE=\", false, function () {\n  return [useAuth];\n});\n_c = LoginForm;\nexport default LoginForm;\nvar _c;\n$RefreshReg$(_c, \"LoginForm\");", "map": {"version": 3, "names": ["React", "useState", "TextField", "<PERSON><PERSON>", "Typography", "Box", "<PERSON><PERSON>", "Divider", "Link", "useAuth", "GoogleSignIn", "jsxDEV", "_jsxDEV", "LoginForm", "_s", "email", "setEmail", "password", "setPassword", "error", "setError", "loading", "setLoading", "login", "googleLogin", "switchAuthMode", "handleSubmit", "e", "preventDefault", "result", "success", "message", "handleGoogleSuccess", "response", "credential", "handleGoogleError", "children", "component", "variant", "align", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "severity", "sx", "mb", "String", "onSuccess", "onError", "my", "onSubmit", "margin", "required", "fullWidth", "id", "label", "name", "autoComplete", "autoFocus", "value", "onChange", "target", "type", "mt", "disabled", "textAlign", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Documents/AuthPages/Evolune/medcure/src/components/auth/LoginForm.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  TextField,\n  Button,\n  Typography,\n  Box,\n  Alert,\n  Divider,\n  Link,\n} from '@mui/material';\n\nimport { useAuth } from '../../context/AuthContext';\nimport GoogleSignIn from './GoogleSignIn';\n\nconst LoginForm = () => {\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n  const [error, setError] = useState('');\n  const [loading, setLoading] = useState(false);\n  \n  const { login, googleLogin, switchAuthMode } = useAuth();\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n\n    const result = await login(email, password);\n    \n    if (!result.success) {\n      setError(result.message);\n    }\n    \n    setLoading(false);\n  };\n\n  const handleGoogleSuccess = async (response) => {\n    setLoading(true);\n    setError('');\n\n    const result = await googleLogin(response.credential);\n    \n    if (!result.success) {\n      setError(result.message);\n    }\n    \n    setLoading(false);\n  };\n\n  const handleGoogleError = () => {\n    setError('Google sign-in failed');\n  };\n\n  return (\n    <Box>\n      <Typography component=\"h1\" variant=\"h4\" align=\"center\" gutterBottom>\n        Sign In\n      </Typography>\n      \n      {error && <Alert severity=\"error\" sx={{ mb: 2 }}>{String(error)}</Alert>}\n      \n      <GoogleSignIn \n        onSuccess={handleGoogleSuccess}\n        onError={handleGoogleError}\n      />\n      \n      <Divider sx={{ my: 2 }}>OR</Divider>\n      \n      <Box component=\"form\" onSubmit={handleSubmit}>\n        <TextField\n          margin=\"normal\"\n          required\n          fullWidth\n          id=\"email\"\n          label=\"Email Address\"\n          name=\"email\"\n          autoComplete=\"email\"\n          autoFocus\n          value={email}\n          onChange={(e) => setEmail(e.target.value)}\n        />\n        <TextField\n          margin=\"normal\"\n          required\n          fullWidth\n          name=\"password\"\n          label=\"Password\"\n          type=\"password\"\n          id=\"password\"\n          autoComplete=\"current-password\"\n          value={password}\n          onChange={(e) => setPassword(e.target.value)}\n        />\n        <Button\n          type=\"submit\"\n          fullWidth\n          variant=\"contained\"\n          sx={{ mt: 3, mb: 2 }}\n          disabled={loading}\n        >\n          {loading ? 'Signing In...' : 'Sign In'}\n        </Button>\n        <Box textAlign=\"center\">\n          <Link\n            component=\"button\"\n            variant=\"body2\"\n            onClick={(e) => {\n              e.preventDefault();\n              switchAuthMode('signup');\n            }}\n          >\n            Don't have an account? Sign Up\n          </Link>\n        </Box>\n      </Box>\n    </Box>\n  );\n};\n\nexport default LoginForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,SAAS,EACTC,MAAM,EACNC,UAAU,EACVC,GAAG,EACHC,KAAK,EACLC,OAAO,EACPC,IAAI,QACC,eAAe;AAEtB,SAASC,OAAO,QAAQ,2BAA2B;AACnD,OAAOC,YAAY,MAAM,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1C,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACgB,QAAQ,EAAEC,WAAW,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACkB,KAAK,EAAEC,QAAQ,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACoB,OAAO,EAAEC,UAAU,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EAE7C,MAAM;IAAEsB,KAAK;IAAEC,WAAW;IAAEC;EAAe,CAAC,GAAGhB,OAAO,CAAC,CAAC;EAExD,MAAMiB,YAAY,GAAG,MAAOC,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBN,UAAU,CAAC,IAAI,CAAC;IAChBF,QAAQ,CAAC,EAAE,CAAC;IAEZ,MAAMS,MAAM,GAAG,MAAMN,KAAK,CAACR,KAAK,EAAEE,QAAQ,CAAC;IAE3C,IAAI,CAACY,MAAM,CAACC,OAAO,EAAE;MACnBV,QAAQ,CAACS,MAAM,CAACE,OAAO,CAAC;IAC1B;IAEAT,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAED,MAAMU,mBAAmB,GAAG,MAAOC,QAAQ,IAAK;IAC9CX,UAAU,CAAC,IAAI,CAAC;IAChBF,QAAQ,CAAC,EAAE,CAAC;IAEZ,MAAMS,MAAM,GAAG,MAAML,WAAW,CAACS,QAAQ,CAACC,UAAU,CAAC;IAErD,IAAI,CAACL,MAAM,CAACC,OAAO,EAAE;MACnBV,QAAQ,CAACS,MAAM,CAACE,OAAO,CAAC;IAC1B;IAEAT,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAED,MAAMa,iBAAiB,GAAGA,CAAA,KAAM;IAC9Bf,QAAQ,CAAC,uBAAuB,CAAC;EACnC,CAAC;EAED,oBACER,OAAA,CAACP,GAAG;IAAA+B,QAAA,gBACFxB,OAAA,CAACR,UAAU;MAACiC,SAAS,EAAC,IAAI;MAACC,OAAO,EAAC,IAAI;MAACC,KAAK,EAAC,QAAQ;MAACC,YAAY;MAAAJ,QAAA,EAAC;IAEpE;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,EAEZzB,KAAK,iBAAIP,OAAA,CAACN,KAAK;MAACuC,QAAQ,EAAC,OAAO;MAACC,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAX,QAAA,EAAEY,MAAM,CAAC7B,KAAK;IAAC;MAAAsB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eAExEhC,OAAA,CAACF,YAAY;MACXuC,SAAS,EAAEjB,mBAAoB;MAC/BkB,OAAO,EAAEf;IAAkB;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5B,CAAC,eAEFhC,OAAA,CAACL,OAAO;MAACuC,EAAE,EAAE;QAAEK,EAAE,EAAE;MAAE,CAAE;MAAAf,QAAA,EAAC;IAAE;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAS,CAAC,eAEpChC,OAAA,CAACP,GAAG;MAACgC,SAAS,EAAC,MAAM;MAACe,QAAQ,EAAE1B,YAAa;MAAAU,QAAA,gBAC3CxB,OAAA,CAACV,SAAS;QACRmD,MAAM,EAAC,QAAQ;QACfC,QAAQ;QACRC,SAAS;QACTC,EAAE,EAAC,OAAO;QACVC,KAAK,EAAC,eAAe;QACrBC,IAAI,EAAC,OAAO;QACZC,YAAY,EAAC,OAAO;QACpBC,SAAS;QACTC,KAAK,EAAE9C,KAAM;QACb+C,QAAQ,EAAGnC,CAAC,IAAKX,QAAQ,CAACW,CAAC,CAACoC,MAAM,CAACF,KAAK;MAAE;QAAApB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C,CAAC,eACFhC,OAAA,CAACV,SAAS;QACRmD,MAAM,EAAC,QAAQ;QACfC,QAAQ;QACRC,SAAS;QACTG,IAAI,EAAC,UAAU;QACfD,KAAK,EAAC,UAAU;QAChBO,IAAI,EAAC,UAAU;QACfR,EAAE,EAAC,UAAU;QACbG,YAAY,EAAC,kBAAkB;QAC/BE,KAAK,EAAE5C,QAAS;QAChB6C,QAAQ,EAAGnC,CAAC,IAAKT,WAAW,CAACS,CAAC,CAACoC,MAAM,CAACF,KAAK;MAAE;QAAApB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9C,CAAC,eACFhC,OAAA,CAACT,MAAM;QACL6D,IAAI,EAAC,QAAQ;QACbT,SAAS;QACTjB,OAAO,EAAC,WAAW;QACnBQ,EAAE,EAAE;UAAEmB,EAAE,EAAE,CAAC;UAAElB,EAAE,EAAE;QAAE,CAAE;QACrBmB,QAAQ,EAAE7C,OAAQ;QAAAe,QAAA,EAEjBf,OAAO,GAAG,eAAe,GAAG;MAAS;QAAAoB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC,CAAC,eACThC,OAAA,CAACP,GAAG;QAAC8D,SAAS,EAAC,QAAQ;QAAA/B,QAAA,eACrBxB,OAAA,CAACJ,IAAI;UACH6B,SAAS,EAAC,QAAQ;UAClBC,OAAO,EAAC,OAAO;UACf8B,OAAO,EAAGzC,CAAC,IAAK;YACdA,CAAC,CAACC,cAAc,CAAC,CAAC;YAClBH,cAAc,CAAC,QAAQ,CAAC;UAC1B,CAAE;UAAAW,QAAA,EACH;QAED;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC9B,EAAA,CAvGID,SAAS;EAAA,QAMkCJ,OAAO;AAAA;AAAA4D,EAAA,GANlDxD,SAAS;AAyGf,eAAeA,SAAS;AAAC,IAAAwD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}