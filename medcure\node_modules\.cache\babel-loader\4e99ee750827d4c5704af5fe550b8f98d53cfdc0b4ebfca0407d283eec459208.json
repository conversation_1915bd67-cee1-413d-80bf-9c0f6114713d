{"ast": null, "code": "export { default } from \"./useThemeProps.js\";\nexport { default as getThemeProps } from \"./getThemeProps.js\";", "map": {"version": 3, "names": ["default", "getThemeProps"], "sources": ["C:/Users/<USER>/OneDrive/Documents/AuthPages/Evolune/medcure/node_modules/@mui/system/esm/useThemeProps/index.js"], "sourcesContent": ["export { default } from \"./useThemeProps.js\";\nexport { default as getThemeProps } from \"./getThemeProps.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,oBAAoB;AAC5C,SAASA,OAAO,IAAIC,aAAa,QAAQ,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}