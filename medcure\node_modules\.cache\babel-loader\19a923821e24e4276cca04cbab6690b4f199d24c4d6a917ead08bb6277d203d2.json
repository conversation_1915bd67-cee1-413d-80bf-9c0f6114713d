{"ast": null, "code": "'use client';\n\nimport createStyled from '@mui/system/createStyled';\nimport defaultTheme from \"./defaultTheme.js\";\nimport THEME_ID from \"./identifier.js\";\nimport rootShouldForwardProp from \"./rootShouldForwardProp.js\";\nexport { default as slotShouldForwardProp } from \"./slotShouldForwardProp.js\";\nexport { default as rootShouldForwardProp } from \"./rootShouldForwardProp.js\";\nconst styled = createStyled({\n  themeId: THEME_ID,\n  defaultTheme,\n  rootShouldForwardProp\n});\nexport default styled;", "map": {"version": 3, "names": ["createStyled", "defaultTheme", "THEME_ID", "rootShouldForwardProp", "default", "slotShouldForwardProp", "styled", "themeId"], "sources": ["C:/Users/<USER>/OneDrive/Documents/AuthPages/Evolune/medcure/node_modules/@mui/material/esm/styles/styled.js"], "sourcesContent": ["'use client';\n\nimport createStyled from '@mui/system/createStyled';\nimport defaultTheme from \"./defaultTheme.js\";\nimport THEME_ID from \"./identifier.js\";\nimport rootShouldForwardProp from \"./rootShouldForwardProp.js\";\nexport { default as slotShouldForwardProp } from \"./slotShouldForwardProp.js\";\nexport { default as rootShouldForwardProp } from \"./rootShouldForwardProp.js\";\nconst styled = createStyled({\n  themeId: THEME_ID,\n  defaultTheme,\n  rootShouldForwardProp\n});\nexport default styled;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,YAAY,MAAM,0BAA0B;AACnD,OAAOC,YAAY,MAAM,mBAAmB;AAC5C,OAAOC,QAAQ,MAAM,iBAAiB;AACtC,OAAOC,qBAAqB,MAAM,4BAA4B;AAC9D,SAASC,OAAO,IAAIC,qBAAqB,QAAQ,4BAA4B;AAC7E,SAASD,OAAO,IAAID,qBAAqB,QAAQ,4BAA4B;AAC7E,MAAMG,MAAM,GAAGN,YAAY,CAAC;EAC1BO,OAAO,EAAEL,QAAQ;EACjBD,YAAY;EACZE;AACF,CAAC,CAAC;AACF,eAAeG,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}