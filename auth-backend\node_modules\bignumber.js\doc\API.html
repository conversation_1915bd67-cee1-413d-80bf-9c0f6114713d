<!DOCTYPE HTML>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="Author" content="<PERSON>">
<title>bignumber.js API</title>
<style>
html{font-size:100%}
body{background:#fff;font-family:"Helvetica Neue",Helvetica,Arial,sans-serif;font-size:13px;
  line-height:1.65em;min-height:100%;margin:0}
body,i{color:#000}
.nav{background:#fff;position:fixed;top:0;bottom:0;left:0;width:200px;overflow-y:auto;
  padding:15px 0 30px 15px}
div.container{width:600px;margin:50px 0 50px 240px}
p{margin:0 0 1em;width:600px}
pre,ul{margin:1em 0}
h1,h2,h3,h4,h5{margin:0;padding:1.5em 0 0}
h1,h2{padding:.75em 0}
h1{font:400 3em Verdana,sans-serif;color:#000;margin-bottom:1em}
h2{font-size:2.25em;color:#ff2a00}
h3{font-size:1.75em;color:#4dc71f}
h4{font-size:1.75em;color:#ff2a00;padding-bottom:.75em}
h5{font-size:1.2em;margin-bottom:.4em}
h6{font-size:1.1em;margin-bottom:0.8em;padding:0.5em 0}
dd{padding-top:.35em}
dt{padding-top:.5em}
b{font-weight:700}
dt b{font-size:1.3em}
a,a:visited{color:#ff2a00;text-decoration:none}
a:active,a:hover{outline:0;text-decoration:underline}
.nav a,.nav b,.nav a:visited{display:block;color:#ff2a00;font-weight:700; margin-top:15px}
.nav b{color:#4dc71f;margin-top:20px;cursor:default;width:auto}
ul{list-style-type:none;padding:0 0 0 20px}
.nav ul{line-height:14px;padding-left:0;margin:5px 0 0}
.nav ul a,.nav ul a:visited,span{display:inline;color:#000;font-family:Verdana,Geneva,sans-serif;
  font-size:11px;font-weight:400;margin:0}
.inset,ul.inset{margin-left:20px}
.inset{font-size:.9em}
.nav li{width:auto;margin:0 0 3px}
.alias{font-style:italic;margin-left:20px}
table{border-collapse:collapse;border-spacing:0;border:2px solid #a7dbd8;margin:1.75em 0;padding:0}
td,th{text-align:left;margin:0;padding:2px 5px;border:1px dotted #a7dbd8}
th{border-top:2px solid #a7dbd8;border-bottom:2px solid #a7dbd8;color:#ff2a00}
code,pre{font-family:Consolas, monaco, monospace;font-weight:400}
pre{background:#f5f5f5;white-space:pre-wrap;word-wrap:break-word;border-left:5px solid #abef98;
  padding:1px 0 1px 15px;margin:1.2em 0}
code,.nav-title{color:#ff2a00}
.end{margin-bottom:25px}
.centre{text-align:center}
.error-table{font-size:13px;width:100%}
#faq{margin:3em 0 0}
li span{float:right;margin-right:10px;color:#c0c0c0}
#js{font:inherit;color:#4dc71f}
</style>
</head>
<body>

  <div class="nav">

    <b>v9.0.2</b>

	<a class='nav-title' href="#">API</a>

    <b> CONSTRUCTOR </b>
    <ul>
      <li><a href="#bignumber">BigNumber</a></li>
    </ul>

    <a href="#methods">Methods</a>
    <ul>
      <li><a href="#clone">clone</a></li>
      <li><a href="#config" >config</a><span>set</span></li>
      <li>
        <ul class="inset">
          <li><a href="#decimal-places">DECIMAL_PLACES</a></li>
          <li><a href="#rounding-mode" >ROUNDING_MODE</a></li>
          <li><a href="#exponential-at">EXPONENTIAL_AT</a></li>
          <li><a href="#range"         >RANGE</a></li>
          <li><a href="#crypto"        >CRYPTO</a></li>
          <li><a href="#modulo-mode"   >MODULO_MODE</a></li>
          <li><a href="#pow-precision" >POW_PRECISION</a></li>
          <li><a href="#format"        >FORMAT</a></li>
          <li><a href="#alphabet"      >ALPHABET</a></li>
        </ul>
      </li>
      <li><a href="#isBigNumber">isBigNumber</a></li>
      <li><a href="#max"        >maximum</a><span>max</span></li>
      <li><a href="#min"        >minimum</a><span>min</span></li>
      <li><a href="#random"     >random</a></li>
      <li><a href="#sum"        >sum</a></li>
    </ul>

    <a href="#constructor-properties">Properties</a>
    <ul>
      <li><a href="#round-up"        >ROUND_UP</a></li>
      <li><a href="#round-down"      >ROUND_DOWN</a></li>
      <li><a href="#round-ceil"      >ROUND_CEIL</a></li>
      <li><a href="#round-floor"     >ROUND_FLOOR</a></li>
      <li><a href="#round-half-up"   >ROUND_HALF_UP</a></li>
      <li><a href="#round-half-down" >ROUND_HALF_DOWN</a></li>
      <li><a href="#round-half-even" >ROUND_HALF_EVEN</a></li>
      <li><a href="#round-half-ceil" >ROUND_HALF_CEIL</a></li>
      <li><a href="#round-half-floor">ROUND_HALF_FLOOR</a></li>
      <li><a href="#debug"           >DEBUG</a></li>
    </ul>

    <b> INSTANCE </b>

    <a href="#prototype-methods">Methods</a>
    <ul>
      <li><a href="#abs"    >absoluteValue         </a><span>abs</span>  </li>
      <li><a href="#cmp"    >comparedTo            </a>                  </li>
      <li><a href="#dp"     >decimalPlaces         </a><span>dp</span>   </li>
      <li><a href="#div"    >dividedBy             </a><span>div</span>  </li>
      <li><a href="#divInt" >dividedToIntegerBy    </a><span>idiv</span> </li>
      <li><a href="#pow"    >exponentiatedBy       </a><span>pow</span>  </li>
      <li><a href="#int"    >integerValue          </a>                  </li>
      <li><a href="#eq"     >isEqualTo             </a><span>eq</span>   </li>
      <li><a href="#isF"    >isFinite              </a>                  </li>
      <li><a href="#gt"     >isGreaterThan         </a><span>gt</span>   </li>
      <li><a href="#gte"    >isGreaterThanOrEqualTo</a><span>gte</span>  </li>
      <li><a href="#isInt"  >isInteger             </a>                  </li>
      <li><a href="#lt"     >isLessThan            </a><span>lt</span>   </li>
      <li><a href="#lte"    >isLessThanOrEqualTo   </a><span>lte</span>  </li>
      <li><a href="#isNaN"  >isNaN                 </a>                  </li>
      <li><a href="#isNeg"  >isNegative            </a>                  </li>
      <li><a href="#isPos"  >isPositive            </a>                  </li>
      <li><a href="#isZ"    >isZero                </a>                  </li>
      <li><a href="#minus"  >minus                 </a>                  </li>
      <li><a href="#mod"    >modulo                </a><span>mod</span>  </li>
      <li><a href="#times"  >multipliedBy          </a><span>times</span></li>
      <li><a href="#neg"    >negated               </a>                  </li>
      <li><a href="#plus"   >plus                  </a>                  </li>
      <li><a href="#sd"     >precision             </a><span>sd</span>   </li>
      <li><a href="#shift"  >shiftedBy             </a>                  </li>
      <li><a href="#sqrt"   >squareRoot            </a><span>sqrt</span> </li>
      <li><a href="#toE"    >toExponential         </a>                  </li>
      <li><a href="#toFix"  >toFixed               </a>                  </li>
      <li><a href="#toFor"  >toFormat              </a>                  </li>
      <li><a href="#toFr"   >toFraction            </a>                  </li>
      <li><a href="#toJSON" >toJSON                </a>                  </li>
      <li><a href="#toN"    >toNumber              </a>                  </li>
      <li><a href="#toP"    >toPrecision           </a>                  </li>
      <li><a href="#toS"    >toString              </a>                  </li>
      <li><a href="#valueOf">valueOf               </a>                  </li>
    </ul>

    <a href="#instance-properties">Properties</a>
    <ul>
      <li><a href="#coefficient">c: coefficient</a></li>
      <li><a href="#exponent"   >e: exponent</a></li>
      <li><a href="#sign"       >s: sign</a></li>
    </ul>

    <a href="#zero-nan-infinity">Zero, NaN &amp; Infinity</a>
    <a href="#Errors">Errors</a>
    <a href="#type-coercion">Type coercion</a>
    <a class='end' href="#faq">FAQ</a>

  </div>

  <div class="container">

    <h1>bignumber<span id='js'>.js</span></h1>

    <p>A JavaScript library for arbitrary-precision arithmetic.</p>
    <p><a href="https://github.com/MikeMcl/bignumber.js">Hosted on GitHub</a>. </p>

    <h2>API</h2>

    <p>
      See the <a href='https://github.com/MikeMcl/bignumber.js'>README</a> on GitHub for a
      quick-start introduction.
    </p>
    <p>
      In all examples below, <code>var</code> and semicolons are not shown, and if a commented-out
      value is in quotes it means <code>toString</code> has been called on the preceding expression.
    </p>


    <h3>CONSTRUCTOR</h3>


    <h5 id="bignumber">
      BigNumber<code class='inset'>BigNumber(n [, base]) <i>&rArr; BigNumber</i></code>
    </h5>
    <p>
      <code>n</code>: <i>number|string|BigNumber</i><br />
      <code>base</code>: <i>number</i>: integer, <code>2</code> to <code>36</code> inclusive. (See
      <a href='#alphabet'><code>ALPHABET</code></a> to extend this range).
    </p>
    <p>
      Returns a new instance of a BigNumber object with value <code>n</code>, where <code>n</code>
      is a numeric value in the specified <code>base</code>, or base <code>10</code> if
      <code>base</code> is omitted or is <code>null</code> or <code>undefined</code>.
    </p>
    <p>
      Note that the BigNnumber constructor accepts an <code>n</code> of type <em>number</em> purely
      as a convenience so that string quotes don't have to be typed when entering literal values,
      and that it is the <code>toString</code> value of <code>n</code> that is used rather than its
      underlying binary floating point value converted to decimal. 
    </p>
    <pre>
x = new BigNumber(123.4567)                // '123.4567'
// 'new' is optional
y = BigNumber(x)                           // '123.4567'</pre>
    <p>
      If <code>n</code> is a base <code>10</code> value it can be in normal or exponential notation.
      Values in other bases must be in normal notation. Values in any base can have fraction digits,
      i.e. digits after the decimal point.
    </p>
    <pre>
new BigNumber(43210)                       // '43210'
new BigNumber('4.321e+4')                  // '43210'
new BigNumber('-735.0918e-430')            // '-7.350918e-428'
new BigNumber('123412421.234324', 5)       // '607236.557696'</pre>
    <p>
      Signed <code>0</code>, signed <code>Infinity</code> and <code>NaN</code> are supported.
    </p>
    <pre>
new BigNumber('-Infinity')                 // '-Infinity'
new BigNumber(NaN)                         // 'NaN'
new BigNumber(-0)                          // '0'
new BigNumber('.5')                        // '0.5'
new BigNumber('+2')                        // '2'</pre>
    <p>
      String values in hexadecimal literal form, e.g. <code>'0xff'</code> or <code>'0xFF'</code>
      (but not <code>'0xfF'</code>), are valid, as are string values with the octal and binary
      prefixs <code>'0o'</code> and <code>'0b'</code>. String values in octal literal form without
      the prefix will be interpreted as decimals, e.g. <code>'011'</code> is interpreted as 11, not 9.
    </p>
    <pre>
new BigNumber(-10110100.1, 2)              // '-180.5'
new BigNumber('-0b10110100.1')             // '-180.5'
new BigNumber('ff.8', 16)                  // '255.5'
new BigNumber('0xff.8')                    // '255.5'</pre>
    <p>
      If a base is specified, <code>n</code> is rounded according to the current
      <a href='#decimal-places'><code>DECIMAL_PLACES</code></a> and
      <a href='#rounding-mode'><code>ROUNDING_MODE</code></a> settings. <em>This includes base
      <code>10</code> so don't include a <code>base</code> parameter for decimal values unless
      this behaviour is wanted.</em>
    </p>
    <pre>BigNumber.config({ DECIMAL_PLACES: 5 })
new BigNumber(1.23456789)                  // '1.23456789'
new BigNumber(1.23456789, 10)              // '1.23457'</pre>
    <p>An error is thrown if <code>base</code> is invalid. See <a href='#Errors'>Errors</a>.</p>
    <p>
      There is no limit to the number of digits of a value of type <em>string</em> (other than
      that of JavaScript's maximum array size). See <a href='#range'><code>RANGE</code></a> to set
      the maximum and minimum possible exponent value of a BigNumber.
    </p>
    <pre>
new BigNumber('5032485723458348569331745.33434346346912144534543')
new BigNumber('4.321e10000000')</pre>
    <p>BigNumber <code>NaN</code> is returned if <code>n</code> is invalid
    (unless <code>BigNumber.DEBUG</code> is <code>true</code>, see below).</p>
    <pre>
new BigNumber('.1*')                       // 'NaN'
new BigNumber('blurgh')                    // 'NaN'
new BigNumber(9, 2)                        // 'NaN'</pre>
    <p>
      To aid in debugging, if <code>BigNumber.DEBUG</code> is <code>true</code> then an error will
      be thrown on an invalid <code>n</code>. An error will also be thrown if <code>n</code> is of
      type <em>number</em> and has more than <code>15</code> significant digits, as calling
      <code><a href='#toS'>toString</a></code> or <code><a href='#valueOf'>valueOf</a></code> on
      these numbers may not result in the intended value.
    </p>
      <pre>
console.log(823456789123456.3)            //  823456789123456.2
new BigNumber(823456789123456.3)          // '823456789123456.2'
BigNumber.DEBUG = true
// '[BigNumber Error] Number primitive has more than 15 significant digits'
new BigNumber(823456789123456.3)
// '[BigNumber Error] Not a base 2 number'
new BigNumber(9, 2)</pre>
    <p>
      A BigNumber can also be created from an object literal.
      Use <code><a href='#isBigNumber'>isBigNumber</a></code> to check that it is well-formed.
    </p>
    <pre>new BigNumber({ s: 1, e: 2, c: [ 777, 12300000000000 ], _isBigNumber: true })    // '777.123'</pre>




    <h4 id="methods">Methods</h4>
     <p>The static methods of a BigNumber constructor.</p>




    <h5 id="clone">clone
      <code class='inset'>.clone([object]) <i>&rArr; BigNumber constructor</i></code>
    </h5>
    <p><code>object</code>: <i>object</i></p>
    <p>
      Returns a new independent BigNumber constructor with configuration as described by
      <code>object</code> (see <a href='#config'><code>config</code></a>), or with the default
      configuration if <code>object</code> is <code>null</code> or <code>undefined</code>.
    </p>
    <p>
      Throws if <code>object</code> is not an object. See <a href='#Errors'>Errors</a>.
    </p>
    <pre>BigNumber.config({ DECIMAL_PLACES: 5 })
BN = BigNumber.clone({ DECIMAL_PLACES: 9 })

x = new BigNumber(1)
y = new BN(1)

x.div(3)                        // 0.33333
y.div(3)                        // 0.333333333

// BN = BigNumber.clone({ DECIMAL_PLACES: 9 }) is equivalent to:
BN = BigNumber.clone()
BN.config({ DECIMAL_PLACES: 9 })</pre>



    <h5 id="config">config<code class='inset'>set([object]) <i>&rArr; object</i></code></h5>
    <p>
      <code>object</code>: <i>object</i>: an object that contains some or all of the following
      properties.
    </p>
    <p>Configures the settings for this particular BigNumber constructor.</p>

    <dl class='inset'>
      <dt id="decimal-places"><code><b>DECIMAL_PLACES</b></code></dt>
      <dd>
        <i>number</i>: integer, <code>0</code> to <code>1e+9</code> inclusive<br />
        Default value: <code>20</code>
      </dd>
      <dd>
        The <em>maximum</em> number of decimal places of the results of operations involving
        division, i.e. division, square root and base conversion operations, and power operations
        with negative exponents.<br />
      </dd>
      <dd>
      <pre>BigNumber.config({ DECIMAL_PLACES: 5 })
BigNumber.set({ DECIMAL_PLACES: 5 })    // equivalent</pre>
      </dd>



      <dt id="rounding-mode"><code><b>ROUNDING_MODE</b></code></dt>
      <dd>
        <i>number</i>: integer, <code>0</code> to <code>8</code> inclusive<br />
        Default value: <code>4</code> <a href="#round-half-up">(<code>ROUND_HALF_UP</code>)</a>
      </dd>
      <dd>
        The rounding mode used in the above operations and the default rounding mode of
        <a href='#dp'><code>decimalPlaces</code></a>,
        <a href='#sd'><code>precision</code></a>,
        <a href='#toE'><code>toExponential</code></a>,
        <a href='#toFix'><code>toFixed</code></a>,
        <a href='#toFor'><code>toFormat</code></a> and
        <a href='#toP'><code>toPrecision</code></a>.
      </dd>
      <dd>The modes are available as enumerated properties of the BigNumber constructor.</dd>
       <dd>
      <pre>BigNumber.config({ ROUNDING_MODE: 0 })
BigNumber.set({ ROUNDING_MODE: BigNumber.ROUND_UP })    // equivalent</pre>
        </dd>



      <dt id="exponential-at"><code><b>EXPONENTIAL_AT</b></code></dt>
      <dd>
        <i>number</i>: integer, magnitude <code>0</code> to <code>1e+9</code> inclusive, or
        <br />
        <i>number</i>[]: [ integer <code>-1e+9</code> to <code>0</code> inclusive, integer
        <code>0</code> to <code>1e+9</code> inclusive ]<br />
        Default value: <code>[-7, 20]</code>
      </dd>
      <dd>
        The exponent value(s) at which <code>toString</code> returns exponential notation.
      </dd>
      <dd>
        If a single number is assigned, the value is the exponent magnitude.<br />
        If an array of two numbers is assigned then the first number is the negative exponent
        value at and beneath which exponential notation is used, and the second number is the
        positive exponent value at and above which the same.
      </dd>
      <dd>
        For example, to emulate JavaScript numbers in terms of the exponent values at which they
        begin to use exponential notation, use <code>[-7, 20]</code>.
      </dd>
      <dd>
      <pre>BigNumber.config({ EXPONENTIAL_AT: 2 })
new BigNumber(12.3)         // '12.3'        e is only 1
new BigNumber(123)          // '1.23e+2'
new BigNumber(0.123)        // '0.123'       e is only -1
new BigNumber(0.0123)       // '1.23e-2'

BigNumber.config({ EXPONENTIAL_AT: [-7, 20] })
new BigNumber(123456789)    // '123456789'   e is only 8
new BigNumber(0.000000123)  // '1.23e-7'

// Almost never return exponential notation:
BigNumber.config({ EXPONENTIAL_AT: 1e+9 })

// Always return exponential notation:
BigNumber.config({ EXPONENTIAL_AT: 0 })</pre>
      </dd>
      <dd>
        Regardless of the value of <code>EXPONENTIAL_AT</code>, the <code>toFixed</code> method
        will always return a value in normal notation and the <code>toExponential</code> method
        will always return a value in exponential form.
      </dd>
      <dd>
        Calling <code>toString</code> with a base argument, e.g. <code>toString(10)</code>, will
        also always return normal notation.
      </dd>



      <dt id="range"><code><b>RANGE</b></code></dt>
      <dd>
        <i>number</i>: integer, magnitude <code>1</code> to <code>1e+9</code> inclusive, or
        <br />
        <i>number</i>[]: [ integer <code>-1e+9</code> to <code>-1</code> inclusive, integer
        <code>1</code> to <code>1e+9</code> inclusive ]<br />
        Default value: <code>[-1e+9, 1e+9]</code>
      </dd>
      <dd>
        The exponent value(s) beyond which overflow to <code>Infinity</code> and underflow to
        zero occurs.
      </dd>
      <dd>
        If a single number is assigned, it is the maximum exponent magnitude: values wth a
        positive exponent of greater magnitude become <code>Infinity</code> and those with a
        negative exponent of greater magnitude become zero.
      <dd>
        If an array of two numbers is assigned then the first number is the negative exponent
        limit and the second number is the positive exponent limit.
      </dd>
      <dd>
        For example, to emulate JavaScript numbers in terms of the exponent values at which they
        become zero and <code>Infinity</code>, use <code>[-324, 308]</code>.
      </dd>
      <dd>
      <pre>BigNumber.config({ RANGE: 500 })
BigNumber.config().RANGE     // [ -500, 500 ]
new BigNumber('9.999e499')   // '9.999e+499'
new BigNumber('1e500')       // 'Infinity'
new BigNumber('1e-499')      // '1e-499'
new BigNumber('1e-500')      // '0'

BigNumber.config({ RANGE: [-3, 4] })
new BigNumber(99999)         // '99999'      e is only 4
new BigNumber(100000)        // 'Infinity'   e is 5
new BigNumber(0.001)         // '0.01'       e is only -3
new BigNumber(0.0001)        // '0'          e is -4</pre>
      </dd>
      <dd>
        The largest possible magnitude of a finite BigNumber is
        <code>9.999...e+1000000000</code>.<br />
        The smallest possible magnitude of a non-zero BigNumber is <code>1e-1000000000</code>.
      </dd>



      <dt id="crypto"><code><b>CRYPTO</b></code></dt>
      <dd>
        <i>boolean</i>: <code>true</code> or <code>false</code>.<br />
        Default value: <code>false</code>
      </dd>
      <dd>
        The value that determines whether cryptographically-secure pseudo-random number
        generation is used.
      </dd>
      <dd>
        If <code>CRYPTO</code> is set to <code>true</code> then the
        <a href='#random'><code>random</code></a> method will generate random digits using
        <code>crypto.getRandomValues</code> in browsers that support it, or
        <code>crypto.randomBytes</code> if using Node.js.
      </dd>
      <dd>
        If neither function is supported by the host environment then attempting to set
        <code>CRYPTO</code> to <code>true</code> will fail and an exception will be thrown.
      </dd>
      <dd>
        If <code>CRYPTO</code> is <code>false</code> then the source of randomness used will be
        <code>Math.random</code> (which is assumed to generate at least <code>30</code> bits of
        randomness).
      </dd>
      <dd>See <a href='#random'><code>random</code></a>.</dd>
      <dd>
      <pre>
// Node.js
const crypto = require('crypto');   // CommonJS
import * as crypto from 'crypto';   // ES module

global.crypto = crypto;

BigNumber.config({ CRYPTO: true })
BigNumber.config().CRYPTO       // true
BigNumber.random()              // 0.54340758610486147524</pre>
      </dd>



      <dt id="modulo-mode"><code><b>MODULO_MODE</b></code></dt>
      <dd>
        <i>number</i>: integer, <code>0</code> to <code>9</code> inclusive<br />
        Default value: <code>1</code> (<a href="#round-down"><code>ROUND_DOWN</code></a>)
      </dd>
      <dd>The modulo mode used when calculating the modulus: <code>a mod n</code>.</dd>
      <dd>
        The quotient, <code>q = a / n</code>, is calculated according to the
        <a href='#rounding-mode'><code>ROUNDING_MODE</code></a> that corresponds to the chosen
        <code>MODULO_MODE</code>.
      </dd>
      <dd>The remainder, <code>r</code>, is calculated as: <code>r = a - n * q</code>.</dd>
      <dd>
        The modes that are most commonly used for the modulus/remainder operation are shown in
        the following table. Although the other rounding modes can be used, they may not give
        useful results.
      </dd>
      <dd>
        <table>
          <tr><th>Property</th><th>Value</th><th>Description</th></tr>
          <tr>
            <td><b>ROUND_UP</b></td><td class='centre'>0</td>
            <td>
              The remainder is positive if the dividend is negative, otherwise it is negative.
            </td>
          </tr>
          <tr>
            <td><b>ROUND_DOWN</b></td><td class='centre'>1</td>
            <td>
              The remainder has the same sign as the dividend.<br />
              This uses 'truncating division' and matches the behaviour of JavaScript's
              remainder operator <code>%</code>.
            </td>
          </tr>
          <tr>
            <td><b>ROUND_FLOOR</b></td><td class='centre'>3</td>
            <td>
              The remainder has the same sign as the divisor.<br />
              This matches Python's <code>%</code> operator.
            </td>
          </tr>
          <tr>
            <td><b>ROUND_HALF_EVEN</b></td><td class='centre'>6</td>
            <td>The <i>IEEE 754</i> remainder function.</td>
          </tr>
           <tr>
             <td><b>EUCLID</b></td><td class='centre'>9</td>
             <td>
               The remainder is always positive. Euclidian division: <br />
               <code>q = sign(n) * floor(a / abs(n))</code>
             </td>
           </tr>
        </table>
      </dd>
      <dd>
        The rounding/modulo modes are available as enumerated properties of the BigNumber
        constructor.
      </dd>
      <dd>See <a href='#mod'><code>modulo</code></a>.</dd>
      <dd>
        <pre>BigNumber.config({ MODULO_MODE: BigNumber.EUCLID })
BigNumber.config({ MODULO_MODE: 9 })          // equivalent</pre>
      </dd>



      <dt id="pow-precision"><code><b>POW_PRECISION</b></code></dt>
      <dd>
        <i>number</i>: integer, <code>0</code> to <code>1e+9</code> inclusive.<br />
        Default value: <code>0</code>
      </dd>
      <dd>
        The <i>maximum</i> precision, i.e. number of significant digits, of the result of the power
        operation (unless a modulus is specified).
      </dd>
      <dd>If set to <code>0</code>, the number of significant digits will not be limited.</dd>
      <dd>See <a href='#pow'><code>exponentiatedBy</code></a>.</dd>
      <dd><pre>BigNumber.config({ POW_PRECISION: 100 })</pre></dd>



      <dt id="format"><code><b>FORMAT</b></code></dt>
      <dd><i>object</i></dd>
      <dd>
        The <code>FORMAT</code> object configures the format of the string returned by the
        <a href='#toFor'><code>toFormat</code></a> method.
      </dd>
      <dd>
        The example below shows the properties of the <code>FORMAT</code> object that are
        recognised, and their default values.
      </dd>
      <dd>
         Unlike the other configuration properties, the values of the properties of the
         <code>FORMAT</code> object will not be checked for validity. The existing
         <code>FORMAT</code> object will simply be replaced by the object that is passed in.
         The object can include any number of the properties shown below.
      </dd>
      <dd>See <a href='#toFor'><code>toFormat</code></a> for examples of usage.</dd>
      <dd>
      <pre>
BigNumber.config({
  FORMAT: {
    // string to prepend
    prefix: '',
    // decimal separator
    decimalSeparator: '.',
    // grouping separator of the integer part
    groupSeparator: ',',
    // primary grouping size of the integer part
    groupSize: 3,
    // secondary grouping size of the integer part
    secondaryGroupSize: 0,
    // grouping separator of the fraction part
    fractionGroupSeparator: ' ',
    // grouping size of the fraction part
    fractionGroupSize: 0,
    // string to append
    suffix: ''
  }
});</pre>
      </dd>



      <dt id="alphabet"><code><b>ALPHABET</b></code></dt>
      <dd>
        <i>string</i><br />
        Default value: <code>'0123456789abcdefghijklmnopqrstuvwxyz'</code>
      </dd>
      <dd>
        The alphabet used for base conversion. The length of the alphabet corresponds to the
        maximum value of the base argument that can be passed to the
        <a href='#bignumber'><code>BigNumber</code></a> constructor or
        <a href='#toS'><code>toString</code></a>.
      </dd>
      <dd>
        There is no maximum length for the alphabet, but it must be at least 2 characters long, and
        it must not contain whitespace or a repeated character, or the sign indicators
        <code>'+'</code> and <code>'-'</code>, or the decimal separator <code>'.'</code>.
      </dd>
      <dd>
        <pre>// duodecimal (base 12)
BigNumber.config({ ALPHABET: '0123456789TE' })
x = new BigNumber('T', 12)
x.toString()                // '10'
x.toString(12)              // 'T'</pre>
      </dd>



    </dl>
    <br /><br />
    <p>Returns an object with the above properties and their current values.</p>
    <p>
      Throws if <code>object</code> is not an object, or if an invalid value is assigned to
      one or more of the above properties. See <a href='#Errors'>Errors</a>.
    </p>
    <pre>
BigNumber.config({
  DECIMAL_PLACES: 40,
  ROUNDING_MODE: BigNumber.ROUND_HALF_CEIL,
  EXPONENTIAL_AT: [-10, 20],
  RANGE: [-500, 500],
  CRYPTO: true,
  MODULO_MODE: BigNumber.ROUND_FLOOR,
  POW_PRECISION: 80,
  FORMAT: {
    groupSize: 3,
    groupSeparator: ' ',
    decimalSeparator: ','
  },
  ALPHABET: '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ$_'
});

obj = BigNumber.config();
obj.DECIMAL_PLACES        // 40
obj.RANGE                 // [-500, 500]</pre>



    <h5 id="isBigNumber">
      isBigNumber<code class='inset'>.isBigNumber(value) <i>&rArr; boolean</i></code>
    </h5>
    <p><code>value</code>: <i>any</i><br /></p>
    <p>
      Returns <code>true</code> if <code>value</code> is a BigNumber instance, otherwise returns
      <code>false</code>.
    </p>
    <pre>x = 42
y = new BigNumber(x)

BigNumber.isBigNumber(x)             // false
y instanceof BigNumber               // true
BigNumber.isBigNumber(y)             // true

BN = BigNumber.clone();
z = new BN(x)
z instanceof BigNumber               // false
BigNumber.isBigNumber(z)             // true</pre>
    <p>
      If <code>value</code> is a BigNumber instance and <code>BigNumber.DEBUG</code> is <code>true</code>,
      then this method will also check if <code>value</code> is well-formed, and throw if it is not.
      See <a href='#Errors'>Errors</a>.
    </p>
    <p>
      The check can be useful if creating a BigNumber from an object literal.
      See <a href='#bignumber'>BigNumber</a>.
    </p>
    <pre>
x = new BigNumber(10)

// Change x.c to an illegitimate value.
x.c = NaN

BigNumber.DEBUG = false

// No error.
BigNumber.isBigNumber(x)    // true

BigNumber.DEBUG = true

// Error.
BigNumber.isBigNumber(x)    // '[BigNumber Error] Invalid BigNumber'</pre>



    <h5 id="max">maximum<code class='inset'>.max(n...) <i>&rArr; BigNumber</i></code></h5>
    <p>
      <code>n</code>: <i>number|string|BigNumber</i><br />
      <i>See <code><a href="#bignumber">BigNumber</a></code> for further parameter details.</i>
    </p>
    <p>
      Returns a BigNumber whose value is the maximum of the arguments.
    </p>
    <p>The return value is always exact and unrounded.</p>
    <pre>x = new BigNumber('3257869345.0378653')
BigNumber.maximum(4e9, x, '123456789.9')      // '4000000000'

arr = [12, '13', new BigNumber(14)]
BigNumber.max.apply(null, arr)                // '14'</pre>



    <h5 id="min">minimum<code class='inset'>.min(n...) <i>&rArr; BigNumber</i></code></h5>
    <p>
      <code>n</code>: <i>number|string|BigNumber</i><br />
      <i>See <code><a href="#bignumber">BigNumber</a></code> for further parameter details.</i>
    </p>
    <p>
      Returns a BigNumber whose value is the minimum of the arguments.
    </p>
    <p>The return value is always exact and unrounded.</p>
    <pre>x = new BigNumber('3257869345.0378653')
BigNumber.minimum(4e9, x, '123456789.9')      // '123456789.9'

arr = [2, new BigNumber(-14), '-15.9999', -12]
BigNumber.min.apply(null, arr)                // '-15.9999'</pre>



    <h5 id="random">
      random<code class='inset'>.random([dp]) <i>&rArr; BigNumber</i></code>
    </h5>
    <p><code>dp</code>: <i>number</i>: integer, <code>0</code> to <code>1e+9</code> inclusive</p>
    <p>
      Returns a new BigNumber with a pseudo-random value equal to or greater than <code>0</code> and
      less than <code>1</code>.
    </p>
    <p>
      The return value will have <code>dp</code> decimal places (or less if trailing zeros are
      produced).<br />
      If <code>dp</code> is omitted then the number of decimal places will default to the current
      <a href='#decimal-places'><code>DECIMAL_PLACES</code></a> setting.
    </p>
    <p>
      Depending on the value of this BigNumber constructor's
      <a href='#crypto'><code>CRYPTO</code></a> setting and the support for the
      <code>crypto</code> object in the host environment, the random digits of the return value are
      generated by either <code>Math.random</code> (fastest), <code>crypto.getRandomValues</code>
      (Web Cryptography API in recent browsers) or <code>crypto.randomBytes</code> (Node.js).
    </p>
    <p>
      To be able to set <a href='#crypto'><code>CRYPTO</code></a> to <code>true</code> when using
      Node.js, the <code>crypto</code> object must be available globally:
    </p>
    <pre>// Node.js
const crypto = require('crypto');   // CommonJS
import * as crypto from 'crypto';   // ES module
global.crypto = crypto;</pre>
    <p>
      If <a href='#crypto'><code>CRYPTO</code></a> is <code>true</code>, i.e. one of the
      <code>crypto</code> methods is to be used, the value of a returned BigNumber should be
      cryptographically-secure and statistically indistinguishable from a random value.
    </p>
    <p>
      Throws if <code>dp</code> is invalid. See <a href='#Errors'>Errors</a>.
    </p>
    <pre>BigNumber.config({ DECIMAL_PLACES: 10 })
BigNumber.random()              // '0.4117936847'
BigNumber.random(20)            // '0.78193327636914089009'</pre>



    <h5 id="sum">sum<code class='inset'>.sum(n...) <i>&rArr; BigNumber</i></code></h5>
    <p>
      <code>n</code>: <i>number|string|BigNumber</i><br />
      <i>See <code><a href="#bignumber">BigNumber</a></code> for further parameter details.</i>
    </p>
    <p>Returns a BigNumber whose value is the sum of the arguments.</p>
    <p>The return value is always exact and unrounded.</p>
    <pre>x = new BigNumber('3257869345.0378653')
BigNumber.sum(4e9, x, '123456789.9')      // '7381326134.9378653'

arr = [2, new BigNumber(14), '15.9999', 12]
BigNumber.sum.apply(null, arr)            // '43.9999'</pre>



    <h4 id="constructor-properties">Properties</h4>
    <p>
      The library's enumerated rounding modes are stored as properties of the constructor.<br />
      (They are not referenced internally by the library itself.)
    </p>
    <p>
      Rounding modes <code>0</code> to <code>6</code> (inclusive) are the same as those of Java's
      BigDecimal class.
    </p>
    <table>
      <tr>
        <th>Property</th>
        <th>Value</th>
        <th>Description</th>
      </tr>
      <tr>
        <td id="round-up"><b>ROUND_UP</b></td>
        <td class='centre'>0</td>
        <td>Rounds away from zero</td>
      </tr>
      <tr>
        <td id="round-down"><b>ROUND_DOWN</b></td>
        <td class='centre'>1</td>
        <td>Rounds towards zero</td>
      </tr>
      <tr>
        <td id="round-ceil"><b>ROUND_CEIL</b></td>
        <td class='centre'>2</td>
        <td>Rounds towards <code>Infinity</code></td>
      </tr>
      <tr>
        <td id="round-floor"><b>ROUND_FLOOR</b></td>
        <td class='centre'>3</td>
        <td>Rounds towards <code>-Infinity</code></td>
      </tr>
      <tr>
        <td id="round-half-up"><b>ROUND_HALF_UP</b></td>
        <td class='centre'>4</td>
        <td>
          Rounds towards nearest neighbour.<br />
          If equidistant, rounds away from zero
        </td>
      </tr>
      <tr>
        <td id="round-half-down"><b>ROUND_HALF_DOWN</b></td>
        <td class='centre'>5</td>
        <td>
          Rounds towards nearest neighbour.<br />
          If equidistant, rounds towards zero
        </td>
      </tr>
      <tr>
        <td id="round-half-even"><b>ROUND_HALF_EVEN</b></td>
        <td class='centre'>6</td>
        <td>
          Rounds towards nearest neighbour.<br />
          If equidistant, rounds towards even neighbour
        </td>
      </tr>
      <tr>
        <td id="round-half-ceil"><b>ROUND_HALF_CEIL</b></td>
        <td class='centre'>7</td>
        <td>
          Rounds towards nearest neighbour.<br />
          If equidistant, rounds towards <code>Infinity</code>
        </td>
      </tr>
      <tr>
        <td id="round-half-floor"><b>ROUND_HALF_FLOOR</b></td>
        <td class='centre'>8</td>
        <td>
          Rounds towards nearest neighbour.<br />
          If equidistant, rounds towards <code>-Infinity</code>
        </td>
      </tr>
    </table>
    <pre>
BigNumber.config({ ROUNDING_MODE: BigNumber.ROUND_CEIL })
BigNumber.config({ ROUNDING_MODE: 2 })     // equivalent</pre>

    <h5 id="debug">DEBUG</h5>
    <p><i>undefined|false|true</i></p>
    <p>
      If <code>BigNumber.DEBUG</code> is set <code>true</code> then an error will be thrown
      if this <a href='#bignumber'>BigNumber</a> constructor receives an invalid value, such as
      a value of type <em>number</em> with more than <code>15</code> significant digits.
      See <a href='#bignumber'>BigNumber</a>.
    </p>
    <p>
      An error will also be thrown if the <code><a href='#isBigNumber'>isBigNumber</a></code>
      method receives a BigNumber that is not well-formed.
      See <code><a href='#isBigNumber'>isBigNumber</a></code>.
    </p>
    <pre>BigNumber.DEBUG = true</pre>


    <h3>INSTANCE</h3>


    <h4 id="prototype-methods">Methods</h4>
    <p>The methods inherited by a BigNumber instance from its constructor's prototype object.</p>
    <p>A BigNumber is immutable in the sense that it is not changed by its methods. </p>
    <p>
      The treatment of &plusmn;<code>0</code>, &plusmn;<code>Infinity</code> and <code>NaN</code> is
      consistent with how JavaScript treats these values.
    </p>
    <p>Many method names have a shorter alias.</p>



    <h5 id="abs">absoluteValue<code class='inset'>.abs() <i>&rArr; BigNumber</i></code></h5>
    <p>
      Returns a BigNumber whose value is the absolute value, i.e. the magnitude, of the value of
      this BigNumber.
    </p>
    <p>The return value is always exact and unrounded.</p>
    <pre>
x = new BigNumber(-0.8)
y = x.absoluteValue()           // '0.8'
z = y.abs()                     // '0.8'</pre>



    <h5 id="cmp">
      comparedTo<code class='inset'>.comparedTo(n [, base]) <i>&rArr; number</i></code>
    </h5>
    <p>
      <code>n</code>: <i>number|string|BigNumber</i><br />
      <code>base</code>: <i>number</i><br />
      <i>See <a href="#bignumber">BigNumber</a> for further parameter details.</i>
    </p>
    <table>
      <tr><th>Returns</th><th>&nbsp;</th></tr>
      <tr>
        <td class='centre'><code>1</code></td>
        <td>If the value of this BigNumber is greater than the value of <code>n</code></td>
      </tr>
      <tr>
        <td class='centre'><code>-1</code></td>
        <td>If the value of this BigNumber is less than the value of <code>n</code></td>
      </tr>
      <tr>
        <td class='centre'><code>0</code></td>
        <td>If this BigNumber and <code>n</code> have the same value</td>
      </tr>
       <tr>
        <td class='centre'><code>null</code></td>
        <td>If the value of either this BigNumber or <code>n</code> is <code>NaN</code></td>
      </tr>
    </table>
    <pre>
x = new BigNumber(Infinity)
y = new BigNumber(5)
x.comparedTo(y)                 // 1
x.comparedTo(x.minus(1))        // 0
y.comparedTo(NaN)               // null
y.comparedTo('110', 2)          // -1</pre>



    <h5 id="dp">
      decimalPlaces<code class='inset'>.dp([dp [, rm]]) <i>&rArr; BigNumber|number</i></code>
    </h5>
    <p>
      <code>dp</code>: <i>number</i>: integer, <code>0</code> to <code>1e+9</code> inclusive<br />
      <code>rm</code>: <i>number</i>: integer, <code>0</code> to <code>8</code> inclusive
    </p>
    <p>
      If <code>dp</code> is a number, returns a BigNumber whose value is the value of this BigNumber
      rounded by rounding mode <code>rm</code> to a maximum of <code>dp</code> decimal places.
    </p>
    <p>
      If <code>dp</code> is omitted, or is <code>null</code> or <code>undefined</code>, the return
      value is the number of decimal places of the value of this BigNumber, or <code>null</code> if
      the value of this BigNumber is &plusmn;<code>Infinity</code> or <code>NaN</code>.
    </p>
    <p>
      If <code>rm</code> is omitted, or is <code>null</code> or <code>undefined</code>,
      <a href='#rounding-mode'><code>ROUNDING_MODE</code></a> is used.
    </p>
    <p>
      Throws if <code>dp</code> or <code>rm</code> is invalid. See <a href='#Errors'>Errors</a>.
    </p>
    <pre>
x = new BigNumber(1234.56)
x.decimalPlaces(1)                     // '1234.6'
x.dp()                                 // 2
x.decimalPlaces(2)                     // '1234.56'
x.dp(10)                               // '1234.56'
x.decimalPlaces(0, 1)                  // '1234'
x.dp(0, 6)                             // '1235'
x.decimalPlaces(1, 1)                  // '1234.5'
x.dp(1, BigNumber.ROUND_HALF_EVEN)     // '1234.6'
x                                      // '1234.56'
y = new BigNumber('9.9e-101')
y.dp()                                 // 102</pre>



    <h5 id="div">dividedBy<code class='inset'>.div(n [, base]) <i>&rArr; BigNumber</i></code>
    </h5>
    <p>
      <code>n</code>: <i>number|string|BigNumber</i><br />
      <code>base</code>: <i>number</i><br />
      <i>See <a href="#bignumber">BigNumber</a> for further parameter details.</i>
    </p>
    <p>
      Returns a BigNumber whose value is the value of this BigNumber divided by
      <code>n</code>, rounded according to the current
      <a href='#decimal-places'><code>DECIMAL_PLACES</code></a> and
      <a href='#rounding-mode'><code>ROUNDING_MODE</code></a> settings.
    </p>
    <pre>
x = new BigNumber(355)
y = new BigNumber(113)
x.dividedBy(y)                  // '3.14159292035398230088'
x.div(5)                        // '71'
x.div(47, 16)                   // '5'</pre>



    <h5 id="divInt">
      dividedToIntegerBy<code class='inset'>.idiv(n [, base]) &rArr;
      <i>BigNumber</i></code>
    </h5>
    <p>
      <code>n</code>: <i>number|string|BigNumber</i><br />
      <code>base</code>: <i>number</i><br />
      <i>See <a href="#bignumber">BigNumber</a> for further parameter details.</i>
    </p>
    <p>
      Returns a BigNumber whose value is the integer part of dividing the value of this BigNumber by
      <code>n</code>.
    </p>
    <pre>
x = new BigNumber(5)
y = new BigNumber(3)
x.dividedToIntegerBy(y)         // '1'
x.idiv(0.7)                     // '7'
x.idiv('0.f', 16)               // '5'</pre>



    <h5 id="pow">
      exponentiatedBy<code class='inset'>.pow(n [, m]) <i>&rArr; BigNumber</i></code>
    </h5>
    <p>
      <code>n</code>: <i>number|string|BigNumber</i>: integer<br />
      <code>m</code>: <i>number|string|BigNumber</i>
    </p>
    <p>
      Returns a BigNumber whose value is the value of this BigNumber exponentiated by
      <code>n</code>, i.e. raised to the power <code>n</code>, and optionally modulo a modulus
      <code>m</code>.
    </p>
    <p>
      Throws if <code>n</code> is not an integer. See <a href='#Errors'>Errors</a>.
    </p>
    <p>
      If <code>n</code> is negative the result is rounded according to the current
      <a href='#decimal-places'><code>DECIMAL_PLACES</code></a> and
      <a href='#rounding-mode'><code>ROUNDING_MODE</code></a> settings.
    </p>
    <p>
      As the number of digits of the result of the power operation can grow so large so quickly,
      e.g. 123.456<sup>10000</sup> has over <code>50000</code> digits, the number of significant
      digits calculated is limited to the value of the
      <a href='#pow-precision'><code>POW_PRECISION</code></a> setting (unless a modulus
      <code>m</code> is specified).
    </p>
    <p>
      By default <a href='#pow-precision'><code>POW_PRECISION</code></a> is set to <code>0</code>.
      This means that an unlimited number of significant digits will be calculated, and that the
      method's performance will decrease dramatically for larger exponents.
    </p>
    <p>
      If <code>m</code> is specified and the value of <code>m</code>, <code>n</code> and this
      BigNumber are integers, and <code>n</code> is positive, then a fast modular exponentiation
      algorithm is used, otherwise the operation will be performed as
      <code>x.exponentiatedBy(n).modulo(m)</code> with a
      <a href='#pow-precision'><code>POW_PRECISION</code></a> of <code>0</code>.
    </p>
    <pre>
Math.pow(0.7, 2)                // 0.48999999999999994
x = new BigNumber(0.7)
x.exponentiatedBy(2)            // '0.49'
BigNumber(3).pow(-2)            // '0.11111111111111111111'</pre>



    <h5 id="int">
      integerValue<code class='inset'>.integerValue([rm]) <i>&rArr; BigNumber</i></code>
    </h5>
    <p>
      <code>rm</code>: <i>number</i>: integer, <code>0</code> to <code>8</code> inclusive
    </p>
   <p>
      Returns a BigNumber whose value is the value of this BigNumber rounded to an integer using
      rounding mode <code>rm</code>.
    </p>
    <p>
      If <code>rm</code> is omitted, or is <code>null</code> or <code>undefined</code>,
      <a href='#rounding-mode'><code>ROUNDING_MODE</code></a> is used.
    </p>
    <p>
      Throws if <code>rm</code> is invalid. See <a href='#Errors'>Errors</a>.
    </p>
    <pre>
x = new BigNumber(123.456)
x.integerValue()                        // '123'
x.integerValue(BigNumber.ROUND_CEIL)    // '124'
y = new BigNumber(-12.7)
y.integerValue()                        // '-13'
y.integerValue(BigNumber.ROUND_DOWN)    // '-12'</pre>
    <p>
      The following is an example of how to add a prototype method that emulates JavaScript's
      <code>Math.round</code> function. <code>Math.ceil</code>, <code>Math.floor</code> and
      <code>Math.trunc</code> can be emulated in the same way with
      <code>BigNumber.ROUND_CEIL</code>, <code>BigNumber.ROUND_FLOOR</code> and
      <code> BigNumber.ROUND_DOWN</code> respectively.
    </p>
    <pre>
BigNumber.prototype.round = function () {
  return this.integerValue(BigNumber.ROUND_HALF_CEIL);
};
x.round()                               // '123'</pre>



    <h5 id="eq">isEqualTo<code class='inset'>.eq(n [, base]) <i>&rArr; boolean</i></code></h5>
    <p>
      <code>n</code>: <i>number|string|BigNumber</i><br />
      <code>base</code>: <i>number</i><br />
      <i>See <a href="#bignumber">BigNumber</a> for further parameter details.</i>
    </p>
    <p>
      Returns <code>true</code> if the value of this BigNumber is equal to the value of
      <code>n</code>, otherwise returns <code>false</code>.<br />
      As with JavaScript, <code>NaN</code> does not equal <code>NaN</code>.
    </p>
    <p>Note: This method uses the <a href='#cmp'><code>comparedTo</code></a> method internally.</p>
    <pre>
0 === 1e-324                    // true
x = new BigNumber(0)
x.isEqualTo('1e-324')           // false
BigNumber(-0).eq(x)             // true  ( -0 === 0 )
BigNumber(255).eq('ff', 16)     // true

y = new BigNumber(NaN)
y.isEqualTo(NaN)                // false</pre>



    <h5 id="isF">isFinite<code class='inset'>.isFinite() <i>&rArr; boolean</i></code></h5>
    <p>
      Returns <code>true</code> if the value of this BigNumber is a finite number, otherwise
      returns <code>false</code>.
    </p>
    <p>
      The only possible non-finite values of a BigNumber are <code>NaN</code>, <code>Infinity</code>
      and <code>-Infinity</code>.
    </p>
    <pre>
x = new BigNumber(1)
x.isFinite()                    // true
y = new BigNumber(Infinity)
y.isFinite()                    // false</pre>
    <p>
      Note: The native method <code>isFinite()</code> can be used if
      <code>n &lt;= Number.MAX_VALUE</code>.
    </p>



    <h5 id="gt">isGreaterThan<code class='inset'>.gt(n [, base]) <i>&rArr; boolean</i></code></h5>
    <p>
      <code>n</code>: <i>number|string|BigNumber</i><br />
      <code>base</code>: <i>number</i><br />
      <i>See <a href="#bignumber">BigNumber</a> for further parameter details.</i>
    </p>
    <p>
      Returns <code>true</code> if the value of this BigNumber is greater than the value of
      <code>n</code>, otherwise returns <code>false</code>.
    </p>
    <p>Note: This method uses the <a href='#cmp'><code>comparedTo</code></a> method internally.</p>
    <pre>
0.1 &gt; (0.3 - 0.2)                             // true
x = new BigNumber(0.1)
x.isGreaterThan(BigNumber(0.3).minus(0.2))    // false
BigNumber(0).gt(x)                            // false
BigNumber(11, 3).gt(11.1, 2)                  // true</pre>



    <h5 id="gte">
      isGreaterThanOrEqualTo<code class='inset'>.gte(n [, base]) <i>&rArr; boolean</i></code>
    </h5>
    <p>
      <code>n</code>: <i>number|string|BigNumber</i><br />
      <code>base</code>: <i>number</i><br />
      <i>See <a href="#bignumber">BigNumber</a> for further parameter details.</i>
    </p>
    <p>
      Returns <code>true</code> if the value of this BigNumber is greater than or equal to the value
      of <code>n</code>, otherwise returns <code>false</code>.
    </p>
    <p>Note: This method uses the <a href='#cmp'><code>comparedTo</code></a> method internally.</p>
    <pre>
(0.3 - 0.2) &gt;= 0.1                     // false
x = new BigNumber(0.3).minus(0.2)
x.isGreaterThanOrEqualTo(0.1)          // true
BigNumber(1).gte(x)                    // true
BigNumber(10, 18).gte('i', 36)         // true</pre>



    <h5 id="isInt">isInteger<code class='inset'>.isInteger() <i>&rArr; boolean</i></code></h5>
    <p>
      Returns <code>true</code> if the value of this BigNumber is an integer, otherwise returns
      <code>false</code>.
    </p>
    <pre>
x = new BigNumber(1)
x.isInteger()                   // true
y = new BigNumber(123.456)
y.isInteger()                   // false</pre>



    <h5 id="lt">isLessThan<code class='inset'>.lt(n [, base]) <i>&rArr; boolean</i></code></h5>
    <p>
      <code>n</code>: <i>number|string|BigNumber</i><br />
      <code>base</code>: <i>number</i><br />
      <i>See <a href="#bignumber">BigNumber</a> for further parameter details.</i>
    </p>
    <p>
      Returns <code>true</code> if the value of this BigNumber is less than the value of
      <code>n</code>, otherwise returns <code>false</code>.
    </p>
     <p>Note: This method uses the <a href='#cmp'><code>comparedTo</code></a> method internally.</p>
    <pre>
(0.3 - 0.2) &lt; 0.1                       // true
x = new BigNumber(0.3).minus(0.2)
x.isLessThan(0.1)                       // false
BigNumber(0).lt(x)                      // true
BigNumber(11.1, 2).lt(11, 3)            // true</pre>



    <h5 id="lte">
      isLessThanOrEqualTo<code class='inset'>.lte(n [, base]) <i>&rArr; boolean</i></code>
    </h5>
    <p>
      <code>n</code>: <i>number|string|BigNumber</i><br />
      <code>base</code>: <i>number</i><br />
      <i>See <a href="#bignumber">BigNumber</a> for further parameter details.</i>
    </p>
    <p>
      Returns <code>true</code> if the value of this BigNumber is less than or equal to the value of
      <code>n</code>, otherwise returns <code>false</code>.
    </p>
    <p>Note: This method uses the <a href='#cmp'><code>comparedTo</code></a> method internally.</p>
    <pre>
0.1 &lt;= (0.3 - 0.2)                                // false
x = new BigNumber(0.1)
x.isLessThanOrEqualTo(BigNumber(0.3).minus(0.2))  // true
BigNumber(-1).lte(x)                              // true
BigNumber(10, 18).lte('i', 36)                    // true</pre>



    <h5 id="isNaN">isNaN<code class='inset'>.isNaN() <i>&rArr; boolean</i></code></h5>
    <p>
      Returns <code>true</code> if the value of this BigNumber is <code>NaN</code>, otherwise
      returns <code>false</code>.
    </p>
    <pre>
x = new BigNumber(NaN)
x.isNaN()                       // true
y = new BigNumber('Infinity')
y.isNaN()                       // false</pre>
    <p>Note: The native method <code>isNaN()</code> can also be used.</p>



    <h5 id="isNeg">isNegative<code class='inset'>.isNegative() <i>&rArr; boolean</i></code></h5>
    <p>
      Returns <code>true</code> if the sign of this BigNumber is negative, otherwise returns
      <code>false</code>.
    </p>
    <pre>
x = new BigNumber(-0)
x.isNegative()                  // true
y = new BigNumber(2)
y.isNegative()                  // false</pre>
    <p>Note: <code>n &lt; 0</code> can be used if <code>n &lt;= -Number.MIN_VALUE</code>.</p>



    <h5 id="isPos">isPositive<code class='inset'>.isPositive() <i>&rArr; boolean</i></code></h5>
    <p>
      Returns <code>true</code> if the sign of this BigNumber is positive, otherwise returns
      <code>false</code>.
    </p>
    <pre>
x = new BigNumber(-0)
x.isPositive()                  // false
y = new BigNumber(2)
y.isPositive()                  // true</pre>



    <h5 id="isZ">isZero<code class='inset'>.isZero() <i>&rArr; boolean</i></code></h5>
    <p>
      Returns <code>true</code> if the value of this BigNumber is zero or minus zero, otherwise
      returns <code>false</code>.
    </p>
    <pre>
x = new BigNumber(-0)
x.isZero() &amp;&amp; x.isNegative()         // true
y = new BigNumber(Infinity)
y.isZero()                      // false</pre>
    <p>Note: <code>n == 0</code> can be used if <code>n &gt;= Number.MIN_VALUE</code>.</p>



    <h5 id="minus">
      minus<code class='inset'>.minus(n [, base]) <i>&rArr; BigNumber</i></code>
    </h5>
    <p>
      <code>n</code>: <i>number|string|BigNumber</i><br />
      <code>base</code>: <i>number</i><br />
      <i>See <a href="#bignumber">BigNumber</a> for further parameter details.</i>
    </p>
    <p>Returns a BigNumber whose value is the value of this BigNumber minus <code>n</code>.</p>
    <p>The return value is always exact and unrounded.</p>
    <pre>
0.3 - 0.1                       // 0.19999999999999998
x = new BigNumber(0.3)
x.minus(0.1)                    // '0.2'
x.minus(0.6, 20)                // '0'</pre>



    <h5 id="mod">modulo<code class='inset'>.mod(n [, base]) <i>&rArr; BigNumber</i></code></h5>
    <p>
      <code>n</code>: <i>number|string|BigNumber</i><br />
      <code>base</code>: <i>number</i><br />
      <i>See <a href="#bignumber">BigNumber</a> for further parameter details.</i>
    </p>
    <p>
      Returns a BigNumber whose value is the value of this BigNumber modulo <code>n</code>, i.e.
      the integer remainder of dividing this BigNumber by <code>n</code>.
    </p>
    <p>
      The value returned, and in particular its sign, is dependent on the value of the
      <a href='#modulo-mode'><code>MODULO_MODE</code></a> setting of this BigNumber constructor.
      If it is <code>1</code> (default value), the result will have the same sign as this BigNumber,
      and it will match that of Javascript's <code>%</code> operator (within the limits of double
      precision) and BigDecimal's <code>remainder</code> method.
    </p>
    <p>The return value is always exact and unrounded.</p>
    <p>
      See <a href='#modulo-mode'><code>MODULO_MODE</code></a> for a description of the other
      modulo modes.
    </p>
    <pre>
1 % 0.9                         // 0.09999999999999998
x = new BigNumber(1)
x.modulo(0.9)                   // '0.1'
y = new BigNumber(33)
y.mod('a', 33)                  // '3'</pre>



    <h5 id="times">
      multipliedBy<code class='inset'>.times(n [, base]) <i>&rArr; BigNumber</i></code>
    </h5>
    <p>
      <code>n</code>: <i>number|string|BigNumber</i><br />
      <code>base</code>: <i>number</i><br />
      <i>See <a href="#bignumber">BigNumber</a> for further parameter details.</i>
    </p>
    <p>
      Returns a BigNumber whose value is the value of this BigNumber multiplied by <code>n</code>.
    </p>
    <p>The return value is always exact and unrounded.</p>
    <pre>
0.6 * 3                         // 1.7999999999999998
x = new BigNumber(0.6)
y = x.multipliedBy(3)           // '1.8'
BigNumber('7e+500').times(y)    // '1.26e+501'
x.multipliedBy('-a', 16)        // '-6'</pre>



    <h5 id="neg">negated<code class='inset'>.negated() <i>&rArr; BigNumber</i></code></h5>
    <p>
      Returns a BigNumber whose value is the value of this BigNumber negated, i.e. multiplied by
      <code>-1</code>.
    </p>
    <pre>
x = new BigNumber(1.8)
x.negated()                     // '-1.8'
y = new BigNumber(-1.3)
y.negated()                     // '1.3'</pre>



    <h5 id="plus">plus<code class='inset'>.plus(n [, base]) <i>&rArr; BigNumber</i></code></h5>
    <p>
      <code>n</code>: <i>number|string|BigNumber</i><br />
      <code>base</code>: <i>number</i><br />
      <i>See <a href="#bignumber">BigNumber</a> for further parameter details.</i>
    </p>
    <p>Returns a BigNumber whose value is the value of this BigNumber plus <code>n</code>.</p>
    <p>The return value is always exact and unrounded.</p>
    <pre>
0.1 + 0.2                       // 0.30000000000000004
x = new BigNumber(0.1)
y = x.plus(0.2)                 // '0.3'
BigNumber(0.7).plus(x).plus(y)  // '1.1'
x.plus('0.1', 8)                // '0.225'</pre>



    <h5 id="sd">
      precision<code class='inset'>.sd([d [, rm]]) <i>&rArr; BigNumber|number</i></code>
    </h5>
    <p>
      <code>d</code>: <i>number|boolean</i>: integer, <code>1</code> to <code>1e+9</code>
      inclusive, or <code>true</code> or <code>false</code><br />
      <code>rm</code>: <i>number</i>: integer, <code>0</code> to <code>8</code> inclusive.
    </p>
    <p>
      If <code>d</code> is a number, returns a BigNumber whose value is the value of this BigNumber
      rounded to a precision of <code>d</code> significant digits using rounding mode
      <code>rm</code>.
    </p>
    <p>
      If <code>d</code> is omitted or is <code>null</code> or <code>undefined</code>, the return
      value is the number of significant digits of the value of this BigNumber, or <code>null</code>
      if the value of this BigNumber is &plusmn;<code>Infinity</code> or <code>NaN</code>.
    </p>
    <p>
      If <code>d</code> is <code>true</code> then any trailing zeros of the integer
      part of a number are counted as significant digits, otherwise they are not.
    </p>
    <p>
      If <code>rm</code> is omitted or is <code>null</code> or <code>undefined</code>,
      <a href='#rounding-mode'><code>ROUNDING_MODE</code></a> will be used.
    </p>
    <p>
      Throws if <code>d</code> or <code>rm</code> is invalid. See <a href='#Errors'>Errors</a>.
    </p>
    <pre>
x = new BigNumber(9876.54321)
x.precision(6)                         // '9876.54'
x.sd()                                 // 9
x.precision(6, BigNumber.ROUND_UP)     // '9876.55'
x.sd(2)                                // '9900'
x.precision(2, 1)                      // '9800'
x                                      // '9876.54321'
y = new BigNumber(987000)
y.precision()                          // 3
y.sd(true)                             // 6</pre>



<h5 id="shift">shiftedBy<code class='inset'>.shiftedBy(n) <i>&rArr; BigNumber</i></code></h5>
    <p>
      <code>n</code>: <i>number</i>: integer,
      <code>-9007199254740991</code> to <code>9007199254740991</code> inclusive
    </p>
    <p>
      Returns a BigNumber whose value is the value of this BigNumber shifted by <code>n</code>
      places.
    <p>
      The shift is of the decimal point, i.e. of powers of ten, and is to the left if <code>n</code>
      is negative or to the right if <code>n</code> is positive.
    </p>
    <p>The return value is always exact and unrounded.</p>
    <p>
      Throws if <code>n</code> is invalid. See <a href='#Errors'>Errors</a>.
    </p>
    <pre>
x = new BigNumber(1.23)
x.shiftedBy(3)                      // '1230'
x.shiftedBy(-3)                     // '0.00123'</pre>



    <h5 id="sqrt">squareRoot<code class='inset'>.sqrt() <i>&rArr; BigNumber</i></code></h5>
    <p>
      Returns a BigNumber whose value is the square root of the value of this BigNumber,
      rounded according to the current
      <a href='#decimal-places'><code>DECIMAL_PLACES</code></a> and
      <a href='#rounding-mode'><code>ROUNDING_MODE</code></a> settings.
    </p>
    <p>
      The return value will be correctly rounded, i.e. rounded as if the result was first calculated
      to an infinite number of correct digits before rounding.
    </p>
    <pre>
x = new BigNumber(16)
x.squareRoot()                  // '4'
y = new BigNumber(3)
y.sqrt()                        // '1.73205080756887729353'</pre>



    <h5 id="toE">
      toExponential<code class='inset'>.toExponential([dp [, rm]]) <i>&rArr; string</i></code>
    </h5>
    <p>
      <code>dp</code>: <i>number</i>: integer, <code>0</code> to <code>1e+9</code> inclusive<br />
      <code>rm</code>: <i>number</i>: integer, <code>0</code> to <code>8</code> inclusive
    </p>
    <p>
      Returns a string representing the value of this BigNumber in exponential notation rounded
      using rounding mode <code>rm</code> to <code>dp</code> decimal places, i.e with one digit
      before the decimal point and <code>dp</code> digits after it.
    </p>
    <p>
      If the value of this BigNumber in exponential notation has fewer than <code>dp</code> fraction
      digits, the return value will be appended with zeros accordingly.
    </p>
    <p>
      If <code>dp</code> is omitted, or is <code>null</code> or <code>undefined</code>, the number
      of digits after the decimal point defaults to the minimum number of digits necessary to
      represent the value exactly.<br />
      If <code>rm</code> is omitted or is <code>null</code> or <code>undefined</code>,
      <a href='#rounding-mode'><code>ROUNDING_MODE</code></a> is used.
    </p>
    <p>
      Throws if <code>dp</code> or <code>rm</code> is invalid. See <a href='#Errors'>Errors</a>.
    </p>
     <pre>
x = 45.6
y = new BigNumber(x)
x.toExponential()               // '4.56e+1'
y.toExponential()               // '4.56e+1'
x.toExponential(0)              // '5e+1'
y.toExponential(0)              // '5e+1'
x.toExponential(1)              // '4.6e+1'
y.toExponential(1)              // '4.6e+1'
y.toExponential(1, 1)           // '4.5e+1'  (ROUND_DOWN)
x.toExponential(3)              // '4.560e+1'
y.toExponential(3)              // '4.560e+1'</pre>



    <h5 id="toFix">
      toFixed<code class='inset'>.toFixed([dp [, rm]]) <i>&rArr; string</i></code>
    </h5>
    <p>
      <code>dp</code>: <i>number</i>: integer, <code>0</code> to <code>1e+9</code> inclusive<br />
      <code>rm</code>: <i>number</i>: integer, <code>0</code> to <code>8</code> inclusive
    </p>
    <p>
      Returns a string representing the value of this BigNumber in normal (fixed-point) notation
      rounded to <code>dp</code> decimal places using rounding mode <code>rm</code>.
    </p>
    <p>
      If the value of this BigNumber in normal notation has fewer than <code>dp</code> fraction
      digits, the return value will be appended with zeros accordingly.
    </p>
   <p>
      Unlike <code>Number.prototype.toFixed</code>, which returns exponential notation if a number
      is greater or equal to <code>10<sup>21</sup></code>, this method will always return normal
      notation.
    </p>
    <p>
      If <code>dp</code> is omitted or is <code>null</code> or <code>undefined</code>, the return
      value will be unrounded and in normal notation. This is also unlike
      <code>Number.prototype.toFixed</code>, which returns the value to zero decimal places.<br />
      It is useful when fixed-point notation is required and the current
      <a href="#exponential-at"><code>EXPONENTIAL_AT</code></a> setting causes
      <code><a href='#toS'>toString</a></code> to return exponential notation.<br />
      If <code>rm</code> is omitted or is <code>null</code> or <code>undefined</code>,
      <a href='#rounding-mode'><code>ROUNDING_MODE</code></a> is used.
    </p>
    <p>
      Throws if <code>dp</code> or <code>rm</code> is invalid. See <a href='#Errors'>Errors</a>.
    </p>
    <pre>
x = 3.456
y = new BigNumber(x)
x.toFixed()                     // '3'
y.toFixed()                     // '3.456'
y.toFixed(0)                    // '3'
x.toFixed(2)                    // '3.46'
y.toFixed(2)                    // '3.46'
y.toFixed(2, 1)                 // '3.45'  (ROUND_DOWN)
x.toFixed(5)                    // '3.45600'
y.toFixed(5)                    // '3.45600'</pre>



    <h5 id="toFor">
      toFormat<code class='inset'>.toFormat([dp [, rm[, format]]]) <i>&rArr; string</i></code>
    </h5>
    <p>
      <code>dp</code>: <i>number</i>: integer, <code>0</code> to <code>1e+9</code> inclusive<br />
      <code>rm</code>: <i>number</i>: integer, <code>0</code> to <code>8</code> inclusive<br />
      <code>format</code>: <i>object</i>: see <a href='#format'><code>FORMAT</code></a>
    </p>
    <p>
      <p>
      Returns a string representing the value of this BigNumber in normal (fixed-point) notation
      rounded to <code>dp</code> decimal places using rounding mode <code>rm</code>, and formatted
      according to the properties of the <code>format</code> object.
    </p>
    <p>
      See <a href='#format'><code>FORMAT</code></a> and the examples below for the properties of the
      <code>format</code> object, their types, and their usage. A formatting object may contain
      some or all of the recognised properties.
    </p>
    <p>
      If <code>dp</code> is omitted or is <code>null</code> or <code>undefined</code>, then the
      return value is not rounded to a fixed number of decimal places.<br />
      If <code>rm</code> is omitted or is <code>null</code> or <code>undefined</code>,
      <a href='#rounding-mode'><code>ROUNDING_MODE</code></a> is used.<br />
      If <code>format</code> is omitted or is <code>null</code> or <code>undefined</code>, the
      <a href='#format'><code>FORMAT</code></a> object is used.
    </p>
    <p>
      Throws if <code>dp</code>, <code>rm</code> or <code>format</code> is invalid. See
      <a href='#Errors'>Errors</a>.
    </p>
    <pre>
fmt = {
  prefix: '',
  decimalSeparator: '.',
  groupSeparator: ',',
  groupSize: 3,
  secondaryGroupSize: 0,
  fractionGroupSeparator: ' ',
  fractionGroupSize: 0,
  suffix: ''
}

x = new BigNumber('123456789.123456789')

// Set the global formatting options
BigNumber.config({ FORMAT: fmt })

x.toFormat()                              // '123,456,789.123456789'
x.toFormat(3)                             // '123,456,789.123'

// If a reference to the object assigned to FORMAT has been retained,
// the format properties can be changed directly
fmt.groupSeparator = ' '
fmt.fractionGroupSize = 5
x.toFormat()                              // '123 456 789.12345 6789'

// Alternatively, pass the formatting options as an argument
fmt = {
  prefix: '=> ',
  decimalSeparator: ',',
  groupSeparator: '.',
  groupSize: 3,
  secondaryGroupSize: 2
}

x.toFormat()                              // '123 456 789.12345 6789'
x.toFormat(fmt)                           // '=> 12.34.56.789,123456789'
x.toFormat(2, fmt)                        // '=> 12.34.56.789,12'
x.toFormat(3, BigNumber.ROUND_UP, fmt)    // '=> 12.34.56.789,124'</pre>



    <h5 id="toFr">
      toFraction<code class='inset'>.toFraction([maximum_denominator])
      <i>&rArr; [BigNumber, BigNumber]</i></code>
    </h5>
    <p>
      <code>maximum_denominator</code>:
      <i>number|string|BigNumber</i>: integer &gt;= <code>1</code> and &lt;=
      <code>Infinity</code>
    </p>
    <p>
      Returns an array of two BigNumbers representing the value of this BigNumber as a simple
      fraction with an integer numerator and an integer denominator. The denominator will be a
      positive non-zero value less than or equal to <code>maximum_denominator</code>.
    </p>
    <p>
      If a <code>maximum_denominator</code> is not specified, or is <code>null</code> or
      <code>undefined</code>, the denominator will be the lowest value necessary to represent the
      number exactly.
    </p>
    <p>
      Throws if <code>maximum_denominator</code> is invalid. See <a href='#Errors'>Errors</a>.
    </p>
    <pre>
x = new BigNumber(1.75)
x.toFraction()                  // '7, 4'

pi = new BigNumber('3.14159265358')
pi.toFraction()                 // '157079632679,50000000000'
pi.toFraction(100000)           // '312689, 99532'
pi.toFraction(10000)            // '355, 113'
pi.toFraction(100)              // '311, 99'
pi.toFraction(10)               // '22, 7'
pi.toFraction(1)                // '3, 1'</pre>



    <h5 id="toJSON">toJSON<code class='inset'>.toJSON() <i>&rArr; string</i></code></h5>
    <p>As <a href='#valueOf'><code>valueOf</code></a>.</p>
    <pre>
x = new BigNumber('177.7e+457')
y = new BigNumber(235.4325)
z = new BigNumber('0.0098074')

// Serialize an array of three BigNumbers
str = JSON.stringify( [x, y, z] )
// "["1.777e+459","235.4325","0.0098074"]"

// Return an array of three BigNumbers
JSON.parse(str, function (key, val) {
    return key === '' ? val : new BigNumber(val)
})</pre>



    <h5 id="toN">toNumber<code class='inset'>.toNumber() <i>&rArr; number</i></code></h5>
    <p>Returns the value of this BigNumber as a JavaScript number primitive.</p>
    <p>
      This method is identical to using type coercion with the unary plus operator.
    </p>
    <pre>
x = new BigNumber(456.789)
x.toNumber()                    // 456.789
+x                              // 456.789

y = new BigNumber('45987349857634085409857349856430985')
y.toNumber()                    // 4.598734985763409e+34

z = new BigNumber(-0)
1 / z.toNumber()                // -Infinity
1 / +z                          // -Infinity</pre>



    <h5 id="toP">
      toPrecision<code class='inset'>.toPrecision([sd [, rm]]) <i>&rArr; string</i></code>
    </h5>
    <p>
      <code>sd</code>: <i>number</i>: integer, <code>1</code> to <code>1e+9</code> inclusive<br />
      <code>rm</code>: <i>number</i>: integer, <code>0</code> to <code>8</code> inclusive
    </p>
    <p>
      Returns a string representing the value of this BigNumber rounded to <code>sd</code>
      significant digits using rounding mode <code>rm</code>.
    </p>
    <p>
      If <code>sd</code> is less than the number of digits necessary to represent the integer part
      of the value in normal (fixed-point) notation, then exponential notation is used.
    </p>
    <p>
      If <code>sd</code> is omitted, or is <code>null</code> or <code>undefined</code>, then the
      return value is the same as <code>n.toString()</code>.<br />
      If <code>rm</code> is omitted or is <code>null</code> or <code>undefined</code>,
      <a href='#rounding-mode'><code>ROUNDING_MODE</code></a> is used.
    </p>
    <p>
      Throws if <code>sd</code> or <code>rm</code> is invalid. See <a href='#Errors'>Errors</a>.
    </p>
     <pre>
x = 45.6
y = new BigNumber(x)
x.toPrecision()                 // '45.6'
y.toPrecision()                 // '45.6'
x.toPrecision(1)                // '5e+1'
y.toPrecision(1)                // '5e+1'
y.toPrecision(2, 0)             // '4.6e+1'  (ROUND_UP)
y.toPrecision(2, 1)             // '4.5e+1'  (ROUND_DOWN)
x.toPrecision(5)                // '45.600'
y.toPrecision(5)                // '45.600'</pre>



    <h5 id="toS">toString<code class='inset'>.toString([base]) <i>&rArr; string</i></code></h5>
    <p>
      <code>base</code>: <i>number</i>: integer, <code>2</code> to <code>ALPHABET.length</code>
      inclusive (see <a href='#alphabet'><code>ALPHABET</code></a>).
    </p>
    <p>
      Returns a string representing the value of this BigNumber in the specified base, or base
      <code>10</code> if <code>base</code> is omitted or is <code>null</code> or
      <code>undefined</code>.
    </p>
    <p>
      For bases above <code>10</code>, and using the default base conversion alphabet
      (see <a href='#alphabet'><code>ALPHABET</code></a>), values from <code>10</code> to
      <code>35</code> are represented by <code>a-z</code>
      (as with <code>Number.prototype.toString</code>).
    </p>
    <p>
      If a base is specified the value is rounded according to the current
      <a href='#decimal-places'><code>DECIMAL_PLACES</code></a>
      and <a href='#rounding-mode'><code>ROUNDING_MODE</code></a> settings.
    </p>
    <p>
      If a base is not specified, and this BigNumber has a positive
      exponent that is equal to or greater than the positive component of the
      current <a href="#exponential-at"><code>EXPONENTIAL_AT</code></a> setting,
      or a negative exponent equal to or less than the negative component of the
      setting, then exponential notation is returned.
    </p>
    <p>If <code>base</code> is <code>null</code> or <code>undefined</code> it is ignored.</p>
    <p>
      Throws if <code>base</code> is invalid. See <a href='#Errors'>Errors</a>.
    </p>
    <pre>
x = new BigNumber(750000)
x.toString()                    // '750000'
BigNumber.config({ EXPONENTIAL_AT: 5 })
x.toString()                    // '7.5e+5'

y = new BigNumber(362.875)
y.toString(2)                   // '101101010.111'
y.toString(9)                   // '442.77777777777777777778'
y.toString(32)                  // 'ba.s'

BigNumber.config({ DECIMAL_PLACES: 4 });
z = new BigNumber('1.23456789')
z.toString()                    // '1.23456789'
z.toString(10)                  // '1.2346'</pre>



    <h5 id="valueOf">valueOf<code class='inset'>.valueOf() <i>&rArr; string</i></code></h5>
    <p>
      As <a href='#toS'><code>toString</code></a>, but does not accept a base argument and includes
      the minus sign for negative zero.
    </p>
    <pre>
x = new BigNumber('-0')
x.toString()                    // '0'
x.valueOf()                     // '-0'
y = new BigNumber('1.777e+457')
y.valueOf()                     // '1.777e+457'</pre>



    <h4 id="instance-properties">Properties</h4>
    <p>The properties of a BigNumber instance:</p>
    <table>
      <tr>
        <th>Property</th>
        <th>Description</th>
        <th>Type</th>
        <th>Value</th>
      </tr>
      <tr>
        <td class='centre' id='coefficient'><b>c</b></td>
        <td>coefficient<sup>*</sup></td>
        <td><i>number</i><code>[]</code></td>
        <td> Array of base <code>1e14</code> numbers</td>
      </tr>
      <tr>
        <td class='centre' id='exponent'><b>e</b></td>
        <td>exponent</td>
        <td><i>number</i></td>
        <td>Integer, <code>-1000000000</code> to <code>1000000000</code> inclusive</td>
      </tr>
      <tr>
        <td class='centre' id='sign'><b>s</b></td>
        <td>sign</td>
        <td><i>number</i></td>
        <td><code>-1</code> or <code>1</code></td>
      </tr>
    </table>
    <p><sup>*</sup>significand</p>
    <p>
      The value of any of the <code>c</code>, <code>e</code> and <code>s</code> properties may also
      be <code>null</code>.
    </p>
    <p>
      The above properties are best considered to be read-only. In early versions of this library it
      was okay to change the exponent of a BigNumber by writing to its exponent property directly,
      but this is no longer reliable as the value of the first element of the coefficient array is
      now dependent on the exponent.
    </p>
    <p>
      Note that, as with JavaScript numbers, the original exponent and fractional trailing zeros are
      not necessarily preserved.
    </p>
    <pre>x = new BigNumber(0.123)              // '0.123'
x.toExponential()                     // '1.23e-1'
x.c                                   // '1,2,3'
x.e                                   // -1
x.s                                   // 1

y = new Number(-123.4567000e+2)       // '-12345.67'
y.toExponential()                     // '-1.234567e+4'
z = new BigNumber('-123.4567000e+2')  // '-12345.67'
z.toExponential()                     // '-1.234567e+4'
z.c                                   // '1,2,3,4,5,6,7'
z.e                                   // 4
z.s                                   // -1</pre>



    <h4 id="zero-nan-infinity">Zero, NaN and Infinity</h4>
    <p>
      The table below shows how &plusmn;<code>0</code>, <code>NaN</code> and
      &plusmn;<code>Infinity</code> are stored.
    </p>
    <table>
      <tr>
        <th> </th>
        <th class='centre'>c</th>
        <th class='centre'>e</th>
        <th class='centre'>s</th>
      </tr>
      <tr>
        <td>&plusmn;0</td>
        <td><code>[0]</code></td>
        <td><code>0</code></td>
        <td><code>&plusmn;1</code></td>
      </tr>
      <tr>
        <td>NaN</td>
        <td><code>null</code></td>
        <td><code>null</code></td>
        <td><code>null</code></td>
      </tr>
      <tr>
        <td>&plusmn;Infinity</td>
        <td><code>null</code></td>
        <td><code>null</code></td>
        <td><code>&plusmn;1</code></td>
      </tr>
    </table>
    <pre>
x = new Number(-0)              // 0
1 / x == -Infinity              // true

y = new BigNumber(-0)           // '0'
y.c                             // '0' ( [0].toString() )
y.e                             // 0
y.s                             // -1</pre>



    <h4 id='Errors'>Errors</h4>
    <p>The table below shows the errors that are thrown.</p>
    <p>
      The errors are generic <code>Error</code> objects whose message begins
      <code>'[BigNumber Error]'</code>.
    </p>
    <table class='error-table'>
      <tr>
        <th>Method</th>
        <th>Throws</th>
      </tr>
      <tr>
        <td rowspan=6>
          <code>BigNumber</code><br />
          <code>comparedTo</code><br />
          <code>dividedBy</code><br />
          <code>dividedToIntegerBy</code><br />
          <code>isEqualTo</code><br />
          <code>isGreaterThan</code><br />
          <code>isGreaterThanOrEqualTo</code><br />
          <code>isLessThan</code><br />
          <code>isLessThanOrEqualTo</code><br />
          <code>minus</code><br />
          <code>modulo</code><br />
          <code>plus</code><br />
          <code>multipliedBy</code>
        </td>
        <td>Base not a primitive number</td>
      </tr>
      <tr>
        <td>Base not an integer</td>
      </tr>
      <tr>
        <td>Base out of range</td>
      </tr>
       <tr>
        <td>Number primitive has more than 15 significant digits<sup>*</sup></td>
      </tr>
      <tr>
        <td>Not a base... number<sup>*</sup></td>
      </tr>
      <tr>
        <td>Not a number<sup>*</sup></td>
      </tr>
      <tr>
        <td><code>clone</code></td>
        <td>Object expected</td>
      </tr>
      <tr>
        <td rowspan=24><code>config</code></td>
        <td>Object expected</td>
      </tr>
      <tr>
        <td><code>DECIMAL_PLACES</code> not a primitive number</td>
      </tr>
      <tr>
        <td><code>DECIMAL_PLACES</code> not an integer</td>
      </tr>
      <tr>
        <td><code>DECIMAL_PLACES</code> out of range</td>
      </tr>
      <tr>
        <td><code>ROUNDING_MODE</code> not a primitive number</td>
      </tr>
      <tr>
        <td><code>ROUNDING_MODE</code> not an integer</td>
      </tr>
      <tr>
        <td><code>ROUNDING_MODE</code> out of range</td>
      </tr>
      <tr>
        <td><code>EXPONENTIAL_AT</code> not a primitive number</td>
      </tr>
      <tr>
        <td><code>EXPONENTIAL_AT</code> not an integer</td>
      </tr>
      <tr>
        <td><code>EXPONENTIAL_AT</code> out of range</td>
      </tr>
      <tr>
        <td><code>RANGE</code> not a primitive number</td>
      </tr>
      <tr>
        <td><code>RANGE</code> not an integer</td>
      </tr>
      <tr>
        <td><code>RANGE</code> cannot be zero</td>
      </tr>
      <tr>
        <td><code>RANGE</code> cannot be zero</td>
      </tr>
      <tr>
        <td><code>CRYPTO</code> not true or false</td>
      </tr>
      <tr>
        <td><code>crypto</code> unavailable</td>
      </tr>
      <tr>
        <td><code>MODULO_MODE</code> not a primitive number</td>
      </tr>
      <tr>
        <td><code>MODULO_MODE</code> not an integer</td>
      </tr>
      <tr>
        <td><code>MODULO_MODE</code> out of range</td>
      </tr>
      <tr>
        <td><code>POW_PRECISION</code> not a primitive number</td>
      </tr>
      <tr>
        <td><code>POW_PRECISION</code> not an integer</td>
      </tr>
      <tr>
        <td><code>POW_PRECISION</code> out of range</td>
      </tr>
      <tr>
        <td><code>FORMAT</code> not an object</td>
      </tr>
      <tr>
        <td><code>ALPHABET</code> invalid</td>
      </tr>
      <tr>
        <td rowspan=3>
          <code>decimalPlaces</code><br />
          <code>precision</code><br />
          <code>random</code><br />
          <code>shiftedBy</code><br />
          <code>toExponential</code><br />
          <code>toFixed</code><br />
          <code>toFormat</code><br />
          <code>toPrecision</code>
        </td>
        <td>Argument not a primitive number</td>
      </tr>
      <tr>
        <td>Argument not an integer</td>
      </tr>
      <tr>
        <td>Argument out of range</td>
      </tr>
      <tr>
        <td>
          <code>decimalPlaces</code><br />
          <code>precision</code>
        </td>
        <td>Argument not true or false</td>
      </tr>
      <tr>
        <td><code>exponentiatedBy</code></td>
        <td>Argument not an integer</td>
      </tr>
      <tr>
        <td><code>isBigNumber</code></td>
        <td>Invalid BigNumber<sup>*</sup></td>
      </tr>
      <tr>
        <td>
          <code>minimum</code><br />
          <code>maximum</code>
        </td>
        <td>Not a number<sup>*</sup></td>
      </tr>
      <tr>
        <td>
          <code>random</code>
        </td>
        <td>crypto unavailable</td>
      </tr>
      <tr>
        <td>
          <code>toFormat</code>
        </td>
        <td>Argument not an object</td>
      </tr>
      <tr>
        <td rowspan=2><code>toFraction</code></td>
        <td>Argument not an integer</td>
      </tr>
      <tr>
        <td>Argument out of range</td>
      </tr>
      <tr>
        <td rowspan=3><code>toString</code></td>
        <td>Base not a primitive number</td>
      </tr>
      <tr>
        <td>Base not an integer</td>
      </tr>
      <tr>
        <td>Base out of range</td>
      </tr>
    </table>
    <p><sup>*</sup>Only thrown if <code>BigNumber.DEBUG</code> is <code>true</code>.</p>
    <p>To determine if an exception is a BigNumber Error:</p>
    <pre>
try {
  // ...
} catch (e) {
  if (e instanceof Error &amp;&amp; e.message.indexOf('[BigNumber Error]') === 0) {
      // ...
  }
}</pre>



    <h4 id="type-coercion">Type coercion</h4>
    <p>
      To prevent the accidental use of a BigNumber in primitive number operations, or the
      accidental addition of a BigNumber to a string, the <code>valueOf</code> method can be safely
      overwritten as shown below.
    </p>
    <p>
      The <a href='#valueOf'><code>valueOf</code></a> method is the same as the
      <a href='#toJSON'><code>toJSON</code></a> method, and both are the same as the
      <a href='#toS'><code>toString</code></a> method except they do not take a <code>base</code>
      argument and they include the minus sign for negative zero.
    </p>
    <pre>
BigNumber.prototype.valueOf = function () {
  throw Error('valueOf called!')
}

x = new BigNumber(1)
x / 2                    // '[BigNumber Error] valueOf called!'
x + 'abc'                // '[BigNumber Error] valueOf called!'
</pre>



    <h4 id='faq'>FAQ</h4>

    <h6>Why are trailing fractional zeros removed from BigNumbers?</h6>
    <p>
      Some arbitrary-precision libraries retain trailing fractional zeros as they can indicate the
      precision of a value. This can be useful but the results of arithmetic operations can be
      misleading.
    </p>
    <pre>
x = new BigDecimal("1.0")
y = new BigDecimal("1.1000")
z = x.add(y)                      // 2.1000

x = new BigDecimal("1.20")
y = new BigDecimal("3.45000")
z = x.multiply(y)                 // 4.1400000</pre>
    <p>
      To specify the precision of a value is to specify that the value lies
      within a certain range.
    </p>
    <p>
      In the first example, <code>x</code> has a value of <code>1.0</code>. The trailing zero shows
      the precision of the value, implying that it is in the range <code>0.95</code> to
      <code>1.05</code>. Similarly, the precision indicated by the trailing zeros of <code>y</code>
      indicates that the value is in the range <code>1.09995</code> to <code>1.10005</code>.
    </p>
    <p>
      If we  add the two lowest values in the ranges we have, <code>0.95 + 1.09995 = 2.04995</code>,
      and if we add the two highest values we have, <code>1.05 + 1.10005 = 2.15005</code>, so the
      range of the result of the addition implied by the precision of its operands is
      <code>2.04995</code> to <code>2.15005</code>.
    </p>
    <p>
      The result given by BigDecimal of <code>2.1000</code> however, indicates that the value is in
      the range <code>2.09995</code> to <code>2.10005</code> and therefore the precision implied by
      its trailing zeros may be misleading.
    </p>
    <p>
      In the second example, the true range is <code>4.122744</code> to <code>4.157256</code> yet
      the BigDecimal answer of <code>4.1400000</code> indicates a range of <code>4.13999995</code>
      to  <code>4.14000005</code>. Again, the precision implied by the trailing zeros may be
      misleading.
    </p>
    <p>
      This library, like binary floating point and most calculators, does not retain trailing
      fractional zeros. Instead, the <code>toExponential</code>, <code>toFixed</code> and
      <code>toPrecision</code> methods enable trailing zeros to be added if and when required.<br />
    </p>
  </div>

</body>
</html>
