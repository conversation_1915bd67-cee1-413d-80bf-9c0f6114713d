{"ast": null, "code": "export { default } from \"./typography.js\";\nexport * from \"./typography.js\";", "map": {"version": 3, "names": ["default"], "sources": ["C:/Users/<USER>/OneDrive/Documents/AuthPages/Evolune/medcure/node_modules/@mui/system/esm/typography/index.js"], "sourcesContent": ["export { default } from \"./typography.js\";\nexport * from \"./typography.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,iBAAiB;AACzC,cAAc,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}