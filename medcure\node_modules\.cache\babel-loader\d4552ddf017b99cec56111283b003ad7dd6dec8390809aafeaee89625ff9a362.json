{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\AuthPages\\\\Evolune\\\\medcure\\\\src\\\\Footer.js\";\nimport React from 'react';\nimport './Footer.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Footer = () => {\n  return /*#__PURE__*/_jsxDEV(\"footer\", {\n    className: \"footer\",\n    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"footer-links\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        children: \"\\xA9 2025 MedCode AI\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"separator\",\n        children: \"|\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"footer-link\",\n        onClick: () => {},\n        children: \"Privacy Policy\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 10,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"separator\",\n        children: \"|\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 11,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"footer-link\",\n        onClick: () => {},\n        children: \"Terms of Service\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 12,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"separator\",\n        children: \"|\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 13,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"footer-link\",\n        onClick: () => {},\n        children: \"Contact\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 14,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: \"MedCode AI - Where AI Meets Healthcare, Smarter Insights, Better Outcomes!\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n};\n_c = Footer;\nexport default Footer;\nvar _c;\n$RefreshReg$(_c, \"Footer\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Footer", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Documents/AuthPages/Evolune/medcure/src/Footer.js"], "sourcesContent": ["import React from 'react'\r\nimport './Footer.css';\r\n\r\nconst Footer = () => {\r\n  return (\r\n    <footer className=\"footer\">\r\n        <p className=\"footer-links\">\r\n          <span>© 2025 MedCode AI</span>\r\n          <span className=\"separator\">|</span>\r\n          <button className=\"footer-link\" onClick={() => {}}>Privacy Policy</button>\r\n          <span className=\"separator\">|</span>\r\n          <button className=\"footer-link\" onClick={() => {}}>Terms of Service</button>\r\n          <span className=\"separator\">|</span>\r\n          <button className=\"footer-link\" onClick={() => {}}>Contact</button>\r\n        </p>\r\n        <p>MedCode AI - Where AI Meets Healthcare, Smarter Insights, Better Outcomes!</p>\r\n      </footer>\r\n  )\r\n}\r\n\r\nexport default Footer"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtB,MAAMC,MAAM,GAAGA,CAAA,KAAM;EACnB,oBACED,OAAA;IAAQE,SAAS,EAAC,QAAQ;IAAAC,QAAA,gBACtBH,OAAA;MAAGE,SAAS,EAAC,cAAc;MAAAC,QAAA,gBACzBH,OAAA;QAAAG,QAAA,EAAM;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC9BP,OAAA;QAAME,SAAS,EAAC,WAAW;QAAAC,QAAA,EAAC;MAAC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACpCP,OAAA;QAAQE,SAAS,EAAC,aAAa;QAACM,OAAO,EAAEA,CAAA,KAAM,CAAC,CAAE;QAAAL,QAAA,EAAC;MAAc;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAC1EP,OAAA;QAAME,SAAS,EAAC,WAAW;QAAAC,QAAA,EAAC;MAAC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACpCP,OAAA;QAAQE,SAAS,EAAC,aAAa;QAACM,OAAO,EAAEA,CAAA,KAAM,CAAC,CAAE;QAAAL,QAAA,EAAC;MAAgB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAC5EP,OAAA;QAAME,SAAS,EAAC,WAAW;QAAAC,QAAA,EAAC;MAAC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACpCP,OAAA;QAAQE,SAAS,EAAC,aAAa;QAACM,OAAO,EAAEA,CAAA,KAAM,CAAC,CAAE;QAAAL,QAAA,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClE,CAAC,eACJP,OAAA;MAAAG,QAAA,EAAG;IAA0E;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC3E,CAAC;AAEf,CAAC;AAAAE,EAAA,GAfKR,MAAM;AAiBZ,eAAeA,MAAM;AAAA,IAAAQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}