{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\AuthPages\\\\Evolune\\\\medcure\\\\src\\\\components\\\\auth\\\\SignupForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { TextField, Button, Typography, Box, Alert, Divider, Link } from '@mui/material';\nimport { useAuth } from '../../context/AuthContext';\nimport GoogleSignIn from './GoogleSignIn';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SignupForm = () => {\n  _s();\n  const [name, setName] = useState('');\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n  const [confirmPassword, setConfirmPassword] = useState('');\n  const [error, setError] = useState('');\n  const [loading, setLoading] = useState(false);\n  const {\n    signup,\n    googleLogin,\n    switchAuthMode\n  } = useAuth();\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (password !== confirmPassword) {\n      setError('Passwords do not match');\n      return;\n    }\n    if (password.length < 6) {\n      setError('Password must be at least 6 characters');\n      return;\n    }\n    setLoading(true);\n    setError('');\n    const result = await signup(name, email, password);\n    if (!result.success) {\n      setError(result.message);\n    }\n    setLoading(false);\n  };\n  const handleGoogleSuccess = async response => {\n    setLoading(true);\n    setError('');\n    const result = await googleLogin(response.credential);\n    if (!result.success) {\n      setError(result.message);\n    }\n    setLoading(false);\n  };\n  const handleGoogleError = () => {\n    setError('Google sign-in failed');\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      component: \"h1\",\n      variant: \"h4\",\n      align: \"center\",\n      gutterBottom: true,\n      children: \"Sign Up\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 69,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      sx: {\n        mb: 2\n      },\n      children: String(error)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(GoogleSignIn, {\n      onSuccess: handleGoogleSuccess,\n      onError: handleGoogleError\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Divider, {\n      sx: {\n        my: 2\n      },\n      children: \"OR\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      component: \"form\",\n      onSubmit: handleSubmit,\n      children: [/*#__PURE__*/_jsxDEV(TextField, {\n        margin: \"normal\",\n        required: true,\n        fullWidth: true,\n        id: \"name\",\n        label: \"Full Name\",\n        name: \"name\",\n        autoComplete: \"name\",\n        autoFocus: true,\n        value: name,\n        onChange: e => setName(e.target.value)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TextField, {\n        margin: \"normal\",\n        required: true,\n        fullWidth: true,\n        id: \"email\",\n        label: \"Email Address\",\n        name: \"email\",\n        autoComplete: \"email\",\n        value: email,\n        onChange: e => setEmail(e.target.value)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TextField, {\n        margin: \"normal\",\n        required: true,\n        fullWidth: true,\n        name: \"password\",\n        label: \"Password\",\n        type: \"password\",\n        id: \"password\",\n        autoComplete: \"new-password\",\n        value: password,\n        onChange: e => setPassword(e.target.value)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 106,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TextField, {\n        margin: \"normal\",\n        required: true,\n        fullWidth: true,\n        name: \"confirmPassword\",\n        label: \"Confirm Password\",\n        type: \"password\",\n        id: \"confirmPassword\",\n        value: confirmPassword,\n        onChange: e => setConfirmPassword(e.target.value)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"submit\",\n        fullWidth: true,\n        variant: \"contained\",\n        sx: {\n          mt: 3,\n          mb: 2\n        },\n        disabled: loading,\n        children: loading ? 'Creating Account...' : 'Sign Up'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        textAlign: \"center\",\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          component: \"button\",\n          variant: \"body2\",\n          onClick: e => {\n            e.preventDefault();\n            switchAuthMode('login');\n          },\n          children: \"Already have an account? Sign In\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 68,\n    columnNumber: 5\n  }, this);\n};\n_s(SignupForm, \"c6SJ8CfjhZS6cMxFTWSaPn3SO6A=\", false, function () {\n  return [useAuth];\n});\n_c = SignupForm;\nexport default SignupForm;\nvar _c;\n$RefreshReg$(_c, \"SignupForm\");", "map": {"version": 3, "names": ["React", "useState", "TextField", "<PERSON><PERSON>", "Typography", "Box", "<PERSON><PERSON>", "Divider", "Link", "useAuth", "GoogleSignIn", "jsxDEV", "_jsxDEV", "SignupForm", "_s", "name", "setName", "email", "setEmail", "password", "setPassword", "confirmPassword", "setConfirmPassword", "error", "setError", "loading", "setLoading", "signup", "googleLogin", "switchAuthMode", "handleSubmit", "e", "preventDefault", "length", "result", "success", "message", "handleGoogleSuccess", "response", "credential", "handleGoogleError", "children", "component", "variant", "align", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "severity", "sx", "mb", "String", "onSuccess", "onError", "my", "onSubmit", "margin", "required", "fullWidth", "id", "label", "autoComplete", "autoFocus", "value", "onChange", "target", "type", "mt", "disabled", "textAlign", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Documents/AuthPages/Evolune/medcure/src/components/auth/SignupForm.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  TextField,\n  Button,\n  Typography,\n  Box,\n  Alert,\n  Divider,\n  Link,\n} from '@mui/material';\n\nimport { useAuth } from '../../context/AuthContext';\nimport GoogleSignIn from './GoogleSignIn';\n\nconst SignupForm = () => {\n  const [name, setName] = useState('');\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n  const [confirmPassword, setConfirmPassword] = useState('');\n  const [error, setError] = useState('');\n  const [loading, setLoading] = useState(false);\n  \n  const { signup, googleLogin, switchAuthMode } = useAuth();\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    if (password !== confirmPassword) {\n      setError('Passwords do not match');\n      return;\n    }\n    \n    if (password.length < 6) {\n      setError('Password must be at least 6 characters');\n      return;\n    }\n\n    setLoading(true);\n    setError('');\n\n    const result = await signup(name, email, password);\n    \n    if (!result.success) {\n      setError(result.message);\n    }\n    \n    setLoading(false);\n  };\n\n  const handleGoogleSuccess = async (response) => {\n    setLoading(true);\n    setError('');\n\n    const result = await googleLogin(response.credential);\n    \n    if (!result.success) {\n      setError(result.message);\n    }\n    \n    setLoading(false);\n  };\n\n  const handleGoogleError = () => {\n    setError('Google sign-in failed');\n  };\n\n  return (\n    <Box>\n      <Typography component=\"h1\" variant=\"h4\" align=\"center\" gutterBottom>\n        Sign Up\n      </Typography>\n      \n      {error && <Alert severity=\"error\" sx={{ mb: 2 }}>{String(error)}</Alert>}\n      \n      <GoogleSignIn \n        onSuccess={handleGoogleSuccess}\n        onError={handleGoogleError}\n      />\n      \n      <Divider sx={{ my: 2 }}>OR</Divider>\n      \n      <Box component=\"form\" onSubmit={handleSubmit}>\n        <TextField\n          margin=\"normal\"\n          required\n          fullWidth\n          id=\"name\"\n          label=\"Full Name\"\n          name=\"name\"\n          autoComplete=\"name\"\n          autoFocus\n          value={name}\n          onChange={(e) => setName(e.target.value)}\n        />\n        <TextField\n          margin=\"normal\"\n          required\n          fullWidth\n          id=\"email\"\n          label=\"Email Address\"\n          name=\"email\"\n          autoComplete=\"email\"\n          value={email}\n          onChange={(e) => setEmail(e.target.value)}\n        />\n        <TextField\n          margin=\"normal\"\n          required\n          fullWidth\n          name=\"password\"\n          label=\"Password\"\n          type=\"password\"\n          id=\"password\"\n          autoComplete=\"new-password\"\n          value={password}\n          onChange={(e) => setPassword(e.target.value)}\n        />\n        <TextField\n          margin=\"normal\"\n          required\n          fullWidth\n          name=\"confirmPassword\"\n          label=\"Confirm Password\"\n          type=\"password\"\n          id=\"confirmPassword\"\n          value={confirmPassword}\n          onChange={(e) => setConfirmPassword(e.target.value)}\n        />\n        <Button\n          type=\"submit\"\n          fullWidth\n          variant=\"contained\"\n          sx={{ mt: 3, mb: 2 }}\n          disabled={loading}\n        >\n          {loading ? 'Creating Account...' : 'Sign Up'}\n        </Button>\n        <Box textAlign=\"center\">\n          <Link\n            component=\"button\"\n            variant=\"body2\"\n            onClick={(e) => {\n              e.preventDefault();\n              switchAuthMode('login');\n            }}\n          >\n            Already have an account? Sign In\n          </Link>\n        </Box>\n      </Box>\n    </Box>\n  );\n};\n\nexport default SignupForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,SAAS,EACTC,MAAM,EACNC,UAAU,EACVC,GAAG,EACHC,KAAK,EACLC,OAAO,EACPC,IAAI,QACC,eAAe;AAEtB,SAASC,OAAO,QAAQ,2BAA2B;AACnD,OAAOC,YAAY,MAAM,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1C,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvB,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACgB,KAAK,EAAEC,QAAQ,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACkB,QAAQ,EAAEC,WAAW,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACoB,eAAe,EAAEC,kBAAkB,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACsB,KAAK,EAAEC,QAAQ,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACwB,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EAE7C,MAAM;IAAE0B,MAAM;IAAEC,WAAW;IAAEC;EAAe,CAAC,GAAGpB,OAAO,CAAC,CAAC;EAEzD,MAAMqB,YAAY,GAAG,MAAOC,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAElB,IAAIb,QAAQ,KAAKE,eAAe,EAAE;MAChCG,QAAQ,CAAC,wBAAwB,CAAC;MAClC;IACF;IAEA,IAAIL,QAAQ,CAACc,MAAM,GAAG,CAAC,EAAE;MACvBT,QAAQ,CAAC,wCAAwC,CAAC;MAClD;IACF;IAEAE,UAAU,CAAC,IAAI,CAAC;IAChBF,QAAQ,CAAC,EAAE,CAAC;IAEZ,MAAMU,MAAM,GAAG,MAAMP,MAAM,CAACZ,IAAI,EAAEE,KAAK,EAAEE,QAAQ,CAAC;IAElD,IAAI,CAACe,MAAM,CAACC,OAAO,EAAE;MACnBX,QAAQ,CAACU,MAAM,CAACE,OAAO,CAAC;IAC1B;IAEAV,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAED,MAAMW,mBAAmB,GAAG,MAAOC,QAAQ,IAAK;IAC9CZ,UAAU,CAAC,IAAI,CAAC;IAChBF,QAAQ,CAAC,EAAE,CAAC;IAEZ,MAAMU,MAAM,GAAG,MAAMN,WAAW,CAACU,QAAQ,CAACC,UAAU,CAAC;IAErD,IAAI,CAACL,MAAM,CAACC,OAAO,EAAE;MACnBX,QAAQ,CAACU,MAAM,CAACE,OAAO,CAAC;IAC1B;IAEAV,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAED,MAAMc,iBAAiB,GAAGA,CAAA,KAAM;IAC9BhB,QAAQ,CAAC,uBAAuB,CAAC;EACnC,CAAC;EAED,oBACEZ,OAAA,CAACP,GAAG;IAAAoC,QAAA,gBACF7B,OAAA,CAACR,UAAU;MAACsC,SAAS,EAAC,IAAI;MAACC,OAAO,EAAC,IAAI;MAACC,KAAK,EAAC,QAAQ;MAACC,YAAY;MAAAJ,QAAA,EAAC;IAEpE;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,EAEZ1B,KAAK,iBAAIX,OAAA,CAACN,KAAK;MAAC4C,QAAQ,EAAC,OAAO;MAACC,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAX,QAAA,EAAEY,MAAM,CAAC9B,KAAK;IAAC;MAAAuB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eAExErC,OAAA,CAACF,YAAY;MACX4C,SAAS,EAAEjB,mBAAoB;MAC/BkB,OAAO,EAAEf;IAAkB;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5B,CAAC,eAEFrC,OAAA,CAACL,OAAO;MAAC4C,EAAE,EAAE;QAAEK,EAAE,EAAE;MAAE,CAAE;MAAAf,QAAA,EAAC;IAAE;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAS,CAAC,eAEpCrC,OAAA,CAACP,GAAG;MAACqC,SAAS,EAAC,MAAM;MAACe,QAAQ,EAAE3B,YAAa;MAAAW,QAAA,gBAC3C7B,OAAA,CAACV,SAAS;QACRwD,MAAM,EAAC,QAAQ;QACfC,QAAQ;QACRC,SAAS;QACTC,EAAE,EAAC,MAAM;QACTC,KAAK,EAAC,WAAW;QACjB/C,IAAI,EAAC,MAAM;QACXgD,YAAY,EAAC,MAAM;QACnBC,SAAS;QACTC,KAAK,EAAElD,IAAK;QACZmD,QAAQ,EAAGnC,CAAC,IAAKf,OAAO,CAACe,CAAC,CAACoC,MAAM,CAACF,KAAK;MAAE;QAAAnB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C,CAAC,eACFrC,OAAA,CAACV,SAAS;QACRwD,MAAM,EAAC,QAAQ;QACfC,QAAQ;QACRC,SAAS;QACTC,EAAE,EAAC,OAAO;QACVC,KAAK,EAAC,eAAe;QACrB/C,IAAI,EAAC,OAAO;QACZgD,YAAY,EAAC,OAAO;QACpBE,KAAK,EAAEhD,KAAM;QACbiD,QAAQ,EAAGnC,CAAC,IAAKb,QAAQ,CAACa,CAAC,CAACoC,MAAM,CAACF,KAAK;MAAE;QAAAnB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C,CAAC,eACFrC,OAAA,CAACV,SAAS;QACRwD,MAAM,EAAC,QAAQ;QACfC,QAAQ;QACRC,SAAS;QACT7C,IAAI,EAAC,UAAU;QACf+C,KAAK,EAAC,UAAU;QAChBM,IAAI,EAAC,UAAU;QACfP,EAAE,EAAC,UAAU;QACbE,YAAY,EAAC,cAAc;QAC3BE,KAAK,EAAE9C,QAAS;QAChB+C,QAAQ,EAAGnC,CAAC,IAAKX,WAAW,CAACW,CAAC,CAACoC,MAAM,CAACF,KAAK;MAAE;QAAAnB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9C,CAAC,eACFrC,OAAA,CAACV,SAAS;QACRwD,MAAM,EAAC,QAAQ;QACfC,QAAQ;QACRC,SAAS;QACT7C,IAAI,EAAC,iBAAiB;QACtB+C,KAAK,EAAC,kBAAkB;QACxBM,IAAI,EAAC,UAAU;QACfP,EAAE,EAAC,iBAAiB;QACpBI,KAAK,EAAE5C,eAAgB;QACvB6C,QAAQ,EAAGnC,CAAC,IAAKT,kBAAkB,CAACS,CAAC,CAACoC,MAAM,CAACF,KAAK;MAAE;QAAAnB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrD,CAAC,eACFrC,OAAA,CAACT,MAAM;QACLiE,IAAI,EAAC,QAAQ;QACbR,SAAS;QACTjB,OAAO,EAAC,WAAW;QACnBQ,EAAE,EAAE;UAAEkB,EAAE,EAAE,CAAC;UAAEjB,EAAE,EAAE;QAAE,CAAE;QACrBkB,QAAQ,EAAE7C,OAAQ;QAAAgB,QAAA,EAEjBhB,OAAO,GAAG,qBAAqB,GAAG;MAAS;QAAAqB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC,eACTrC,OAAA,CAACP,GAAG;QAACkE,SAAS,EAAC,QAAQ;QAAA9B,QAAA,eACrB7B,OAAA,CAACJ,IAAI;UACHkC,SAAS,EAAC,QAAQ;UAClBC,OAAO,EAAC,OAAO;UACf6B,OAAO,EAAGzC,CAAC,IAAK;YACdA,CAAC,CAACC,cAAc,CAAC,CAAC;YAClBH,cAAc,CAAC,OAAO,CAAC;UACzB,CAAE;UAAAY,QAAA,EACH;QAED;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACnC,EAAA,CA1IID,UAAU;EAAA,QAQkCJ,OAAO;AAAA;AAAAgE,EAAA,GARnD5D,UAAU;AA4IhB,eAAeA,UAAU;AAAC,IAAA4D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}