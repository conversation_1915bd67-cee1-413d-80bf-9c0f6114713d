[{"C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\medcure\\src\\index.js": "1", "C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\medcure\\src\\App.js": "2", "C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\medcure\\src\\LeftScreen.js": "3", "C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\medcure\\src\\QueryHistory.js": "4", "C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\medcure\\src\\RightScreen.js": "5", "C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\medcure\\src\\Settings.js": "6", "C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\medcure\\src\\MedcodeBanner.js": "7", "C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\medcure\\src\\components\\AuthWrapper.js": "8", "C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\medcure\\src\\RoleSelector.js": "9", "C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\medcure\\src\\Footer.js": "10", "C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\medcure\\src\\WelcomeCard.js": "11", "C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\medcure\\src\\ActivityDashboard.js": "12", "C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\medcure\\src\\MedicalQuery.js": "13", "C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\medcure\\src\\services\\api.js": "14", "C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\medcure\\src\\context\\AuthContext.js": "15", "C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\medcure\\src\\components\\auth\\AuthModal.js": "16", "C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\medcure\\src\\components\\auth\\SignupForm.js": "17", "C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\medcure\\src\\components\\auth\\LoginForm.js": "18", "C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\medcure\\src\\components\\auth\\GoogleSignIn.js": "19"}, {"size": 347, "mtime": 1751380634507, "results": "20", "hashOfConfig": "21"}, {"size": 2188, "mtime": 1751719105155, "results": "22", "hashOfConfig": "21"}, {"size": 2643, "mtime": 1751719469281, "results": "23", "hashOfConfig": "21"}, {"size": 3504, "mtime": 1751720186083, "results": "24", "hashOfConfig": "21"}, {"size": 568, "mtime": 1751380634784, "results": "25", "hashOfConfig": "21"}, {"size": 5774, "mtime": 1751380634897, "results": "26", "hashOfConfig": "21"}, {"size": 994, "mtime": 1751380634618, "results": "27", "hashOfConfig": "21"}, {"size": 2691, "mtime": 1751717648531, "results": "28", "hashOfConfig": "21"}, {"size": 1421, "mtime": 1751380634838, "results": "29", "hashOfConfig": "21"}, {"size": 753, "mtime": 1751380634453, "results": "30", "hashOfConfig": "21"}, {"size": 715, "mtime": 1751380634982, "results": "31", "hashOfConfig": "21"}, {"size": 1071, "mtime": 1751380634309, "results": "32", "hashOfConfig": "21"}, {"size": 7494, "mtime": 1751719814158, "results": "33", "hashOfConfig": "21"}, {"size": 9429, "mtime": 1751719828922, "results": "34", "hashOfConfig": "21"}, {"size": 3366, "mtime": 1751719020984, "results": "35", "hashOfConfig": "21"}, {"size": 1243, "mtime": 1751719034005, "results": "36", "hashOfConfig": "21"}, {"size": 3699, "mtime": 1751720216507, "results": "37", "hashOfConfig": "21"}, {"size": 2775, "mtime": 1751720203560, "results": "38", "hashOfConfig": "21"}, {"size": 696, "mtime": 1751719077393, "results": "39", "hashOfConfig": "21"}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "t6mqzj", {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\medcure\\src\\index.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\medcure\\src\\App.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\medcure\\src\\LeftScreen.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\medcure\\src\\QueryHistory.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\medcure\\src\\RightScreen.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\medcure\\src\\Settings.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\medcure\\src\\MedcodeBanner.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\medcure\\src\\components\\AuthWrapper.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\medcure\\src\\RoleSelector.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\medcure\\src\\Footer.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\medcure\\src\\WelcomeCard.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\medcure\\src\\ActivityDashboard.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\medcure\\src\\MedicalQuery.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\medcure\\src\\services\\api.js", ["97"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\medcure\\src\\context\\AuthContext.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\medcure\\src\\components\\auth\\AuthModal.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\medcure\\src\\components\\auth\\SignupForm.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\medcure\\src\\components\\auth\\LoginForm.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\medcure\\src\\components\\auth\\GoogleSignIn.js", [], [], {"ruleId": "98", "severity": 1, "message": "99", "line": 317, "column": 1, "nodeType": "100", "endLine": 317, "endColumn": 58}, "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration"]