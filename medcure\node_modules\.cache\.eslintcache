[{"C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\medcure\\src\\index.js": "1", "C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\medcure\\src\\App.js": "2", "C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\medcure\\src\\LeftScreen.js": "3", "C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\medcure\\src\\QueryHistory.js": "4", "C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\medcure\\src\\RightScreen.js": "5", "C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\medcure\\src\\Settings.js": "6", "C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\medcure\\src\\MedcodeBanner.js": "7", "C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\medcure\\src\\components\\AuthWrapper.js": "8", "C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\medcure\\src\\RoleSelector.js": "9", "C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\medcure\\src\\Footer.js": "10", "C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\medcure\\src\\WelcomeCard.js": "11", "C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\medcure\\src\\ActivityDashboard.js": "12", "C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\medcure\\src\\MedicalQuery.js": "13", "C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\medcure\\src\\services\\api.js": "14"}, {"size": 347, "mtime": 1751380634507, "results": "15", "hashOfConfig": "16"}, {"size": 2111, "mtime": 1751716737798, "results": "17", "hashOfConfig": "16"}, {"size": 2246, "mtime": 1751474745893, "results": "18", "hashOfConfig": "16"}, {"size": 3519, "mtime": 1751716688511, "results": "19", "hashOfConfig": "16"}, {"size": 568, "mtime": 1751380634784, "results": "20", "hashOfConfig": "16"}, {"size": 5774, "mtime": 1751380634897, "results": "21", "hashOfConfig": "16"}, {"size": 994, "mtime": 1751380634618, "results": "22", "hashOfConfig": "16"}, {"size": 2691, "mtime": 1751717648531, "results": "23", "hashOfConfig": "16"}, {"size": 1421, "mtime": 1751380634838, "results": "24", "hashOfConfig": "16"}, {"size": 753, "mtime": 1751380634453, "results": "25", "hashOfConfig": "16"}, {"size": 715, "mtime": 1751380634982, "results": "26", "hashOfConfig": "16"}, {"size": 1071, "mtime": 1751380634309, "results": "27", "hashOfConfig": "16"}, {"size": 7482, "mtime": 1751716637885, "results": "28", "hashOfConfig": "16"}, {"size": 6611, "mtime": 1751717634641, "results": "29", "hashOfConfig": "16"}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "t6mqzj", {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\medcure\\src\\index.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\medcure\\src\\App.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\medcure\\src\\LeftScreen.js", ["72", "73"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\medcure\\src\\QueryHistory.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\medcure\\src\\RightScreen.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\medcure\\src\\Settings.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\medcure\\src\\MedcodeBanner.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\medcure\\src\\components\\AuthWrapper.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\medcure\\src\\RoleSelector.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\medcure\\src\\Footer.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\medcure\\src\\WelcomeCard.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\medcure\\src\\ActivityDashboard.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\medcure\\src\\MedicalQuery.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\medcure\\src\\services\\api.js", ["74"], [], {"ruleId": "75", "severity": 1, "message": "76", "line": 2, "column": 17, "nodeType": "77", "messageId": "78", "endLine": 2, "endColumn": 25}, {"ruleId": "75", "severity": 1, "message": "79", "line": 4, "column": 8, "nodeType": "77", "messageId": "78", "endLine": 4, "endColumn": 16}, {"ruleId": "80", "severity": 1, "message": "81", "line": 243, "column": 1, "nodeType": "82", "endLine": 243, "endColumn": 58}, "no-unused-vars", "'useState' is defined but never used.", "Identifier", "unusedVar", "'MenuIcon' is defined but never used.", "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration"]