.query-history-container {
  padding: 24px;
  font-family: 'Segoe UI', sans-serif;
}

.empty-message {
  background-color: #e8f0fa;
  padding: 20px;
  border-radius: 10px;
  text-align: center;
  font-size: 16px;
  color: #1a73e8;
}

.empty-message a {
  color: #1a73e8;
  text-decoration: none;
  font-weight: 500;
}

.loading-message {
  color: #007bff;
  text-align: center;
  padding: 40px;
  font-size: 18px;
}

.error-message {
  color: #dc3545;
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
  text-align: center;
}

.query-card {
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
  overflow: hidden;
  border: 1px solid #eee;
}

.query-header {
  background-color: #f9f9f9;
  padding: 12px 20px;
  font-weight: 600;
  border-bottom: 1px solid #ddd;
}

.query-body {
  padding: 20px;
}

.query-body p {
  margin: 8px 0;
  font-size: 15px;
  color: #333;
}

.rerun-btn {
  background: linear-gradient(135deg, #0052cc, #2d8cf0);
  color: white;
  padding: 10px 20px;
  margin-top: 15px;
  border: none;
  border-radius: 999px;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.3s ease;
}

.rerun-btn:hover {
  background: linear-gradient(135deg, #0041a3, #1b6edc);
}
