{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\AuthPages\\\\Evolune\\\\medcure\\\\src\\\\WelcomeCard.js\";\nimport React from 'react';\nimport './WelcomeCard.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst WelcomeCard = ({\n  name\n}) => {\n  const initials = name.split(' ').map(part => part[0]).join('').toUpperCase();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"welcome-card\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"welcome-left\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"user-avatar\",\n        children: initials\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 14,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"welcome-text\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: [\"Welcome back, \", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"highlight-name\",\n            children: [name, \"!\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 16,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 16,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"How can we assist you with your medical inquiries today?\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 15,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"welcome-right\",\n      children: [\"\\uD83D\\uDC4B Logged in as \", initials]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 12,\n    columnNumber: 5\n  }, this);\n};\n_c = WelcomeCard;\nexport default WelcomeCard;\nvar _c;\n$RefreshReg$(_c, \"WelcomeCard\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "WelcomeCard", "name", "initials", "split", "map", "part", "join", "toUpperCase", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Documents/AuthPages/Evolune/medcure/src/WelcomeCard.js"], "sourcesContent": ["import React from 'react';\r\nimport './WelcomeCard.css';\r\n\r\nconst WelcomeCard = ({ name }) => {\r\n  const initials = name\r\n    .split(' ')\r\n    .map(part => part[0])\r\n    .join('')\r\n    .toUpperCase();\r\n\r\n  return (\r\n    <div className=\"welcome-card\">\r\n      <div className=\"welcome-left\">\r\n        <div className=\"user-avatar\">{initials}</div>\r\n        <div className=\"welcome-text\">\r\n          <h2>Welcome back, <span className=\"highlight-name\">{name}!</span></h2>\r\n          <p>How can we assist you with your medical inquiries today?</p>\r\n        </div>\r\n      </div>\r\n      <div className=\"welcome-right\">\r\n        👋 Logged in as {initials}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default WelcomeCard;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAMC,WAAW,GAAGA,CAAC;EAAEC;AAAK,CAAC,KAAK;EAChC,MAAMC,QAAQ,GAAGD,IAAI,CAClBE,KAAK,CAAC,GAAG,CAAC,CACVC,GAAG,CAACC,IAAI,IAAIA,IAAI,CAAC,CAAC,CAAC,CAAC,CACpBC,IAAI,CAAC,EAAE,CAAC,CACRC,WAAW,CAAC,CAAC;EAEhB,oBACER,OAAA;IAAKS,SAAS,EAAC,cAAc;IAAAC,QAAA,gBAC3BV,OAAA;MAAKS,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC3BV,OAAA;QAAKS,SAAS,EAAC,aAAa;QAAAC,QAAA,EAAEP;MAAQ;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC7Cd,OAAA;QAAKS,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BV,OAAA;UAAAU,QAAA,GAAI,gBAAc,eAAAV,OAAA;YAAMS,SAAS,EAAC,gBAAgB;YAAAC,QAAA,GAAER,IAAI,EAAC,GAAC;UAAA;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtEd,OAAA;UAAAU,QAAA,EAAG;QAAwD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5D,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACNd,OAAA;MAAKS,SAAS,EAAC,eAAe;MAAAC,QAAA,GAAC,4BACb,EAACP,QAAQ;IAAA;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACC,EAAA,GArBId,WAAW;AAuBjB,eAAeA,WAAW;AAAC,IAAAc,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}