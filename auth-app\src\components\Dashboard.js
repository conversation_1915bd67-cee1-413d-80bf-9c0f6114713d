import React from 'react';
import {
  Container,
  Paper,
  Typography,
  Button,
  Box,
  Card,
  CardContent,
} from '@mui/material';
import { useAuth } from '../context/AuthContext';

function Dashboard() {
  const { user, logout } = useAuth();

  const handleLogout = () => {
    logout();
  };

  return (
    <Container maxWidth="md">
      <Box sx={{ mt: 4 }}>
        <Paper elevation={3} sx={{ p: 4 }}>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
            <Typography variant="h4" component="h1">
              Dashboard
            </Typography>
            <Button variant="outlined" color="secondary" onClick={handleLogout}>
              Logout
            </Button>
          </Box>
          
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Welcome back!
              </Typography>
              <Typography variant="body1" paragraph>
                <strong>Name:</strong> {user?.name}
              </Typography>
              <Typography variant="body1" paragraph>
                <strong>Email:</strong> {user?.email}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                You have successfully logged in to your account.
              </Typography>
            </CardContent>
          </Card>
        </Paper>
      </Box>
    </Container>
  );
}

export default Dashboard;