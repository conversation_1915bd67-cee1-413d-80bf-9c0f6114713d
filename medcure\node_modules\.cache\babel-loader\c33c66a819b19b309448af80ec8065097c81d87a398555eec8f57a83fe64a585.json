{"ast": null, "code": "import axios from 'axios';\n\n// API Configuration\nconst AUTH_API_BASE_URL = 'http://localhost:8000'; // auth-backend-fastapi\nconst MEDICAL_API_BASE_URL = 'http://localhost:8001'; // medcure-backend\n\n// Create axios instances for different backends\nconst authAPI = axios.create({\n  baseURL: AUTH_API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\nconst medicalAPI = axios.create({\n  baseURL: MEDICAL_API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Add request interceptors to include auth token\nconst addAuthInterceptor = apiInstance => {\n  apiInstance.interceptors.request.use(config => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  }, error => {\n    return Promise.reject(error);\n  });\n};\n\n// Add auth interceptors to both API instances\naddAuthInterceptor(authAPI);\naddAuthInterceptor(medicalAPI);\n\n// Authentication API calls (auth-backend-fastapi)\nexport const authService = {\n  // User registration\n  register: async userData => {\n    try {\n      const response = await authAPI.post('/register', userData);\n      return response.data;\n    } catch (error) {\n      var _error$response;\n      throw ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.data) || error.message;\n    }\n  },\n  // User login\n  login: async credentials => {\n    try {\n      const response = await authAPI.post('/login', credentials);\n      if (response.data.access_token) {\n        localStorage.setItem('token', response.data.access_token);\n        localStorage.setItem('user', JSON.stringify(response.data.user));\n      }\n      return response.data;\n    } catch (error) {\n      var _error$response2;\n      throw ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.data) || error.message;\n    }\n  },\n  // Google OAuth login\n  googleLogin: async tokenData => {\n    try {\n      const response = await authAPI.post('/google-login', tokenData);\n      if (response.data.access_token) {\n        localStorage.setItem('token', response.data.access_token);\n        localStorage.setItem('user', JSON.stringify(response.data.user));\n      }\n      return response.data;\n    } catch (error) {\n      var _error$response3;\n      throw ((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : _error$response3.data) || error.message;\n    }\n  },\n  // Get current user profile\n  getProfile: async () => {\n    try {\n      const response = await authAPI.get('/profile');\n      return response.data;\n    } catch (error) {\n      var _error$response4;\n      throw ((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : _error$response4.data) || error.message;\n    }\n  },\n  // Update user profile\n  updateProfile: async profileData => {\n    try {\n      const response = await authAPI.put('/profile', profileData);\n      return response.data;\n    } catch (error) {\n      var _error$response5;\n      throw ((_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : _error$response5.data) || error.message;\n    }\n  },\n  // Logout\n  logout: () => {\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n  },\n  // Check if user is authenticated\n  isAuthenticated: () => {\n    return !!localStorage.getItem('token');\n  },\n  // Get current user from localStorage\n  getCurrentUser: () => {\n    const user = localStorage.getItem('user');\n    return user ? JSON.parse(user) : null;\n  }\n};\n\n// Medical AI API calls (medcure-backend)\nexport const medicalService = {\n  // Submit medical query (text only)\n  submitQuery: async queryData => {\n    try {\n      const response = await medicalAPI.post('/query', queryData);\n      return response.data;\n    } catch (error) {\n      var _error$response6;\n      throw ((_error$response6 = error.response) === null || _error$response6 === void 0 ? void 0 : _error$response6.data) || error.message;\n    }\n  },\n  // Submit medical query with file\n  submitQueryWithFile: async formData => {\n    try {\n      const response = await medicalAPI.post('/query-with-file', formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data'\n        }\n      });\n      return response.data;\n    } catch (error) {\n      var _error$response7;\n      throw ((_error$response7 = error.response) === null || _error$response7 === void 0 ? void 0 : _error$response7.data) || error.message;\n    }\n  },\n  // Get user query history\n  getQueryHistory: async (userId, limit = 20) => {\n    try {\n      const response = await medicalAPI.get(`/history/${userId}?limit=${limit}`);\n      return response.data;\n    } catch (error) {\n      var _error$response8;\n      throw ((_error$response8 = error.response) === null || _error$response8 === void 0 ? void 0 : _error$response8.data) || error.message;\n    }\n  },\n  // Get user statistics\n  getUserStats: async userId => {\n    try {\n      const response = await medicalAPI.get(`/stats/${userId}`);\n      return response.data;\n    } catch (error) {\n      var _error$response9;\n      throw ((_error$response9 = error.response) === null || _error$response9 === void 0 ? void 0 : _error$response9.data) || error.message;\n    }\n  },\n  // Fetch PubMed articles\n  fetchPubMedArticles: async (query, numArticles = 5) => {\n    try {\n      const response = await medicalAPI.post('/pubmed', {\n        query,\n        num_articles: numArticles\n      });\n      return response.data;\n    } catch (error) {\n      var _error$response0;\n      throw ((_error$response0 = error.response) === null || _error$response0 === void 0 ? void 0 : _error$response0.data) || error.message;\n    }\n  },\n  // Generate PDF report\n  generatePDF: async pdfData => {\n    try {\n      const response = await medicalAPI.post('/generate-pdf', pdfData, {\n        headers: {\n          'Content-Type': 'multipart/form-data'\n        },\n        responseType: 'blob' // Important for PDF download\n      });\n      return response.data;\n    } catch (error) {\n      var _error$response1;\n      throw ((_error$response1 = error.response) === null || _error$response1 === void 0 ? void 0 : _error$response1.data) || error.message;\n    }\n  },\n  // Test AI functionality\n  testAI: async (query = \"What is diabetes?\", language = \"English\") => {\n    try {\n      const response = await medicalAPI.post(`/test-ai?query=${encodeURIComponent(query)}&language=${language}`);\n      return response.data;\n    } catch (error) {\n      var _error$response10;\n      throw ((_error$response10 = error.response) === null || _error$response10 === void 0 ? void 0 : _error$response10.data) || error.message;\n    }\n  },\n  // Health check\n  healthCheck: async () => {\n    try {\n      const response = await medicalAPI.get('/health');\n      return response.data;\n    } catch (error) {\n      var _error$response11;\n      throw ((_error$response11 = error.response) === null || _error$response11 === void 0 ? void 0 : _error$response11.data) || error.message;\n    }\n  }\n};\n\n// Utility functions\nexport const apiUtils = {\n  // Handle API errors consistently\n  handleError: error => {\n    if (error.response) {\n      var _error$response$data, _error$response$data2;\n      // Server responded with error status\n      return ((_error$response$data = error.response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || ((_error$response$data2 = error.response.data) === null || _error$response$data2 === void 0 ? void 0 : _error$response$data2.message) || 'Server error occurred';\n    } else if (error.request) {\n      // Request was made but no response received\n      return 'Network error - please check your connection';\n    } else {\n      // Something else happened\n      return error.message || 'An unexpected error occurred';\n    }\n  },\n  // Format error messages for display\n  formatErrorMessage: error => {\n    if (typeof error === 'string') {\n      return error;\n    }\n    if (Array.isArray(error.detail)) {\n      return error.detail.map(err => err.msg).join(', ');\n    }\n    return error.detail || error.message || 'An error occurred';\n  }\n};\nexport default {\n  authService,\n  medicalService,\n  apiUtils\n};", "map": {"version": 3, "names": ["axios", "AUTH_API_BASE_URL", "MEDICAL_API_BASE_URL", "authAPI", "create", "baseURL", "headers", "medicalAPI", "addAuthInterceptor", "apiInstance", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "authService", "register", "userData", "response", "post", "data", "_error$response", "message", "login", "credentials", "access_token", "setItem", "JSON", "stringify", "user", "_error$response2", "googleLogin", "tokenData", "_error$response3", "getProfile", "get", "_error$response4", "updateProfile", "profileData", "put", "_error$response5", "logout", "removeItem", "isAuthenticated", "getCurrentUser", "parse", "medicalService", "submitQuery", "queryData", "_error$response6", "submitQueryWithFile", "formData", "_error$response7", "getQueryHistory", "userId", "limit", "_error$response8", "getUserStats", "_error$response9", "fetchPubMedArticles", "query", "numArticles", "num_articles", "_error$response0", "generatePDF", "pdfData", "responseType", "_error$response1", "testAI", "language", "encodeURIComponent", "_error$response10", "healthCheck", "_error$response11", "apiUtils", "handleError", "_error$response$data", "_error$response$data2", "detail", "formatErrorMessage", "Array", "isArray", "map", "err", "msg", "join"], "sources": ["C:/Users/<USER>/OneDrive/Documents/AuthPages/Evolune/medcure/src/services/api.js"], "sourcesContent": ["import axios from 'axios';\n\n// API Configuration\nconst AUTH_API_BASE_URL = 'http://localhost:8000'; // auth-backend-fastapi\nconst MEDICAL_API_BASE_URL = 'http://localhost:8001'; // medcure-backend\n\n// Create axios instances for different backends\nconst authAPI = axios.create({\n  baseURL: AUTH_API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\nconst medicalAPI = axios.create({\n  baseURL: MEDICAL_API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Add request interceptors to include auth token\nconst addAuthInterceptor = (apiInstance) => {\n  apiInstance.interceptors.request.use(\n    (config) => {\n      const token = localStorage.getItem('token');\n      if (token) {\n        config.headers.Authorization = `Bearer ${token}`;\n      }\n      return config;\n    },\n    (error) => {\n      return Promise.reject(error);\n    }\n  );\n};\n\n// Add auth interceptors to both API instances\naddAuthInterceptor(authAPI);\naddAuthInterceptor(medicalAPI);\n\n// Authentication API calls (auth-backend-fastapi)\nexport const authService = {\n  // User registration\n  register: async (userData) => {\n    try {\n      const response = await authAPI.post('/register', userData);\n      return response.data;\n    } catch (error) {\n      throw error.response?.data || error.message;\n    }\n  },\n\n  // User login\n  login: async (credentials) => {\n    try {\n      const response = await authAPI.post('/login', credentials);\n      if (response.data.access_token) {\n        localStorage.setItem('token', response.data.access_token);\n        localStorage.setItem('user', JSON.stringify(response.data.user));\n      }\n      return response.data;\n    } catch (error) {\n      throw error.response?.data || error.message;\n    }\n  },\n\n  // Google OAuth login\n  googleLogin: async (tokenData) => {\n    try {\n      const response = await authAPI.post('/google-login', tokenData);\n      if (response.data.access_token) {\n        localStorage.setItem('token', response.data.access_token);\n        localStorage.setItem('user', JSON.stringify(response.data.user));\n      }\n      return response.data;\n    } catch (error) {\n      throw error.response?.data || error.message;\n    }\n  },\n\n  // Get current user profile\n  getProfile: async () => {\n    try {\n      const response = await authAPI.get('/profile');\n      return response.data;\n    } catch (error) {\n      throw error.response?.data || error.message;\n    }\n  },\n\n  // Update user profile\n  updateProfile: async (profileData) => {\n    try {\n      const response = await authAPI.put('/profile', profileData);\n      return response.data;\n    } catch (error) {\n      throw error.response?.data || error.message;\n    }\n  },\n\n  // Logout\n  logout: () => {\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n  },\n\n  // Check if user is authenticated\n  isAuthenticated: () => {\n    return !!localStorage.getItem('token');\n  },\n\n  // Get current user from localStorage\n  getCurrentUser: () => {\n    const user = localStorage.getItem('user');\n    return user ? JSON.parse(user) : null;\n  }\n};\n\n// Medical AI API calls (medcure-backend)\nexport const medicalService = {\n  // Submit medical query (text only)\n  submitQuery: async (queryData) => {\n    try {\n      const response = await medicalAPI.post('/query', queryData);\n      return response.data;\n    } catch (error) {\n      throw error.response?.data || error.message;\n    }\n  },\n\n  // Submit medical query with file\n  submitQueryWithFile: async (formData) => {\n    try {\n      const response = await medicalAPI.post('/query-with-file', formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data',\n        },\n      });\n      return response.data;\n    } catch (error) {\n      throw error.response?.data || error.message;\n    }\n  },\n\n  // Get user query history\n  getQueryHistory: async (userId, limit = 20) => {\n    try {\n      const response = await medicalAPI.get(`/history/${userId}?limit=${limit}`);\n      return response.data;\n    } catch (error) {\n      throw error.response?.data || error.message;\n    }\n  },\n\n  // Get user statistics\n  getUserStats: async (userId) => {\n    try {\n      const response = await medicalAPI.get(`/stats/${userId}`);\n      return response.data;\n    } catch (error) {\n      throw error.response?.data || error.message;\n    }\n  },\n\n  // Fetch PubMed articles\n  fetchPubMedArticles: async (query, numArticles = 5) => {\n    try {\n      const response = await medicalAPI.post('/pubmed', {\n        query,\n        num_articles: numArticles\n      });\n      return response.data;\n    } catch (error) {\n      throw error.response?.data || error.message;\n    }\n  },\n\n  // Generate PDF report\n  generatePDF: async (pdfData) => {\n    try {\n      const response = await medicalAPI.post('/generate-pdf', pdfData, {\n        headers: {\n          'Content-Type': 'multipart/form-data',\n        },\n        responseType: 'blob', // Important for PDF download\n      });\n      return response.data;\n    } catch (error) {\n      throw error.response?.data || error.message;\n    }\n  },\n\n  // Test AI functionality\n  testAI: async (query = \"What is diabetes?\", language = \"English\") => {\n    try {\n      const response = await medicalAPI.post(`/test-ai?query=${encodeURIComponent(query)}&language=${language}`);\n      return response.data;\n    } catch (error) {\n      throw error.response?.data || error.message;\n    }\n  },\n\n  // Health check\n  healthCheck: async () => {\n    try {\n      const response = await medicalAPI.get('/health');\n      return response.data;\n    } catch (error) {\n      throw error.response?.data || error.message;\n    }\n  }\n};\n\n// Utility functions\nexport const apiUtils = {\n  // Handle API errors consistently\n  handleError: (error) => {\n    if (error.response) {\n      // Server responded with error status\n      return error.response.data?.detail || error.response.data?.message || 'Server error occurred';\n    } else if (error.request) {\n      // Request was made but no response received\n      return 'Network error - please check your connection';\n    } else {\n      // Something else happened\n      return error.message || 'An unexpected error occurred';\n    }\n  },\n\n  // Format error messages for display\n  formatErrorMessage: (error) => {\n    if (typeof error === 'string') {\n      return error;\n    }\n    if (Array.isArray(error.detail)) {\n      return error.detail.map(err => err.msg).join(', ');\n    }\n    return error.detail || error.message || 'An error occurred';\n  }\n};\n\nexport default { authService, medicalService, apiUtils };\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;;AAEzB;AACA,MAAMC,iBAAiB,GAAG,uBAAuB,CAAC,CAAC;AACnD,MAAMC,oBAAoB,GAAG,uBAAuB,CAAC,CAAC;;AAEtD;AACA,MAAMC,OAAO,GAAGH,KAAK,CAACI,MAAM,CAAC;EAC3BC,OAAO,EAAEJ,iBAAiB;EAC1BK,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;AAEF,MAAMC,UAAU,GAAGP,KAAK,CAACI,MAAM,CAAC;EAC9BC,OAAO,EAAEH,oBAAoB;EAC7BI,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACA,MAAME,kBAAkB,GAAIC,WAAW,IAAK;EAC1CA,WAAW,CAACC,YAAY,CAACC,OAAO,CAACC,GAAG,CACjCC,MAAM,IAAK;IACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAIF,KAAK,EAAE;MACTD,MAAM,CAACP,OAAO,CAACW,aAAa,GAAG,UAAUH,KAAK,EAAE;IAClD;IACA,OAAOD,MAAM;EACf,CAAC,EACAK,KAAK,IAAK;IACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;EAC9B,CACF,CAAC;AACH,CAAC;;AAED;AACAV,kBAAkB,CAACL,OAAO,CAAC;AAC3BK,kBAAkB,CAACD,UAAU,CAAC;;AAE9B;AACA,OAAO,MAAMc,WAAW,GAAG;EACzB;EACAC,QAAQ,EAAE,MAAOC,QAAQ,IAAK;IAC5B,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMrB,OAAO,CAACsB,IAAI,CAAC,WAAW,EAAEF,QAAQ,CAAC;MAC1D,OAAOC,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOR,KAAK,EAAE;MAAA,IAAAS,eAAA;MACd,MAAM,EAAAA,eAAA,GAAAT,KAAK,CAACM,QAAQ,cAAAG,eAAA,uBAAdA,eAAA,CAAgBD,IAAI,KAAIR,KAAK,CAACU,OAAO;IAC7C;EACF,CAAC;EAED;EACAC,KAAK,EAAE,MAAOC,WAAW,IAAK;IAC5B,IAAI;MACF,MAAMN,QAAQ,GAAG,MAAMrB,OAAO,CAACsB,IAAI,CAAC,QAAQ,EAAEK,WAAW,CAAC;MAC1D,IAAIN,QAAQ,CAACE,IAAI,CAACK,YAAY,EAAE;QAC9BhB,YAAY,CAACiB,OAAO,CAAC,OAAO,EAAER,QAAQ,CAACE,IAAI,CAACK,YAAY,CAAC;QACzDhB,YAAY,CAACiB,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACV,QAAQ,CAACE,IAAI,CAACS,IAAI,CAAC,CAAC;MAClE;MACA,OAAOX,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOR,KAAK,EAAE;MAAA,IAAAkB,gBAAA;MACd,MAAM,EAAAA,gBAAA,GAAAlB,KAAK,CAACM,QAAQ,cAAAY,gBAAA,uBAAdA,gBAAA,CAAgBV,IAAI,KAAIR,KAAK,CAACU,OAAO;IAC7C;EACF,CAAC;EAED;EACAS,WAAW,EAAE,MAAOC,SAAS,IAAK;IAChC,IAAI;MACF,MAAMd,QAAQ,GAAG,MAAMrB,OAAO,CAACsB,IAAI,CAAC,eAAe,EAAEa,SAAS,CAAC;MAC/D,IAAId,QAAQ,CAACE,IAAI,CAACK,YAAY,EAAE;QAC9BhB,YAAY,CAACiB,OAAO,CAAC,OAAO,EAAER,QAAQ,CAACE,IAAI,CAACK,YAAY,CAAC;QACzDhB,YAAY,CAACiB,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACV,QAAQ,CAACE,IAAI,CAACS,IAAI,CAAC,CAAC;MAClE;MACA,OAAOX,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOR,KAAK,EAAE;MAAA,IAAAqB,gBAAA;MACd,MAAM,EAAAA,gBAAA,GAAArB,KAAK,CAACM,QAAQ,cAAAe,gBAAA,uBAAdA,gBAAA,CAAgBb,IAAI,KAAIR,KAAK,CAACU,OAAO;IAC7C;EACF,CAAC;EAED;EACAY,UAAU,EAAE,MAAAA,CAAA,KAAY;IACtB,IAAI;MACF,MAAMhB,QAAQ,GAAG,MAAMrB,OAAO,CAACsC,GAAG,CAAC,UAAU,CAAC;MAC9C,OAAOjB,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOR,KAAK,EAAE;MAAA,IAAAwB,gBAAA;MACd,MAAM,EAAAA,gBAAA,GAAAxB,KAAK,CAACM,QAAQ,cAAAkB,gBAAA,uBAAdA,gBAAA,CAAgBhB,IAAI,KAAIR,KAAK,CAACU,OAAO;IAC7C;EACF,CAAC;EAED;EACAe,aAAa,EAAE,MAAOC,WAAW,IAAK;IACpC,IAAI;MACF,MAAMpB,QAAQ,GAAG,MAAMrB,OAAO,CAAC0C,GAAG,CAAC,UAAU,EAAED,WAAW,CAAC;MAC3D,OAAOpB,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOR,KAAK,EAAE;MAAA,IAAA4B,gBAAA;MACd,MAAM,EAAAA,gBAAA,GAAA5B,KAAK,CAACM,QAAQ,cAAAsB,gBAAA,uBAAdA,gBAAA,CAAgBpB,IAAI,KAAIR,KAAK,CAACU,OAAO;IAC7C;EACF,CAAC;EAED;EACAmB,MAAM,EAAEA,CAAA,KAAM;IACZhC,YAAY,CAACiC,UAAU,CAAC,OAAO,CAAC;IAChCjC,YAAY,CAACiC,UAAU,CAAC,MAAM,CAAC;EACjC,CAAC;EAED;EACAC,eAAe,EAAEA,CAAA,KAAM;IACrB,OAAO,CAAC,CAAClC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EACxC,CAAC;EAED;EACAkC,cAAc,EAAEA,CAAA,KAAM;IACpB,MAAMf,IAAI,GAAGpB,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;IACzC,OAAOmB,IAAI,GAAGF,IAAI,CAACkB,KAAK,CAAChB,IAAI,CAAC,GAAG,IAAI;EACvC;AACF,CAAC;;AAED;AACA,OAAO,MAAMiB,cAAc,GAAG;EAC5B;EACAC,WAAW,EAAE,MAAOC,SAAS,IAAK;IAChC,IAAI;MACF,MAAM9B,QAAQ,GAAG,MAAMjB,UAAU,CAACkB,IAAI,CAAC,QAAQ,EAAE6B,SAAS,CAAC;MAC3D,OAAO9B,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOR,KAAK,EAAE;MAAA,IAAAqC,gBAAA;MACd,MAAM,EAAAA,gBAAA,GAAArC,KAAK,CAACM,QAAQ,cAAA+B,gBAAA,uBAAdA,gBAAA,CAAgB7B,IAAI,KAAIR,KAAK,CAACU,OAAO;IAC7C;EACF,CAAC;EAED;EACA4B,mBAAmB,EAAE,MAAOC,QAAQ,IAAK;IACvC,IAAI;MACF,MAAMjC,QAAQ,GAAG,MAAMjB,UAAU,CAACkB,IAAI,CAAC,kBAAkB,EAAEgC,QAAQ,EAAE;QACnEnD,OAAO,EAAE;UACP,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MACF,OAAOkB,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOR,KAAK,EAAE;MAAA,IAAAwC,gBAAA;MACd,MAAM,EAAAA,gBAAA,GAAAxC,KAAK,CAACM,QAAQ,cAAAkC,gBAAA,uBAAdA,gBAAA,CAAgBhC,IAAI,KAAIR,KAAK,CAACU,OAAO;IAC7C;EACF,CAAC;EAED;EACA+B,eAAe,EAAE,MAAAA,CAAOC,MAAM,EAAEC,KAAK,GAAG,EAAE,KAAK;IAC7C,IAAI;MACF,MAAMrC,QAAQ,GAAG,MAAMjB,UAAU,CAACkC,GAAG,CAAC,YAAYmB,MAAM,UAAUC,KAAK,EAAE,CAAC;MAC1E,OAAOrC,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOR,KAAK,EAAE;MAAA,IAAA4C,gBAAA;MACd,MAAM,EAAAA,gBAAA,GAAA5C,KAAK,CAACM,QAAQ,cAAAsC,gBAAA,uBAAdA,gBAAA,CAAgBpC,IAAI,KAAIR,KAAK,CAACU,OAAO;IAC7C;EACF,CAAC;EAED;EACAmC,YAAY,EAAE,MAAOH,MAAM,IAAK;IAC9B,IAAI;MACF,MAAMpC,QAAQ,GAAG,MAAMjB,UAAU,CAACkC,GAAG,CAAC,UAAUmB,MAAM,EAAE,CAAC;MACzD,OAAOpC,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOR,KAAK,EAAE;MAAA,IAAA8C,gBAAA;MACd,MAAM,EAAAA,gBAAA,GAAA9C,KAAK,CAACM,QAAQ,cAAAwC,gBAAA,uBAAdA,gBAAA,CAAgBtC,IAAI,KAAIR,KAAK,CAACU,OAAO;IAC7C;EACF,CAAC;EAED;EACAqC,mBAAmB,EAAE,MAAAA,CAAOC,KAAK,EAAEC,WAAW,GAAG,CAAC,KAAK;IACrD,IAAI;MACF,MAAM3C,QAAQ,GAAG,MAAMjB,UAAU,CAACkB,IAAI,CAAC,SAAS,EAAE;QAChDyC,KAAK;QACLE,YAAY,EAAED;MAChB,CAAC,CAAC;MACF,OAAO3C,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOR,KAAK,EAAE;MAAA,IAAAmD,gBAAA;MACd,MAAM,EAAAA,gBAAA,GAAAnD,KAAK,CAACM,QAAQ,cAAA6C,gBAAA,uBAAdA,gBAAA,CAAgB3C,IAAI,KAAIR,KAAK,CAACU,OAAO;IAC7C;EACF,CAAC;EAED;EACA0C,WAAW,EAAE,MAAOC,OAAO,IAAK;IAC9B,IAAI;MACF,MAAM/C,QAAQ,GAAG,MAAMjB,UAAU,CAACkB,IAAI,CAAC,eAAe,EAAE8C,OAAO,EAAE;QAC/DjE,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDkE,YAAY,EAAE,MAAM,CAAE;MACxB,CAAC,CAAC;MACF,OAAOhD,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOR,KAAK,EAAE;MAAA,IAAAuD,gBAAA;MACd,MAAM,EAAAA,gBAAA,GAAAvD,KAAK,CAACM,QAAQ,cAAAiD,gBAAA,uBAAdA,gBAAA,CAAgB/C,IAAI,KAAIR,KAAK,CAACU,OAAO;IAC7C;EACF,CAAC;EAED;EACA8C,MAAM,EAAE,MAAAA,CAAOR,KAAK,GAAG,mBAAmB,EAAES,QAAQ,GAAG,SAAS,KAAK;IACnE,IAAI;MACF,MAAMnD,QAAQ,GAAG,MAAMjB,UAAU,CAACkB,IAAI,CAAC,kBAAkBmD,kBAAkB,CAACV,KAAK,CAAC,aAAaS,QAAQ,EAAE,CAAC;MAC1G,OAAOnD,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOR,KAAK,EAAE;MAAA,IAAA2D,iBAAA;MACd,MAAM,EAAAA,iBAAA,GAAA3D,KAAK,CAACM,QAAQ,cAAAqD,iBAAA,uBAAdA,iBAAA,CAAgBnD,IAAI,KAAIR,KAAK,CAACU,OAAO;IAC7C;EACF,CAAC;EAED;EACAkD,WAAW,EAAE,MAAAA,CAAA,KAAY;IACvB,IAAI;MACF,MAAMtD,QAAQ,GAAG,MAAMjB,UAAU,CAACkC,GAAG,CAAC,SAAS,CAAC;MAChD,OAAOjB,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOR,KAAK,EAAE;MAAA,IAAA6D,iBAAA;MACd,MAAM,EAAAA,iBAAA,GAAA7D,KAAK,CAACM,QAAQ,cAAAuD,iBAAA,uBAAdA,iBAAA,CAAgBrD,IAAI,KAAIR,KAAK,CAACU,OAAO;IAC7C;EACF;AACF,CAAC;;AAED;AACA,OAAO,MAAMoD,QAAQ,GAAG;EACtB;EACAC,WAAW,EAAG/D,KAAK,IAAK;IACtB,IAAIA,KAAK,CAACM,QAAQ,EAAE;MAAA,IAAA0D,oBAAA,EAAAC,qBAAA;MAClB;MACA,OAAO,EAAAD,oBAAA,GAAAhE,KAAK,CAACM,QAAQ,CAACE,IAAI,cAAAwD,oBAAA,uBAAnBA,oBAAA,CAAqBE,MAAM,OAAAD,qBAAA,GAAIjE,KAAK,CAACM,QAAQ,CAACE,IAAI,cAAAyD,qBAAA,uBAAnBA,qBAAA,CAAqBvD,OAAO,KAAI,uBAAuB;IAC/F,CAAC,MAAM,IAAIV,KAAK,CAACP,OAAO,EAAE;MACxB;MACA,OAAO,8CAA8C;IACvD,CAAC,MAAM;MACL;MACA,OAAOO,KAAK,CAACU,OAAO,IAAI,8BAA8B;IACxD;EACF,CAAC;EAED;EACAyD,kBAAkB,EAAGnE,KAAK,IAAK;IAC7B,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC7B,OAAOA,KAAK;IACd;IACA,IAAIoE,KAAK,CAACC,OAAO,CAACrE,KAAK,CAACkE,MAAM,CAAC,EAAE;MAC/B,OAAOlE,KAAK,CAACkE,MAAM,CAACI,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACC,GAAG,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;IACpD;IACA,OAAOzE,KAAK,CAACkE,MAAM,IAAIlE,KAAK,CAACU,OAAO,IAAI,mBAAmB;EAC7D;AACF,CAAC;AAED,eAAe;EAAEP,WAAW;EAAE+B,cAAc;EAAE4B;AAAS,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}