{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport integerPropType from '@mui/utils/integerPropType';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { styled } from \"../zero-styled/index.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport { getStepperUtilityClass } from \"./stepperClasses.js\";\nimport StepConnector from \"../StepConnector/index.js\";\nimport StepperContext from \"./StepperContext.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    orientation,\n    nonLinear,\n    alternativeLabel,\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root', orientation, nonLinear && 'nonLinear', alternativeLabel && 'alternativeLabel']\n  };\n  return composeClasses(slots, getStepperUtilityClass, classes);\n};\nconst StepperRoot = styled('div', {\n  name: '<PERSON><PERSON><PERSON>tep<PERSON>',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[ownerState.orientation], ownerState.alternativeLabel && styles.alternativeLabel, ownerState.nonLinear && styles.nonLinear];\n  }\n})({\n  display: 'flex',\n  variants: [{\n    props: {\n      orientation: 'horizontal'\n    },\n    style: {\n      flexDirection: 'row',\n      alignItems: 'center'\n    }\n  }, {\n    props: {\n      orientation: 'vertical'\n    },\n    style: {\n      flexDirection: 'column'\n    }\n  }, {\n    props: {\n      alternativeLabel: true\n    },\n    style: {\n      alignItems: 'flex-start'\n    }\n  }]\n});\nconst defaultConnector = /*#__PURE__*/_jsx(StepConnector, {});\nconst Stepper = /*#__PURE__*/React.forwardRef(function Stepper(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiStepper'\n  });\n  const {\n    activeStep = 0,\n    alternativeLabel = false,\n    children,\n    className,\n    component = 'div',\n    connector = defaultConnector,\n    nonLinear = false,\n    orientation = 'horizontal',\n    ...other\n  } = props;\n  const ownerState = {\n    ...props,\n    nonLinear,\n    alternativeLabel,\n    orientation,\n    component\n  };\n  const classes = useUtilityClasses(ownerState);\n  const childrenArray = React.Children.toArray(children).filter(Boolean);\n  const steps = childrenArray.map((step, index) => {\n    return /*#__PURE__*/React.cloneElement(step, {\n      index,\n      last: index + 1 === childrenArray.length,\n      ...step.props\n    });\n  });\n  const contextValue = React.useMemo(() => ({\n    activeStep,\n    alternativeLabel,\n    connector,\n    nonLinear,\n    orientation\n  }), [activeStep, alternativeLabel, connector, nonLinear, orientation]);\n  return /*#__PURE__*/_jsx(StepperContext.Provider, {\n    value: contextValue,\n    children: /*#__PURE__*/_jsx(StepperRoot, {\n      as: component,\n      ownerState: ownerState,\n      className: clsx(classes.root, className),\n      ref: ref,\n      ...other,\n      children: steps\n    })\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Stepper.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Set the active step (zero based index).\n   * Set to -1 to disable all the steps.\n   * @default 0\n   */\n  activeStep: integerPropType,\n  /**\n   * If set to 'true' and orientation is horizontal,\n   * then the step label will be positioned under the icon.\n   * @default false\n   */\n  alternativeLabel: PropTypes.bool,\n  /**\n   * Two or more `<Step />` components.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * An element to be placed between each step.\n   * @default <StepConnector />\n   */\n  connector: PropTypes.element,\n  /**\n   * If set the `Stepper` will not assist in controlling steps for linear flow.\n   * @default false\n   */\n  nonLinear: PropTypes.bool,\n  /**\n   * The component orientation (layout flow direction).\n   * @default 'horizontal'\n   */\n  orientation: PropTypes.oneOf(['horizontal', 'vertical']),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default Stepper;", "map": {"version": 3, "names": ["React", "PropTypes", "clsx", "integerPropType", "composeClasses", "styled", "useDefaultProps", "getStepperUtilityClass", "StepConnector", "StepperContext", "jsx", "_jsx", "useUtilityClasses", "ownerState", "orientation", "nonLinear", "alternativeLabel", "classes", "slots", "root", "StepperRoot", "name", "slot", "overridesResolver", "props", "styles", "display", "variants", "style", "flexDirection", "alignItems", "defaultConnector", "Stepper", "forwardRef", "inProps", "ref", "activeStep", "children", "className", "component", "connector", "other", "childrenA<PERSON>y", "Children", "toArray", "filter", "Boolean", "steps", "map", "step", "index", "cloneElement", "last", "length", "contextValue", "useMemo", "Provider", "value", "as", "process", "env", "NODE_ENV", "propTypes", "bool", "node", "object", "string", "elementType", "element", "oneOf", "sx", "oneOfType", "arrayOf", "func"], "sources": ["C:/Users/<USER>/OneDrive/Documents/AuthPages/Evolune/medcure/node_modules/@mui/material/esm/Stepper/Stepper.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport integerPropType from '@mui/utils/integerPropType';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { styled } from \"../zero-styled/index.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport { getStepperUtilityClass } from \"./stepperClasses.js\";\nimport StepConnector from \"../StepConnector/index.js\";\nimport StepperContext from \"./StepperContext.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    orientation,\n    nonLinear,\n    alternativeLabel,\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root', orientation, nonLinear && 'nonLinear', alternativeLabel && 'alternativeLabel']\n  };\n  return composeClasses(slots, getStepperUtilityClass, classes);\n};\nconst StepperRoot = styled('div', {\n  name: '<PERSON><PERSON><PERSON>tep<PERSON>',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[ownerState.orientation], ownerState.alternativeLabel && styles.alternativeLabel, ownerState.nonLinear && styles.nonLinear];\n  }\n})({\n  display: 'flex',\n  variants: [{\n    props: {\n      orientation: 'horizontal'\n    },\n    style: {\n      flexDirection: 'row',\n      alignItems: 'center'\n    }\n  }, {\n    props: {\n      orientation: 'vertical'\n    },\n    style: {\n      flexDirection: 'column'\n    }\n  }, {\n    props: {\n      alternativeLabel: true\n    },\n    style: {\n      alignItems: 'flex-start'\n    }\n  }]\n});\nconst defaultConnector = /*#__PURE__*/_jsx(StepConnector, {});\nconst Stepper = /*#__PURE__*/React.forwardRef(function Stepper(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiStepper'\n  });\n  const {\n    activeStep = 0,\n    alternativeLabel = false,\n    children,\n    className,\n    component = 'div',\n    connector = defaultConnector,\n    nonLinear = false,\n    orientation = 'horizontal',\n    ...other\n  } = props;\n  const ownerState = {\n    ...props,\n    nonLinear,\n    alternativeLabel,\n    orientation,\n    component\n  };\n  const classes = useUtilityClasses(ownerState);\n  const childrenArray = React.Children.toArray(children).filter(Boolean);\n  const steps = childrenArray.map((step, index) => {\n    return /*#__PURE__*/React.cloneElement(step, {\n      index,\n      last: index + 1 === childrenArray.length,\n      ...step.props\n    });\n  });\n  const contextValue = React.useMemo(() => ({\n    activeStep,\n    alternativeLabel,\n    connector,\n    nonLinear,\n    orientation\n  }), [activeStep, alternativeLabel, connector, nonLinear, orientation]);\n  return /*#__PURE__*/_jsx(StepperContext.Provider, {\n    value: contextValue,\n    children: /*#__PURE__*/_jsx(StepperRoot, {\n      as: component,\n      ownerState: ownerState,\n      className: clsx(classes.root, className),\n      ref: ref,\n      ...other,\n      children: steps\n    })\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Stepper.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Set the active step (zero based index).\n   * Set to -1 to disable all the steps.\n   * @default 0\n   */\n  activeStep: integerPropType,\n  /**\n   * If set to 'true' and orientation is horizontal,\n   * then the step label will be positioned under the icon.\n   * @default false\n   */\n  alternativeLabel: PropTypes.bool,\n  /**\n   * Two or more `<Step />` components.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * An element to be placed between each step.\n   * @default <StepConnector />\n   */\n  connector: PropTypes.element,\n  /**\n   * If set the `Stepper` will not assist in controlling steps for linear flow.\n   * @default false\n   */\n  nonLinear: PropTypes.bool,\n  /**\n   * The component orientation (layout flow direction).\n   * @default 'horizontal'\n   */\n  orientation: PropTypes.oneOf(['horizontal', 'vertical']),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default Stepper;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,eAAe,MAAM,4BAA4B;AACxD,OAAOC,cAAc,MAAM,2BAA2B;AACtD,SAASC,MAAM,QAAQ,yBAAyB;AAChD,SAASC,eAAe,QAAQ,kCAAkC;AAClE,SAASC,sBAAsB,QAAQ,qBAAqB;AAC5D,OAAOC,aAAa,MAAM,2BAA2B;AACrD,OAAOC,cAAc,MAAM,qBAAqB;AAChD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,WAAW;IACXC,SAAS;IACTC,gBAAgB;IAChBC;EACF,CAAC,GAAGJ,UAAU;EACd,MAAMK,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAEL,WAAW,EAAEC,SAAS,IAAI,WAAW,EAAEC,gBAAgB,IAAI,kBAAkB;EAC9F,CAAC;EACD,OAAOZ,cAAc,CAACc,KAAK,EAAEX,sBAAsB,EAAEU,OAAO,CAAC;AAC/D,CAAC;AACD,MAAMG,WAAW,GAAGf,MAAM,CAAC,KAAK,EAAE;EAChCgB,IAAI,EAAE,YAAY;EAClBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJZ;IACF,CAAC,GAAGW,KAAK;IACT,OAAO,CAACC,MAAM,CAACN,IAAI,EAAEM,MAAM,CAACZ,UAAU,CAACC,WAAW,CAAC,EAAED,UAAU,CAACG,gBAAgB,IAAIS,MAAM,CAACT,gBAAgB,EAAEH,UAAU,CAACE,SAAS,IAAIU,MAAM,CAACV,SAAS,CAAC;EACxJ;AACF,CAAC,CAAC,CAAC;EACDW,OAAO,EAAE,MAAM;EACfC,QAAQ,EAAE,CAAC;IACTH,KAAK,EAAE;MACLV,WAAW,EAAE;IACf,CAAC;IACDc,KAAK,EAAE;MACLC,aAAa,EAAE,KAAK;MACpBC,UAAU,EAAE;IACd;EACF,CAAC,EAAE;IACDN,KAAK,EAAE;MACLV,WAAW,EAAE;IACf,CAAC;IACDc,KAAK,EAAE;MACLC,aAAa,EAAE;IACjB;EACF,CAAC,EAAE;IACDL,KAAK,EAAE;MACLR,gBAAgB,EAAE;IACpB,CAAC;IACDY,KAAK,EAAE;MACLE,UAAU,EAAE;IACd;EACF,CAAC;AACH,CAAC,CAAC;AACF,MAAMC,gBAAgB,GAAG,aAAapB,IAAI,CAACH,aAAa,EAAE,CAAC,CAAC,CAAC;AAC7D,MAAMwB,OAAO,GAAG,aAAahC,KAAK,CAACiC,UAAU,CAAC,SAASD,OAAOA,CAACE,OAAO,EAAEC,GAAG,EAAE;EAC3E,MAAMX,KAAK,GAAGlB,eAAe,CAAC;IAC5BkB,KAAK,EAAEU,OAAO;IACdb,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;IACJe,UAAU,GAAG,CAAC;IACdpB,gBAAgB,GAAG,KAAK;IACxBqB,QAAQ;IACRC,SAAS;IACTC,SAAS,GAAG,KAAK;IACjBC,SAAS,GAAGT,gBAAgB;IAC5BhB,SAAS,GAAG,KAAK;IACjBD,WAAW,GAAG,YAAY;IAC1B,GAAG2B;EACL,CAAC,GAAGjB,KAAK;EACT,MAAMX,UAAU,GAAG;IACjB,GAAGW,KAAK;IACRT,SAAS;IACTC,gBAAgB;IAChBF,WAAW;IACXyB;EACF,CAAC;EACD,MAAMtB,OAAO,GAAGL,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAM6B,aAAa,GAAG1C,KAAK,CAAC2C,QAAQ,CAACC,OAAO,CAACP,QAAQ,CAAC,CAACQ,MAAM,CAACC,OAAO,CAAC;EACtE,MAAMC,KAAK,GAAGL,aAAa,CAACM,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;IAC/C,OAAO,aAAalD,KAAK,CAACmD,YAAY,CAACF,IAAI,EAAE;MAC3CC,KAAK;MACLE,IAAI,EAAEF,KAAK,GAAG,CAAC,KAAKR,aAAa,CAACW,MAAM;MACxC,GAAGJ,IAAI,CAACzB;IACV,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,MAAM8B,YAAY,GAAGtD,KAAK,CAACuD,OAAO,CAAC,OAAO;IACxCnB,UAAU;IACVpB,gBAAgB;IAChBwB,SAAS;IACTzB,SAAS;IACTD;EACF,CAAC,CAAC,EAAE,CAACsB,UAAU,EAAEpB,gBAAgB,EAAEwB,SAAS,EAAEzB,SAAS,EAAED,WAAW,CAAC,CAAC;EACtE,OAAO,aAAaH,IAAI,CAACF,cAAc,CAAC+C,QAAQ,EAAE;IAChDC,KAAK,EAAEH,YAAY;IACnBjB,QAAQ,EAAE,aAAa1B,IAAI,CAACS,WAAW,EAAE;MACvCsC,EAAE,EAAEnB,SAAS;MACb1B,UAAU,EAAEA,UAAU;MACtByB,SAAS,EAAEpC,IAAI,CAACe,OAAO,CAACE,IAAI,EAAEmB,SAAS,CAAC;MACxCH,GAAG,EAAEA,GAAG;MACR,GAAGM,KAAK;MACRJ,QAAQ,EAAEU;IACZ,CAAC;EACH,CAAC,CAAC;AACJ,CAAC,CAAC;AACFY,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG7B,OAAO,CAAC8B,SAAS,CAAC,yBAAyB;EACjF;EACA;EACA;EACA;EACA;AACF;AACA;AACA;AACA;EACE1B,UAAU,EAAEjC,eAAe;EAC3B;AACF;AACA;AACA;AACA;EACEa,gBAAgB,EAAEf,SAAS,CAAC8D,IAAI;EAChC;AACF;AACA;EACE1B,QAAQ,EAAEpC,SAAS,CAAC+D,IAAI;EACxB;AACF;AACA;EACE/C,OAAO,EAAEhB,SAAS,CAACgE,MAAM;EACzB;AACF;AACA;EACE3B,SAAS,EAAErC,SAAS,CAACiE,MAAM;EAC3B;AACF;AACA;AACA;EACE3B,SAAS,EAAEtC,SAAS,CAACkE,WAAW;EAChC;AACF;AACA;AACA;EACE3B,SAAS,EAAEvC,SAAS,CAACmE,OAAO;EAC5B;AACF;AACA;AACA;EACErD,SAAS,EAAEd,SAAS,CAAC8D,IAAI;EACzB;AACF;AACA;AACA;EACEjD,WAAW,EAAEb,SAAS,CAACoE,KAAK,CAAC,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;EACxD;AACF;AACA;EACEC,EAAE,EAAErE,SAAS,CAACsE,SAAS,CAAC,CAACtE,SAAS,CAACuE,OAAO,CAACvE,SAAS,CAACsE,SAAS,CAAC,CAACtE,SAAS,CAACwE,IAAI,EAAExE,SAAS,CAACgE,MAAM,EAAEhE,SAAS,CAAC8D,IAAI,CAAC,CAAC,CAAC,EAAE9D,SAAS,CAACwE,IAAI,EAAExE,SAAS,CAACgE,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACV,eAAejC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}