{"ast": null, "code": "'use client';\n\nimport PropTypes from 'prop-types';\nimport createStack from \"./createStack.js\";\n/**\n *\n * Demos:\n *\n * - [Stack (Joy UI)](https://mui.com/joy-ui/react-stack/)\n * - [Stack (Material UI)](https://mui.com/material-ui/react-stack/)\n * - [Stack (MUI System)](https://mui.com/system/react-stack/)\n *\n * API:\n *\n * - [Stack API](https://mui.com/system/api/stack/)\n */\nconst Stack = createStack();\nprocess.env.NODE_ENV !== \"production\" ? Stack.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * Defines the `flex-direction` style property.\n   * It is applied for all screen sizes.\n   * @default 'column'\n   */\n  direction: PropTypes.oneOfType([PropTypes.oneOf(['column-reverse', 'column', 'row-reverse', 'row']), PropTypes.arrayOf(PropTypes.oneOf(['column-reverse', 'column', 'row-reverse', 'row'])), PropTypes.object]),\n  /**\n   * Add an element between each child.\n   */\n  divider: PropTypes.node,\n  /**\n   * Defines the space between immediate children.\n   * @default 0\n   */\n  spacing: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.number, PropTypes.string])), PropTypes.number, PropTypes.object, PropTypes.string]),\n  /**\n   * The system prop, which allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * If `true`, the CSS flexbox `gap` is used instead of applying `margin` to children.\n   *\n   * While CSS `gap` removes the [known limitations](https://mui.com/joy-ui/react-stack/#limitations),\n   * it is not fully supported in some browsers. We recommend checking https://caniuse.com/?search=flex%20gap before using this flag.\n   *\n   * To enable this flag globally, follow the theme's default props configuration.\n   * @default false\n   */\n  useFlexGap: PropTypes.bool\n} : void 0;\nexport default Stack;", "map": {"version": 3, "names": ["PropTypes", "createStack", "<PERSON><PERSON>", "process", "env", "NODE_ENV", "propTypes", "children", "node", "component", "elementType", "direction", "oneOfType", "oneOf", "arrayOf", "object", "divider", "spacing", "number", "string", "sx", "func", "bool", "useFlexGap"], "sources": ["C:/Users/<USER>/OneDrive/Documents/AuthPages/Evolune/auth-app/node_modules/@mui/system/esm/Stack/Stack.js"], "sourcesContent": ["'use client';\n\nimport PropTypes from 'prop-types';\nimport createStack from \"./createStack.js\";\n/**\n *\n * Demos:\n *\n * - [Stack (Joy UI)](https://mui.com/joy-ui/react-stack/)\n * - [Stack (Material UI)](https://mui.com/material-ui/react-stack/)\n * - [Stack (MUI System)](https://mui.com/system/react-stack/)\n *\n * API:\n *\n * - [Stack API](https://mui.com/system/api/stack/)\n */\nconst Stack = createStack();\nprocess.env.NODE_ENV !== \"production\" ? Stack.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * Defines the `flex-direction` style property.\n   * It is applied for all screen sizes.\n   * @default 'column'\n   */\n  direction: PropTypes.oneOfType([PropTypes.oneOf(['column-reverse', 'column', 'row-reverse', 'row']), PropTypes.arrayOf(PropTypes.oneOf(['column-reverse', 'column', 'row-reverse', 'row'])), PropTypes.object]),\n  /**\n   * Add an element between each child.\n   */\n  divider: PropTypes.node,\n  /**\n   * Defines the space between immediate children.\n   * @default 0\n   */\n  spacing: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.number, PropTypes.string])), PropTypes.number, PropTypes.object, PropTypes.string]),\n  /**\n   * The system prop, which allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * If `true`, the CSS flexbox `gap` is used instead of applying `margin` to children.\n   *\n   * While CSS `gap` removes the [known limitations](https://mui.com/joy-ui/react-stack/#limitations),\n   * it is not fully supported in some browsers. We recommend checking https://caniuse.com/?search=flex%20gap before using this flag.\n   *\n   * To enable this flag globally, follow the theme's default props configuration.\n   * @default false\n   */\n  useFlexGap: PropTypes.bool\n} : void 0;\nexport default Stack;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,SAAS,MAAM,YAAY;AAClC,OAAOC,WAAW,MAAM,kBAAkB;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,KAAK,GAAGD,WAAW,CAAC,CAAC;AAC3BE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGH,KAAK,CAACI,SAAS,CAAC,yBAAyB;EAC/E;EACA;EACA;EACA;EACA;AACF;AACA;EACEC,QAAQ,EAAEP,SAAS,CAACQ,IAAI;EACxB;AACF;AACA;AACA;EACEC,SAAS,EAAET,SAAS,CAACU,WAAW;EAChC;AACF;AACA;AACA;AACA;EACEC,SAAS,EAAEX,SAAS,CAACY,SAAS,CAAC,CAACZ,SAAS,CAACa,KAAK,CAAC,CAAC,gBAAgB,EAAE,QAAQ,EAAE,aAAa,EAAE,KAAK,CAAC,CAAC,EAAEb,SAAS,CAACc,OAAO,CAACd,SAAS,CAACa,KAAK,CAAC,CAAC,gBAAgB,EAAE,QAAQ,EAAE,aAAa,EAAE,KAAK,CAAC,CAAC,CAAC,EAAEb,SAAS,CAACe,MAAM,CAAC,CAAC;EAC/M;AACF;AACA;EACEC,OAAO,EAAEhB,SAAS,CAACQ,IAAI;EACvB;AACF;AACA;AACA;EACES,OAAO,EAAEjB,SAAS,CAACY,SAAS,CAAC,CAACZ,SAAS,CAACc,OAAO,CAACd,SAAS,CAACY,SAAS,CAAC,CAACZ,SAAS,CAACkB,MAAM,EAAElB,SAAS,CAACmB,MAAM,CAAC,CAAC,CAAC,EAAEnB,SAAS,CAACkB,MAAM,EAAElB,SAAS,CAACe,MAAM,EAAEf,SAAS,CAACmB,MAAM,CAAC,CAAC;EAClK;AACF;AACA;EACEC,EAAE,EAAEpB,SAAS,CAACY,SAAS,CAAC,CAACZ,SAAS,CAACc,OAAO,CAACd,SAAS,CAACY,SAAS,CAAC,CAACZ,SAAS,CAACqB,IAAI,EAAErB,SAAS,CAACe,MAAM,EAAEf,SAAS,CAACsB,IAAI,CAAC,CAAC,CAAC,EAAEtB,SAAS,CAACqB,IAAI,EAAErB,SAAS,CAACe,MAAM,CAAC,CAAC;EACvJ;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEQ,UAAU,EAAEvB,SAAS,CAACsB;AACxB,CAAC,GAAG,KAAK,CAAC;AACV,eAAepB,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}