{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\AuthPages\\\\Evolune\\\\medcure\\\\src\\\\components\\\\AuthWrapper.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { authService } from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthWrapper = ({\n  children\n}) => {\n  _s();\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    const checkAuth = () => {\n      const authenticated = authService.isAuthenticated();\n      const currentUser = authService.getCurrentUser();\n      setIsAuthenticated(authenticated);\n      setUser(currentUser);\n      setLoading(false);\n    };\n    checkAuth();\n  }, []);\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        height: '100vh',\n        fontSize: '18px'\n      },\n      children: \"Loading...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 7\n    }, this);\n  }\n  if (!isAuthenticated) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        flexDirection: 'column',\n        justifyContent: 'center',\n        alignItems: 'center',\n        height: '100vh',\n        padding: '20px',\n        textAlign: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Authentication Required\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Please log in to access the MedCure application.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"http://localhost:3000\",\n          style: {\n            color: '#007bff',\n            textDecoration: 'none',\n            fontSize: '18px',\n            fontWeight: 'bold'\n          },\n          children: \"Go to Login Page\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 49,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: '20px',\n          fontSize: '14px',\n          color: '#666'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"The authentication system runs on a separate application.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"After logging in, return to this page to access MedCure.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'fixed',\n        top: '10px',\n        right: '10px',\n        background: '#f8f9fa',\n        padding: '5px 10px',\n        borderRadius: '5px',\n        fontSize: '12px',\n        zIndex: 1000\n      },\n      children: [\"Welcome, \", (user === null || user === void 0 ? void 0 : user.name) || (user === null || user === void 0 ? void 0 : user.email) || 'User', /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => {\n          authService.logout();\n          window.location.reload();\n        },\n        style: {\n          marginLeft: '10px',\n          padding: '2px 8px',\n          fontSize: '10px',\n          background: '#dc3545',\n          color: 'white',\n          border: 'none',\n          borderRadius: '3px',\n          cursor: 'pointer'\n        },\n        children: \"Logout\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 7\n    }, this), children]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 71,\n    columnNumber: 5\n  }, this);\n};\n_s(AuthWrapper, \"mKb+zX9grAar2iMVStuBBwt7fpw=\");\n_c = AuthWrapper;\nexport default AuthWrapper;\nvar _c;\n$RefreshReg$(_c, \"AuthWrapper\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "authService", "jsxDEV", "_jsxDEV", "AuthWrapper", "children", "_s", "isAuthenticated", "setIsAuthenticated", "user", "setUser", "loading", "setLoading", "checkAuth", "authenticated", "currentUser", "getCurrentUser", "style", "display", "justifyContent", "alignItems", "height", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "flexDirection", "padding", "textAlign", "href", "color", "textDecoration", "fontWeight", "marginTop", "position", "top", "right", "background", "borderRadius", "zIndex", "name", "email", "onClick", "logout", "window", "location", "reload", "marginLeft", "border", "cursor", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Documents/AuthPages/Evolune/medcure/src/components/AuthWrapper.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { authService } from '../services/api';\n\nconst AuthWrapper = ({ children }) => {\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    const checkAuth = () => {\n      const authenticated = authService.isAuthenticated();\n      const currentUser = authService.getCurrentUser();\n      \n      setIsAuthenticated(authenticated);\n      setUser(currentUser);\n      setLoading(false);\n    };\n\n    checkAuth();\n  }, []);\n\n  if (loading) {\n    return (\n      <div style={{ \n        display: 'flex', \n        justifyContent: 'center', \n        alignItems: 'center', \n        height: '100vh',\n        fontSize: '18px'\n      }}>\n        Loading...\n      </div>\n    );\n  }\n\n  if (!isAuthenticated) {\n    return (\n      <div style={{ \n        display: 'flex', \n        flexDirection: 'column',\n        justifyContent: 'center', \n        alignItems: 'center', \n        height: '100vh',\n        padding: '20px',\n        textAlign: 'center'\n      }}>\n        <h2>Authentication Required</h2>\n        <p>Please log in to access the MedCure application.</p>\n        <p>\n          <a\n            href=\"http://localhost:3000\"\n            style={{\n              color: '#007bff',\n              textDecoration: 'none',\n              fontSize: '18px',\n              fontWeight: 'bold'\n            }}\n          >\n            Go to Login Page\n          </a>\n        </p>\n        <div style={{ marginTop: '20px', fontSize: '14px', color: '#666' }}>\n          <p>The authentication system runs on a separate application.</p>\n          <p>After logging in, return to this page to access MedCure.</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div>\n      {/* Optional: Display user info */}\n      <div style={{ \n        position: 'fixed', \n        top: '10px', \n        right: '10px', \n        background: '#f8f9fa', \n        padding: '5px 10px', \n        borderRadius: '5px',\n        fontSize: '12px',\n        zIndex: 1000\n      }}>\n        Welcome, {user?.name || user?.email || 'User'}\n        <button \n          onClick={() => {\n            authService.logout();\n            window.location.reload();\n          }}\n          style={{ \n            marginLeft: '10px', \n            padding: '2px 8px', \n            fontSize: '10px',\n            background: '#dc3545',\n            color: 'white',\n            border: 'none',\n            borderRadius: '3px',\n            cursor: 'pointer'\n          }}\n        >\n          Logout\n        </button>\n      </div>\n      {children}\n    </div>\n  );\n};\n\nexport default AuthWrapper;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9C,MAAMC,WAAW,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACpC,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGT,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACU,IAAI,EAAEC,OAAO,CAAC,GAAGX,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACY,OAAO,EAAEC,UAAU,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EAE5CC,SAAS,CAAC,MAAM;IACd,MAAMa,SAAS,GAAGA,CAAA,KAAM;MACtB,MAAMC,aAAa,GAAGb,WAAW,CAACM,eAAe,CAAC,CAAC;MACnD,MAAMQ,WAAW,GAAGd,WAAW,CAACe,cAAc,CAAC,CAAC;MAEhDR,kBAAkB,CAACM,aAAa,CAAC;MACjCJ,OAAO,CAACK,WAAW,CAAC;MACpBH,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC;IAEDC,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;EAEN,IAAIF,OAAO,EAAE;IACX,oBACER,OAAA;MAAKc,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,QAAQ;QACxBC,UAAU,EAAE,QAAQ;QACpBC,MAAM,EAAE,OAAO;QACfC,QAAQ,EAAE;MACZ,CAAE;MAAAjB,QAAA,EAAC;IAEH;MAAAkB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAEV;EAEA,IAAI,CAACnB,eAAe,EAAE;IACpB,oBACEJ,OAAA;MAAKc,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfS,aAAa,EAAE,QAAQ;QACvBR,cAAc,EAAE,QAAQ;QACxBC,UAAU,EAAE,QAAQ;QACpBC,MAAM,EAAE,OAAO;QACfO,OAAO,EAAE,MAAM;QACfC,SAAS,EAAE;MACb,CAAE;MAAAxB,QAAA,gBACAF,OAAA;QAAAE,QAAA,EAAI;MAAuB;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAChCvB,OAAA;QAAAE,QAAA,EAAG;MAAgD;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACvDvB,OAAA;QAAAE,QAAA,eACEF,OAAA;UACE2B,IAAI,EAAC,uBAAuB;UAC5Bb,KAAK,EAAE;YACLc,KAAK,EAAE,SAAS;YAChBC,cAAc,EAAE,MAAM;YACtBV,QAAQ,EAAE,MAAM;YAChBW,UAAU,EAAE;UACd,CAAE;UAAA5B,QAAA,EACH;QAED;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACJvB,OAAA;QAAKc,KAAK,EAAE;UAAEiB,SAAS,EAAE,MAAM;UAAEZ,QAAQ,EAAE,MAAM;UAAES,KAAK,EAAE;QAAO,CAAE;QAAA1B,QAAA,gBACjEF,OAAA;UAAAE,QAAA,EAAG;QAAyD;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAChEvB,OAAA;UAAAE,QAAA,EAAG;QAAwD;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5D,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEvB,OAAA;IAAAE,QAAA,gBAEEF,OAAA;MAAKc,KAAK,EAAE;QACVkB,QAAQ,EAAE,OAAO;QACjBC,GAAG,EAAE,MAAM;QACXC,KAAK,EAAE,MAAM;QACbC,UAAU,EAAE,SAAS;QACrBV,OAAO,EAAE,UAAU;QACnBW,YAAY,EAAE,KAAK;QACnBjB,QAAQ,EAAE,MAAM;QAChBkB,MAAM,EAAE;MACV,CAAE;MAAAnC,QAAA,GAAC,WACQ,EAAC,CAAAI,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgC,IAAI,MAAIhC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiC,KAAK,KAAI,MAAM,eAC7CvC,OAAA;QACEwC,OAAO,EAAEA,CAAA,KAAM;UACb1C,WAAW,CAAC2C,MAAM,CAAC,CAAC;UACpBC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;QAC1B,CAAE;QACF9B,KAAK,EAAE;UACL+B,UAAU,EAAE,MAAM;UAClBpB,OAAO,EAAE,SAAS;UAClBN,QAAQ,EAAE,MAAM;UAChBgB,UAAU,EAAE,SAAS;UACrBP,KAAK,EAAE,OAAO;UACdkB,MAAM,EAAE,MAAM;UACdV,YAAY,EAAE,KAAK;UACnBW,MAAM,EAAE;QACV,CAAE;QAAA7C,QAAA,EACH;MAED;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EACLrB,QAAQ;EAAA;IAAAkB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACpB,EAAA,CAtGIF,WAAW;AAAA+C,EAAA,GAAX/C,WAAW;AAwGjB,eAAeA,WAAW;AAAC,IAAA+C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}