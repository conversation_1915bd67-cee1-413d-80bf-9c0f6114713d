import axios from 'axios';

// API Configuration
const AUTH_API_BASE_URL = 'http://localhost:8000'; // auth-backend-fastapi
const MEDICAL_API_BASE_URL = 'http://localhost:8001'; // medcure-backend

// Create axios instances for different backends
const authAPI = axios.create({
  baseURL: AUTH_API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

const medicalAPI = axios.create({
  baseURL: MEDICAL_API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptors to include auth token
const addAuthInterceptor = (apiInstance) => {
  apiInstance.interceptors.request.use(
    (config) => {
      const token = localStorage.getItem('token');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    },
    (error) => {
      return Promise.reject(error);
    }
  );
};

// Add auth interceptors to both API instances
addAuthInterceptor(authAPI);
addAuthInterceptor(medicalAPI);

// Authentication API calls (auth-backend-fastapi)
export const authService = {
  // User registration
  register: async (userData) => {
    try {
      const response = await authAPI.post('/register', userData);
      return response.data;
    } catch (error) {
      throw error.response?.data || error.message;
    }
  },

  // User login
  login: async (credentials) => {
    try {
      const response = await authAPI.post('/login', credentials);
      if (response.data.access_token) {
        localStorage.setItem('token', response.data.access_token);
        localStorage.setItem('user', JSON.stringify(response.data.user));
      }
      return response.data;
    } catch (error) {
      throw error.response?.data || error.message;
    }
  },

  // Google OAuth login
  googleLogin: async (tokenData) => {
    try {
      const response = await authAPI.post('/google-login', tokenData);
      if (response.data.access_token) {
        localStorage.setItem('token', response.data.access_token);
        localStorage.setItem('user', JSON.stringify(response.data.user));
      }
      return response.data;
    } catch (error) {
      throw error.response?.data || error.message;
    }
  },

  // Get current user profile
  getProfile: async () => {
    try {
      const response = await authAPI.get('/profile');
      return response.data;
    } catch (error) {
      throw error.response?.data || error.message;
    }
  },

  // Update user profile
  updateProfile: async (profileData) => {
    try {
      const response = await authAPI.put('/profile', profileData);
      return response.data;
    } catch (error) {
      throw error.response?.data || error.message;
    }
  },

  // Logout
  logout: () => {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
  },

  // Check if user is authenticated
  isAuthenticated: () => {
    return !!localStorage.getItem('token');
  },

  // Get current user from localStorage
  getCurrentUser: () => {
    const user = localStorage.getItem('user');
    return user ? JSON.parse(user) : null;
  }
};

// Medical AI API calls (medcure-backend)
export const medicalService = {
  // Submit medical query (text only)
  submitQuery: async (queryData) => {
    try {
      const response = await medicalAPI.post('/query', queryData);
      return response.data;
    } catch (error) {
      throw error.response?.data || error.message;
    }
  },

  // Submit medical query with file
  submitQueryWithFile: async (formData) => {
    try {
      const response = await medicalAPI.post('/query-with-file', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error) {
      throw error.response?.data || error.message;
    }
  },

  // Get user query history
  getQueryHistory: async (userId, limit = 20) => {
    try {
      const response = await medicalAPI.get(`/history/${userId}?limit=${limit}`);
      return response.data;
    } catch (error) {
      throw error.response?.data || error.message;
    }
  },

  // Get user statistics
  getUserStats: async (userId) => {
    try {
      const response = await medicalAPI.get(`/stats/${userId}`);
      return response.data;
    } catch (error) {
      throw error.response?.data || error.message;
    }
  },

  // Fetch PubMed articles
  fetchPubMedArticles: async (query, numArticles = 5) => {
    try {
      const response = await medicalAPI.post('/pubmed', {
        query,
        num_articles: numArticles
      });
      return response.data;
    } catch (error) {
      throw error.response?.data || error.message;
    }
  },

  // Generate PDF report
  generatePDF: async (pdfData) => {
    try {
      const response = await medicalAPI.post('/generate-pdf', pdfData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        responseType: 'blob', // Important for PDF download
      });
      return response.data;
    } catch (error) {
      throw error.response?.data || error.message;
    }
  },

  // Test AI functionality
  testAI: async (query = "What is diabetes?", language = "English") => {
    try {
      const response = await medicalAPI.post(`/test-ai?query=${encodeURIComponent(query)}&language=${language}`);
      return response.data;
    } catch (error) {
      throw error.response?.data || error.message;
    }
  },

  // Health check
  healthCheck: async () => {
    try {
      const response = await medicalAPI.get('/health');
      return response.data;
    } catch (error) {
      throw error.response?.data || error.message;
    }
  }
};

// Utility functions
export const apiUtils = {
  // Handle API errors consistently
  handleError: (error) => {
    if (error.response) {
      // Server responded with error status
      return error.response.data?.detail || error.response.data?.message || 'Server error occurred';
    } else if (error.request) {
      // Request was made but no response received
      return 'Network error - please check your connection';
    } else {
      // Something else happened
      return error.message || 'An unexpected error occurred';
    }
  },

  // Format error messages for display
  formatErrorMessage: (error) => {
    if (typeof error === 'string') {
      return error;
    }
    if (Array.isArray(error.detail)) {
      return error.detail.map(err => err.msg).join(', ');
    }
    return error.detail || error.message || 'An error occurred';
  }
};

export default { authService, medicalService, apiUtils };
