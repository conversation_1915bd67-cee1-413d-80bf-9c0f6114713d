{"ast": null, "code": "import { unstable_memoTheme } from '@mui/system';\nconst memoTheme = unstable_memoTheme;\nexport default memoTheme;", "map": {"version": 3, "names": ["unstable_memoTheme", "memoTheme"], "sources": ["C:/Users/<USER>/OneDrive/Documents/AuthPages/Evolune/medcure/node_modules/@mui/material/esm/utils/memoTheme.js"], "sourcesContent": ["import { unstable_memoTheme } from '@mui/system';\nconst memoTheme = unstable_memoTheme;\nexport default memoTheme;"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,aAAa;AAChD,MAAMC,SAAS,GAAGD,kBAAkB;AACpC,eAAeC,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}