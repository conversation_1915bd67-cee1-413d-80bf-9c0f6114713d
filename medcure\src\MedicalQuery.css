.medical-query-wrapper {
  max-width: 650px;
  margin: auto;
  padding: 24px;
  font-family: 'Segoe UI', sans-serif;
  color: #333;
}

.method-toggle {
  margin-bottom: 16px;
}

.method-toggle label {
  margin-right: 16px;
  font-weight: 500;
}

.selected {
  color: #007cf0;
}

.text-box {
  width: 100%;
  height: 120px;
  margin: 12px 0;
  padding: 12px;
  font-size: 16px;
  border-radius: 8px;
  border: 1px solid #ccc;
}

.start-btn {
  padding: 10px 18px;
  background-color: #007cf0;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  cursor: pointer;
}

.recording-text {
  color: red;
  margin-top: 8px;
}

.upload-section {
  margin: 24px 0;
}

.folder-icon {
  font-size: 32px;
}

.file-upload {
  margin-top: 10px;
}

.analyze-btn {
  margin-top: 12px;
  padding: 10px 20px;
  background: linear-gradient(to right, #007cf0, #00dfd8);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  cursor: pointer;
}

.error-message {
  color: red;
  margin-top: 10px;
  font-weight: bold;
}

.response-box {
  margin-top: 20px;
  padding: 12px;
  background: #f0f8ff;
  border: 1px solid #007cf0;
  border-radius: 8px;
  max-height: 400px;
  overflow-y: auto;
}

.response-box h3 {
  margin-top: 0;
  color: #007bff;
  border-bottom: 2px solid #007bff;
  padding-bottom: 10px;
}

/* Selection sections for role and language */
.selection-section {
  margin-bottom: 20px;
}

.selection-section p {
  margin-bottom: 8px;
  font-weight: bold;
}

.role-select, .language-select {
  width: 100%;
  max-width: 300px;
  padding: 8px 12px;
  border: 2px solid #ddd;
  border-radius: 8px;
  font-size: 14px;
  background-color: white;
  cursor: pointer;
}

.role-select:focus, .language-select:focus {
  outline: none;
  border-color: #007bff;
}

/* Loading and error states */
.error-message {
  color: #dc3545;
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  padding: 10px;
  border-radius: 5px;
  margin: 10px 0;
  font-weight: bold;
}

.loading-message {
  color: #007bff;
  text-align: center;
  padding: 20px;
  font-size: 16px;
}

/* Disabled button state */
.analyze-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
  opacity: 0.6;
}

