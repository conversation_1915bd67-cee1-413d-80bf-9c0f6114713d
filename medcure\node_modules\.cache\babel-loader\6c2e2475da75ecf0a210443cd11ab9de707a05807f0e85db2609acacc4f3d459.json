{"ast": null, "code": "function clamp(val, min = Number.MIN_SAFE_INTEGER, max = Number.MAX_SAFE_INTEGER) {\n  return Math.max(min, Math.min(val, max));\n}\nexport default clamp;", "map": {"version": 3, "names": ["clamp", "val", "min", "Number", "MIN_SAFE_INTEGER", "max", "MAX_SAFE_INTEGER", "Math"], "sources": ["C:/Users/<USER>/OneDrive/Documents/AuthPages/Evolune/medcure/node_modules/@mui/utils/esm/clamp/clamp.js"], "sourcesContent": ["function clamp(val, min = Number.MIN_SAFE_INTEGER, max = Number.MAX_SAFE_INTEGER) {\n  return Math.max(min, Math.min(val, max));\n}\nexport default clamp;"], "mappings": "AAAA,SAASA,KAAKA,CAACC,GAAG,EAAEC,GAAG,GAAGC,MAAM,CAACC,gBAAgB,EAAEC,GAAG,GAAGF,MAAM,CAACG,gBAAgB,EAAE;EAChF,OAAOC,IAAI,CAACF,GAAG,CAACH,GAAG,EAAEK,IAAI,CAACL,GAAG,CAACD,GAAG,EAAEI,GAAG,CAAC,CAAC;AAC1C;AACA,eAAeL,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}