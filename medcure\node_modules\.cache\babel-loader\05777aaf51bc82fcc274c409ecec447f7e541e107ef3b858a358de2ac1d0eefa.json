{"ast": null, "code": "'use client';\n\nexport { default } from \"./useLazyRipple.js\";", "map": {"version": 3, "names": ["default"], "sources": ["C:/Users/<USER>/OneDrive/Documents/AuthPages/Evolune/medcure/node_modules/@mui/material/esm/useLazyRipple/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from \"./useLazyRipple.js\";"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}