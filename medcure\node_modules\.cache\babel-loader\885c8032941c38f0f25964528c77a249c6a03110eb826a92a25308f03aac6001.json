{"ast": null, "code": "import ownerWindow from '@mui/utils/ownerWindow';\nexport default ownerWindow;", "map": {"version": 3, "names": ["ownerWindow"], "sources": ["C:/Users/<USER>/OneDrive/Documents/AuthPages/Evolune/medcure/node_modules/@mui/material/esm/utils/ownerWindow.js"], "sourcesContent": ["import ownerWindow from '@mui/utils/ownerWindow';\nexport default ownerWindow;"], "mappings": "AAAA,OAAOA,WAAW,MAAM,wBAAwB;AAChD,eAAeA,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}