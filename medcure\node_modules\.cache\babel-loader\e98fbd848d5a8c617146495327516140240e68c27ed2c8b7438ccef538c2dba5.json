{"ast": null, "code": "export const version = \"7.1.1\";\nexport const major = Number(\"7\");\nexport const minor = Number(\"1\");\nexport const patch = Number(\"1\");\nexport const prerelease = undefined;\nexport default version;", "map": {"version": 3, "names": ["version", "major", "Number", "minor", "patch", "prerelease", "undefined"], "sources": ["C:/Users/<USER>/OneDrive/Documents/AuthPages/Evolune/medcure/node_modules/@mui/system/esm/version/index.js"], "sourcesContent": ["export const version = \"7.1.1\";\nexport const major = Number(\"7\");\nexport const minor = Number(\"1\");\nexport const patch = Number(\"1\");\nexport const prerelease = undefined;\nexport default version;"], "mappings": "AAAA,OAAO,MAAMA,OAAO,GAAG,OAAO;AAC9B,OAAO,MAAMC,KAAK,GAAGC,MAAM,CAAC,GAAG,CAAC;AAChC,OAAO,MAAMC,KAAK,GAAGD,MAAM,CAAC,GAAG,CAAC;AAChC,OAAO,MAAME,KAAK,GAAGF,MAAM,CAAC,GAAG,CAAC;AAChC,OAAO,MAAMG,UAAU,GAAGC,SAAS;AACnC,eAAeN,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}