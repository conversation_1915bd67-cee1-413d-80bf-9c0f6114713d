{"ast": null, "code": "// Inspired by https://github.com/material-components/material-components-ios/blob/bca36107405594d5b7b16265a5b0ed698f85a5ee/components/Elevation/src/UIColor%2BMaterialElevation.m#L61\nexport default function getOverlayAlpha(elevation) {\n  let alphaValue;\n  if (elevation < 1) {\n    alphaValue = 5.11916 * elevation ** 2;\n  } else {\n    alphaValue = 4.5 * Math.log(elevation + 1) + 2;\n  }\n  return Math.round(alphaValue * 10) / 1000;\n}", "map": {"version": 3, "names": ["getOverlayAlpha", "elevation", "alphaValue", "Math", "log", "round"], "sources": ["C:/Users/<USER>/OneDrive/Documents/AuthPages/Evolune/medcure/node_modules/@mui/material/esm/styles/getOverlayAlpha.js"], "sourcesContent": ["// Inspired by https://github.com/material-components/material-components-ios/blob/bca36107405594d5b7b16265a5b0ed698f85a5ee/components/Elevation/src/UIColor%2BMaterialElevation.m#L61\nexport default function getOverlayAlpha(elevation) {\n  let alphaValue;\n  if (elevation < 1) {\n    alphaValue = 5.11916 * elevation ** 2;\n  } else {\n    alphaValue = 4.5 * Math.log(elevation + 1) + 2;\n  }\n  return Math.round(alphaValue * 10) / 1000;\n}"], "mappings": "AAAA;AACA,eAAe,SAASA,eAAeA,CAACC,SAAS,EAAE;EACjD,IAAIC,UAAU;EACd,IAAID,SAAS,GAAG,CAAC,EAAE;IACjBC,UAAU,GAAG,OAAO,GAAGD,SAAS,IAAI,CAAC;EACvC,CAAC,MAAM;IACLC,UAAU,GAAG,GAAG,GAAGC,IAAI,CAACC,GAAG,CAACH,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC;EAChD;EACA,OAAOE,IAAI,CAACE,KAAK,CAACH,UAAU,GAAG,EAAE,CAAC,GAAG,IAAI;AAC3C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}