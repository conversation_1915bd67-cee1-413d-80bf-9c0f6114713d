{"ast": null, "code": "import * as React from 'react';\nimport PropTypes from 'prop-types';\nimport SystemInitColorSchemeScript from '@mui/system/InitColorSchemeScript';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport const defaultConfig = {\n  attribute: 'data-mui-color-scheme',\n  colorSchemeStorageKey: 'mui-color-scheme',\n  defaultLightColorScheme: 'light',\n  defaultDarkColorScheme: 'dark',\n  modeStorageKey: 'mui-mode'\n};\n/**\n *\n * Demos:\n *\n * - [InitColorSchemeScript](https://mui.com/material-ui/react-init-color-scheme-script/)\n *\n * API:\n *\n * - [InitColorSchemeScript API](https://mui.com/material-ui/api/init-color-scheme-script/)\n */\nfunction InitColorSchemeScript(props) {\n  const {\n    defaultMode = 'system',\n    defaultLightColorScheme = defaultConfig.defaultLightColorScheme,\n    defaultDarkColorScheme = defaultConfig.defaultDarkColorScheme,\n    modeStorageKey = defaultConfig.modeStorageKey,\n    colorSchemeStorageKey = defaultConfig.colorSchemeStorageKey,\n    attribute: initialAttribute = defaultConfig.attribute,\n    colorSchemeNode = 'document.documentElement',\n    nonce\n  } = props;\n  return /*#__PURE__*/_jsx(SystemInitColorSchemeScript, {\n    defaultMode: defaultMode,\n    defaultLightColorScheme: defaultLightColorScheme,\n    defaultDarkColorScheme: defaultDarkColorScheme,\n    modeStorageKey: modeStorageKey,\n    colorSchemeStorageKey: colorSchemeStorageKey,\n    attribute: initialAttribute,\n    colorSchemeNode: colorSchemeNode,\n    nonce: nonce\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? InitColorSchemeScript.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * DOM attribute for applying a color scheme.\n   * @default 'data-mui-color-scheme'\n   * @example '.mode-%s' // for class based color scheme\n   * @example '[data-mode-%s]' // for data-attribute without '='\n   */\n  attribute: PropTypes.string,\n  /**\n   * The node (provided as string) used to attach the color-scheme attribute.\n   * @default 'document.documentElement'\n   */\n  colorSchemeNode: PropTypes.string,\n  /**\n   * localStorage key used to store `colorScheme`.\n   * @default 'mui-color-scheme'\n   */\n  colorSchemeStorageKey: PropTypes.string,\n  /**\n   * The default color scheme to be used in dark mode.\n   * @default 'dark'\n   */\n  defaultDarkColorScheme: PropTypes.string,\n  /**\n   * The default color scheme to be used in light mode.\n   * @default 'light'\n   */\n  defaultLightColorScheme: PropTypes.string,\n  /**\n   * The default mode when the storage is empty (user's first visit).\n   * @default 'system'\n   */\n  defaultMode: PropTypes.oneOf(['dark', 'light', 'system']),\n  /**\n   * localStorage key used to store `mode`.\n   * @default 'mui-mode'\n   */\n  modeStorageKey: PropTypes.string,\n  /**\n   * Nonce string to pass to the inline script for CSP headers.\n   */\n  nonce: PropTypes.string\n} : void 0;\nexport default InitColorSchemeScript;", "map": {"version": 3, "names": ["React", "PropTypes", "SystemInitColorSchemeScript", "jsx", "_jsx", "defaultConfig", "attribute", "colorSchemeStorageKey", "defaultLightColorScheme", "defaultDarkColorScheme", "modeStorageKey", "InitColorSchemeScript", "props", "defaultMode", "initialAttribute", "colorSchemeNode", "nonce", "process", "env", "NODE_ENV", "propTypes", "string", "oneOf"], "sources": ["C:/Users/<USER>/OneDrive/Documents/AuthPages/Evolune/medcure/node_modules/@mui/material/esm/InitColorSchemeScript/InitColorSchemeScript.js"], "sourcesContent": ["import * as React from 'react';\nimport PropTypes from 'prop-types';\nimport SystemInitColorSchemeScript from '@mui/system/InitColorSchemeScript';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport const defaultConfig = {\n  attribute: 'data-mui-color-scheme',\n  colorSchemeStorageKey: 'mui-color-scheme',\n  defaultLightColorScheme: 'light',\n  defaultDarkColorScheme: 'dark',\n  modeStorageKey: 'mui-mode'\n};\n/**\n *\n * Demos:\n *\n * - [InitColorSchemeScript](https://mui.com/material-ui/react-init-color-scheme-script/)\n *\n * API:\n *\n * - [InitColorSchemeScript API](https://mui.com/material-ui/api/init-color-scheme-script/)\n */\nfunction InitColorSchemeScript(props) {\n  const {\n    defaultMode = 'system',\n    defaultLightColorScheme = defaultConfig.defaultLightColorScheme,\n    defaultDarkColorScheme = defaultConfig.defaultDarkColorScheme,\n    modeStorageKey = defaultConfig.modeStorageKey,\n    colorSchemeStorageKey = defaultConfig.colorSchemeStorageKey,\n    attribute: initialAttribute = defaultConfig.attribute,\n    colorSchemeNode = 'document.documentElement',\n    nonce\n  } = props;\n  return /*#__PURE__*/_jsx(SystemInitColorSchemeScript, {\n    defaultMode: defaultMode,\n    defaultLightColorScheme: defaultLightColorScheme,\n    defaultDarkColorScheme: defaultDarkColorScheme,\n    modeStorageKey: modeStorageKey,\n    colorSchemeStorageKey: colorSchemeStorageKey,\n    attribute: initialAttribute,\n    colorSchemeNode: colorSchemeNode,\n    nonce: nonce\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? InitColorSchemeScript.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * DOM attribute for applying a color scheme.\n   * @default 'data-mui-color-scheme'\n   * @example '.mode-%s' // for class based color scheme\n   * @example '[data-mode-%s]' // for data-attribute without '='\n   */\n  attribute: PropTypes.string,\n  /**\n   * The node (provided as string) used to attach the color-scheme attribute.\n   * @default 'document.documentElement'\n   */\n  colorSchemeNode: PropTypes.string,\n  /**\n   * localStorage key used to store `colorScheme`.\n   * @default 'mui-color-scheme'\n   */\n  colorSchemeStorageKey: PropTypes.string,\n  /**\n   * The default color scheme to be used in dark mode.\n   * @default 'dark'\n   */\n  defaultDarkColorScheme: PropTypes.string,\n  /**\n   * The default color scheme to be used in light mode.\n   * @default 'light'\n   */\n  defaultLightColorScheme: PropTypes.string,\n  /**\n   * The default mode when the storage is empty (user's first visit).\n   * @default 'system'\n   */\n  defaultMode: PropTypes.oneOf(['dark', 'light', 'system']),\n  /**\n   * localStorage key used to store `mode`.\n   * @default 'mui-mode'\n   */\n  modeStorageKey: PropTypes.string,\n  /**\n   * Nonce string to pass to the inline script for CSP headers.\n   */\n  nonce: PropTypes.string\n} : void 0;\nexport default InitColorSchemeScript;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,2BAA2B,MAAM,mCAAmC;AAC3E,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,OAAO,MAAMC,aAAa,GAAG;EAC3BC,SAAS,EAAE,uBAAuB;EAClCC,qBAAqB,EAAE,kBAAkB;EACzCC,uBAAuB,EAAE,OAAO;EAChCC,sBAAsB,EAAE,MAAM;EAC9BC,cAAc,EAAE;AAClB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,qBAAqBA,CAACC,KAAK,EAAE;EACpC,MAAM;IACJC,WAAW,GAAG,QAAQ;IACtBL,uBAAuB,GAAGH,aAAa,CAACG,uBAAuB;IAC/DC,sBAAsB,GAAGJ,aAAa,CAACI,sBAAsB;IAC7DC,cAAc,GAAGL,aAAa,CAACK,cAAc;IAC7CH,qBAAqB,GAAGF,aAAa,CAACE,qBAAqB;IAC3DD,SAAS,EAAEQ,gBAAgB,GAAGT,aAAa,CAACC,SAAS;IACrDS,eAAe,GAAG,0BAA0B;IAC5CC;EACF,CAAC,GAAGJ,KAAK;EACT,OAAO,aAAaR,IAAI,CAACF,2BAA2B,EAAE;IACpDW,WAAW,EAAEA,WAAW;IACxBL,uBAAuB,EAAEA,uBAAuB;IAChDC,sBAAsB,EAAEA,sBAAsB;IAC9CC,cAAc,EAAEA,cAAc;IAC9BH,qBAAqB,EAAEA,qBAAqB;IAC5CD,SAAS,EAAEQ,gBAAgB;IAC3BC,eAAe,EAAEA,eAAe;IAChCC,KAAK,EAAEA;EACT,CAAC,CAAC;AACJ;AACAC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGR,qBAAqB,CAACS,SAAS,CAAC,yBAAyB;EAC/F;EACA;EACA;EACA;EACA;AACF;AACA;AACA;AACA;AACA;EACEd,SAAS,EAAEL,SAAS,CAACoB,MAAM;EAC3B;AACF;AACA;AACA;EACEN,eAAe,EAAEd,SAAS,CAACoB,MAAM;EACjC;AACF;AACA;AACA;EACEd,qBAAqB,EAAEN,SAAS,CAACoB,MAAM;EACvC;AACF;AACA;AACA;EACEZ,sBAAsB,EAAER,SAAS,CAACoB,MAAM;EACxC;AACF;AACA;AACA;EACEb,uBAAuB,EAAEP,SAAS,CAACoB,MAAM;EACzC;AACF;AACA;AACA;EACER,WAAW,EAAEZ,SAAS,CAACqB,KAAK,CAAC,CAAC,MAAM,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;EACzD;AACF;AACA;AACA;EACEZ,cAAc,EAAET,SAAS,CAACoB,MAAM;EAChC;AACF;AACA;EACEL,KAAK,EAAEf,SAAS,CAACoB;AACnB,CAAC,GAAG,KAAK,CAAC;AACV,eAAeV,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}