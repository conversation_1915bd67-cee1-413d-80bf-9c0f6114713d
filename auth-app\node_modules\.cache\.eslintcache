[{"C:\\Users\\<USER>\\OneDrive\\Desktop\\Evolune\\auth-app\\src\\index.js": "1", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Evolune\\auth-app\\src\\App.js": "2", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Evolune\\auth-app\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Evolune\\auth-app\\src\\context\\AuthContext.js": "4", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Evolune\\auth-app\\src\\components\\Signup.js": "5", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Evolune\\auth-app\\src\\components\\Dashboard.js": "6", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Evolune\\auth-app\\src\\components\\Login.js": "7", "C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\auth-app\\src\\index.js": "8", "C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\auth-app\\src\\reportWebVitals.js": "9", "C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\auth-app\\src\\App.js": "10", "C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\auth-app\\src\\components\\Dashboard.js": "11", "C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\auth-app\\src\\components\\Login.js": "12", "C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\auth-app\\src\\components\\Signup.js": "13", "C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\auth-app\\src\\context\\AuthContext.js": "14"}, {"size": 535, "mtime": 1749131976575, "results": "15", "hashOfConfig": "16"}, {"size": 1726, "mtime": 1749136783235, "results": "17", "hashOfConfig": "16"}, {"size": 362, "mtime": 1749131976850, "results": "18", "hashOfConfig": "16"}, {"size": 3185, "mtime": 1749136877342, "results": "19", "hashOfConfig": "16"}, {"size": 5210, "mtime": 1749137436598, "results": "20", "hashOfConfig": "16"}, {"size": 1516, "mtime": 1749136960032, "results": "21", "hashOfConfig": "16"}, {"size": 4905, "mtime": 1749139734762, "results": "22", "hashOfConfig": "16"}, {"size": 535, "mtime": 1749131976000, "results": "23", "hashOfConfig": "24"}, {"size": 362, "mtime": 1749131976000, "results": "25", "hashOfConfig": "24"}, {"size": 1726, "mtime": 1749136782000, "results": "26", "hashOfConfig": "24"}, {"size": 1516, "mtime": 1749136960000, "results": "27", "hashOfConfig": "24"}, {"size": 4905, "mtime": 1749139734000, "results": "28", "hashOfConfig": "24"}, {"size": 5210, "mtime": 1749137436000, "results": "29", "hashOfConfig": "24"}, {"size": 3185, "mtime": 1749136876000, "results": "30", "hashOfConfig": "24"}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1efeboh", {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "zqfrl8", {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\Evolune\\auth-app\\src\\index.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Evolune\\auth-app\\src\\App.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Evolune\\auth-app\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Evolune\\auth-app\\src\\context\\AuthContext.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Evolune\\auth-app\\src\\components\\Signup.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Evolune\\auth-app\\src\\components\\Dashboard.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Evolune\\auth-app\\src\\components\\Login.js", ["73"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\auth-app\\src\\index.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\auth-app\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\auth-app\\src\\App.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\auth-app\\src\\components\\Dashboard.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\auth-app\\src\\components\\Login.js", ["74"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\auth-app\\src\\components\\Signup.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\AuthPages\\Evolune\\auth-app\\src\\context\\AuthContext.js", [], [], {"ruleId": "75", "severity": 1, "message": "76", "line": 12, "column": 20, "nodeType": "77", "messageId": "78", "endLine": 12, "endColumn": 30}, {"ruleId": "75", "severity": 1, "message": "76", "line": 12, "column": 20, "nodeType": "77", "messageId": "78", "endLine": 12, "endColumn": 30}, "no-unused-vars", "'GoogleIcon' is defined but never used.", "Identifier", "unusedVar"]