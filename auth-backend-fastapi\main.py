from fastapi import <PERSON><PERSON><PERSON>, HTT<PERSON>Exception, Depends, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from fastapi.middleware.cors import CORSMiddleware
from passlib.context import CryptContext
from jose import JWTError, jwt
from google.auth.transport import requests
from google.oauth2 import id_token
from datetime import datetime, timedelta
from typing import Optional
import asyncio

from config import settings
from database import db
from models import (
    UserRegister, UserLogin, GoogleLogin, 
    AuthResponse, UserResponse, MessageResponse
)

# Initialize FastAPI app
app = FastAPI(title="Auth API", version="1.0.0")

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],  # React app URL
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Security
security = HTTPBearer()
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# Utility functions
def verify_password(plain_password: str, hashed_password: str) -> bool:
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    return pwd_context.hash(password)

def create_access_token(data: dict) -> str:
    to_encode = data.copy()
    expire = datetime.utcnow() + timedelta(hours=settings.jwt_expiration_hours)
    to_encode.update({"exp": expire})
    return jwt.encode(to_encode, settings.jwt_secret, algorithm=settings.jwt_algorithm)

async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)) -> dict:
    try:
        payload = jwt.decode(credentials.credentials, settings.jwt_secret, algorithms=[settings.jwt_algorithm])
        user_id: int = payload.get("userId")
        if user_id is None:
            raise HTTPException(status_code=401, detail="Invalid token")
        
        user = await db.fetch_one("SELECT id, name, email FROM users WHERE id = $1", user_id)
        if user is None:
            raise HTTPException(status_code=401, detail="User not found")
        
        return user
    except JWTError:
        raise HTTPException(status_code=401, detail="Invalid token")

# Startup and shutdown events
@app.on_event("startup")
async def startup():
    await db.connect()

@app.on_event("shutdown")
async def shutdown():
    await db.disconnect()

# Routes
@app.post("/api/register", response_model=AuthResponse)
async def register(user_data: UserRegister):
    # Check if user already exists
    existing_user = await db.fetch_one("SELECT * FROM users WHERE email = $1", user_data.email)
    if existing_user:
        raise HTTPException(status_code=400, detail="User already exists")
    
    # Hash password
    hashed_password = get_password_hash(user_data.password)
    
    # Create user
    new_user = await db.fetch_one(
        "INSERT INTO users (name, email, password) VALUES ($1, $2, $3) RETURNING id, name, email",
        user_data.name, user_data.email, hashed_password
    )
    
    # Generate JWT token
    token = create_access_token({"userId": new_user["id"], "email": new_user["email"]})
    
    return AuthResponse(
        message="User created successfully",
        token=token,
        user=UserResponse(**new_user)
    )

@app.post("/api/login", response_model=AuthResponse)
async def login(user_data: UserLogin):
    # Check if user exists
    user = await db.fetch_one("SELECT * FROM users WHERE email = $1", user_data.email)
    if not user:
        raise HTTPException(status_code=400, detail="Invalid credentials")
    
    # Check password
    if not verify_password(user_data.password, user["password"]):
        raise HTTPException(status_code=400, detail="Invalid credentials")
    
    # Generate JWT token
    token = create_access_token({"userId": user["id"], "email": user["email"]})
    
    return AuthResponse(
        message="Login successful",
        token=token,
        user=UserResponse(id=user["id"], name=user["name"], email=user["email"])
    )

@app.post("/api/google-login", response_model=AuthResponse)
async def google_login(google_data: GoogleLogin):
    try:
        # Verify Google token
        idinfo = id_token.verify_oauth2_token(
            google_data.token,
            requests.Request(),
            settings.google_client_id
        )

        google_id = idinfo['sub']
        email = idinfo['email']
        name = idinfo['name']

        # Check if user exists
        user = await db.fetch_one(
            "SELECT * FROM users WHERE google_id = $1 OR email = $2",
            google_id, email
        )

        if not user:
            # Create new user
            user = await db.fetch_one(
                "INSERT INTO users (name, email, google_id) VALUES ($1, $2, $3) RETURNING id, name, email",
                name, email, google_id
            )
        else:
            # Update google_id if user exists but doesn't have it
            if not user.get("google_id"):
                await db.execute(
                    "UPDATE users SET google_id = $1 WHERE id = $2",
                    google_id, user["id"]
                )

        # Generate JWT token
        token = create_access_token({"userId": user["id"], "email": user["email"]})

        return AuthResponse(
            message="Google login successful",
            token=token,
            user=UserResponse(id=user["id"], name=user["name"], email=user["email"])
        )

    except ValueError as e:
        raise HTTPException(status_code=400, detail="Google authentication failed")

@app.get("/api/profile", response_model=UserResponse)
async def get_profile(current_user: dict = Depends(get_current_user)):
    return UserResponse(**current_user)

@app.get("/health")
async def health_check():
    return {"status": "healthy"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host=settings.host, port=settings.port)
