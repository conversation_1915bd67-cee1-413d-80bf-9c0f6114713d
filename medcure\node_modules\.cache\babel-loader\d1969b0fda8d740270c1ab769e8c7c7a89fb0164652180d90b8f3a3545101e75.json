{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport integerPropType from '@mui/utils/integerPropType';\nimport composeClasses from '@mui/utils/composeClasses';\nimport StepperContext from \"../Stepper/StepperContext.js\";\nimport StepContext from \"./StepContext.js\";\nimport { styled } from \"../zero-styled/index.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport { getStepUtilityClass } from \"./stepClasses.js\";\nimport { jsxs as _jsxs, jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    orientation,\n    alternativeLabel,\n    completed\n  } = ownerState;\n  const slots = {\n    root: ['root', orientation, alternativeLabel && 'alternativeLabel', completed && 'completed']\n  };\n  return composeClasses(slots, getStepUtilityClass, classes);\n};\nconst StepRoot = styled('div', {\n  name: 'MuiStep',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[ownerState.orientation], ownerState.alternativeLabel && styles.alternativeLabel, ownerState.completed && styles.completed];\n  }\n})({\n  variants: [{\n    props: {\n      orientation: 'horizontal'\n    },\n    style: {\n      paddingLeft: 8,\n      paddingRight: 8\n    }\n  }, {\n    props: {\n      alternativeLabel: true\n    },\n    style: {\n      flex: 1,\n      position: 'relative'\n    }\n  }]\n});\nconst Step = /*#__PURE__*/React.forwardRef(function Step(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiStep'\n  });\n  const {\n    active: activeProp,\n    children,\n    className,\n    component = 'div',\n    completed: completedProp,\n    disabled: disabledProp,\n    expanded = false,\n    index,\n    last,\n    ...other\n  } = props;\n  const {\n    activeStep,\n    connector,\n    alternativeLabel,\n    orientation,\n    nonLinear\n  } = React.useContext(StepperContext);\n  let [active = false, completed = false, disabled = false] = [activeProp, completedProp, disabledProp];\n  if (activeStep === index) {\n    active = activeProp !== undefined ? activeProp : true;\n  } else if (!nonLinear && activeStep > index) {\n    completed = completedProp !== undefined ? completedProp : true;\n  } else if (!nonLinear && activeStep < index) {\n    disabled = disabledProp !== undefined ? disabledProp : true;\n  }\n  const contextValue = React.useMemo(() => ({\n    index,\n    last,\n    expanded,\n    icon: index + 1,\n    active,\n    completed,\n    disabled\n  }), [index, last, expanded, active, completed, disabled]);\n  const ownerState = {\n    ...props,\n    active,\n    orientation,\n    alternativeLabel,\n    completed,\n    disabled,\n    expanded,\n    component\n  };\n  const classes = useUtilityClasses(ownerState);\n  const newChildren = /*#__PURE__*/_jsxs(StepRoot, {\n    as: component,\n    className: clsx(classes.root, className),\n    ref: ref,\n    ownerState: ownerState,\n    ...other,\n    children: [connector && alternativeLabel && index !== 0 ? connector : null, children]\n  });\n  return /*#__PURE__*/_jsx(StepContext.Provider, {\n    value: contextValue,\n    children: connector && !alternativeLabel && index !== 0 ? /*#__PURE__*/_jsxs(React.Fragment, {\n      children: [connector, newChildren]\n    }) : newChildren\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Step.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Sets the step as active. Is passed to child components.\n   */\n  active: PropTypes.bool,\n  /**\n   * Should be `Step` sub-components such as `StepLabel`, `StepContent`.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * Mark the step as completed. Is passed to child components.\n   */\n  completed: PropTypes.bool,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * If `true`, the step is disabled, will also disable the button if\n   * `StepButton` is a child of `Step`. Is passed to child components.\n   */\n  disabled: PropTypes.bool,\n  /**\n   * Expand the step.\n   * @default false\n   */\n  expanded: PropTypes.bool,\n  /**\n   * The position of the step.\n   * The prop defaults to the value inherited from the parent Stepper component.\n   */\n  index: integerPropType,\n  /**\n   * If `true`, the Step is displayed as rendered last.\n   * The prop defaults to the value inherited from the parent Stepper component.\n   */\n  last: PropTypes.bool,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default Step;", "map": {"version": 3, "names": ["React", "PropTypes", "clsx", "integerPropType", "composeClasses", "StepperContext", "StepContext", "styled", "useDefaultProps", "getStepUtilityClass", "jsxs", "_jsxs", "jsx", "_jsx", "useUtilityClasses", "ownerState", "classes", "orientation", "alternativeLabel", "completed", "slots", "root", "StepRoot", "name", "slot", "overridesResolver", "props", "styles", "variants", "style", "paddingLeft", "paddingRight", "flex", "position", "Step", "forwardRef", "inProps", "ref", "active", "activeProp", "children", "className", "component", "completedProp", "disabled", "disabledProp", "expanded", "index", "last", "other", "activeStep", "connector", "nonLinear", "useContext", "undefined", "contextValue", "useMemo", "icon", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "as", "Provider", "value", "Fragment", "process", "env", "NODE_ENV", "propTypes", "bool", "node", "object", "string", "elementType", "sx", "oneOfType", "arrayOf", "func"], "sources": ["C:/Users/<USER>/OneDrive/Documents/AuthPages/Evolune/medcure/node_modules/@mui/material/esm/Step/Step.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport integerPropType from '@mui/utils/integerPropType';\nimport composeClasses from '@mui/utils/composeClasses';\nimport StepperContext from \"../Stepper/StepperContext.js\";\nimport StepContext from \"./StepContext.js\";\nimport { styled } from \"../zero-styled/index.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport { getStepUtilityClass } from \"./stepClasses.js\";\nimport { jsxs as _jsxs, jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    orientation,\n    alternativeLabel,\n    completed\n  } = ownerState;\n  const slots = {\n    root: ['root', orientation, alternativeLabel && 'alternativeLabel', completed && 'completed']\n  };\n  return composeClasses(slots, getStepUtilityClass, classes);\n};\nconst StepRoot = styled('div', {\n  name: 'MuiStep',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[ownerState.orientation], ownerState.alternativeLabel && styles.alternativeLabel, ownerState.completed && styles.completed];\n  }\n})({\n  variants: [{\n    props: {\n      orientation: 'horizontal'\n    },\n    style: {\n      paddingLeft: 8,\n      paddingRight: 8\n    }\n  }, {\n    props: {\n      alternativeLabel: true\n    },\n    style: {\n      flex: 1,\n      position: 'relative'\n    }\n  }]\n});\nconst Step = /*#__PURE__*/React.forwardRef(function Step(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiStep'\n  });\n  const {\n    active: activeProp,\n    children,\n    className,\n    component = 'div',\n    completed: completedProp,\n    disabled: disabledProp,\n    expanded = false,\n    index,\n    last,\n    ...other\n  } = props;\n  const {\n    activeStep,\n    connector,\n    alternativeLabel,\n    orientation,\n    nonLinear\n  } = React.useContext(StepperContext);\n  let [active = false, completed = false, disabled = false] = [activeProp, completedProp, disabledProp];\n  if (activeStep === index) {\n    active = activeProp !== undefined ? activeProp : true;\n  } else if (!nonLinear && activeStep > index) {\n    completed = completedProp !== undefined ? completedProp : true;\n  } else if (!nonLinear && activeStep < index) {\n    disabled = disabledProp !== undefined ? disabledProp : true;\n  }\n  const contextValue = React.useMemo(() => ({\n    index,\n    last,\n    expanded,\n    icon: index + 1,\n    active,\n    completed,\n    disabled\n  }), [index, last, expanded, active, completed, disabled]);\n  const ownerState = {\n    ...props,\n    active,\n    orientation,\n    alternativeLabel,\n    completed,\n    disabled,\n    expanded,\n    component\n  };\n  const classes = useUtilityClasses(ownerState);\n  const newChildren = /*#__PURE__*/_jsxs(StepRoot, {\n    as: component,\n    className: clsx(classes.root, className),\n    ref: ref,\n    ownerState: ownerState,\n    ...other,\n    children: [connector && alternativeLabel && index !== 0 ? connector : null, children]\n  });\n  return /*#__PURE__*/_jsx(StepContext.Provider, {\n    value: contextValue,\n    children: connector && !alternativeLabel && index !== 0 ? /*#__PURE__*/_jsxs(React.Fragment, {\n      children: [connector, newChildren]\n    }) : newChildren\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Step.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Sets the step as active. Is passed to child components.\n   */\n  active: PropTypes.bool,\n  /**\n   * Should be `Step` sub-components such as `StepLabel`, `StepContent`.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * Mark the step as completed. Is passed to child components.\n   */\n  completed: PropTypes.bool,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * If `true`, the step is disabled, will also disable the button if\n   * `StepButton` is a child of `Step`. Is passed to child components.\n   */\n  disabled: PropTypes.bool,\n  /**\n   * Expand the step.\n   * @default false\n   */\n  expanded: PropTypes.bool,\n  /**\n   * The position of the step.\n   * The prop defaults to the value inherited from the parent Stepper component.\n   */\n  index: integerPropType,\n  /**\n   * If `true`, the Step is displayed as rendered last.\n   * The prop defaults to the value inherited from the parent Stepper component.\n   */\n  last: PropTypes.bool,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default Step;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,eAAe,MAAM,4BAA4B;AACxD,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAOC,WAAW,MAAM,kBAAkB;AAC1C,SAASC,MAAM,QAAQ,yBAAyB;AAChD,SAASC,eAAe,QAAQ,kCAAkC;AAClE,SAASC,mBAAmB,QAAQ,kBAAkB;AACtD,SAASC,IAAI,IAAIC,KAAK,EAAEC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC9D,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,OAAO;IACPC,WAAW;IACXC,gBAAgB;IAChBC;EACF,CAAC,GAAGJ,UAAU;EACd,MAAMK,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAEJ,WAAW,EAAEC,gBAAgB,IAAI,kBAAkB,EAAEC,SAAS,IAAI,WAAW;EAC9F,CAAC;EACD,OAAOf,cAAc,CAACgB,KAAK,EAAEX,mBAAmB,EAAEO,OAAO,CAAC;AAC5D,CAAC;AACD,MAAMM,QAAQ,GAAGf,MAAM,CAAC,KAAK,EAAE;EAC7BgB,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJZ;IACF,CAAC,GAAGW,KAAK;IACT,OAAO,CAACC,MAAM,CAACN,IAAI,EAAEM,MAAM,CAACZ,UAAU,CAACE,WAAW,CAAC,EAAEF,UAAU,CAACG,gBAAgB,IAAIS,MAAM,CAACT,gBAAgB,EAAEH,UAAU,CAACI,SAAS,IAAIQ,MAAM,CAACR,SAAS,CAAC;EACxJ;AACF,CAAC,CAAC,CAAC;EACDS,QAAQ,EAAE,CAAC;IACTF,KAAK,EAAE;MACLT,WAAW,EAAE;IACf,CAAC;IACDY,KAAK,EAAE;MACLC,WAAW,EAAE,CAAC;MACdC,YAAY,EAAE;IAChB;EACF,CAAC,EAAE;IACDL,KAAK,EAAE;MACLR,gBAAgB,EAAE;IACpB,CAAC;IACDW,KAAK,EAAE;MACLG,IAAI,EAAE,CAAC;MACPC,QAAQ,EAAE;IACZ;EACF,CAAC;AACH,CAAC,CAAC;AACF,MAAMC,IAAI,GAAG,aAAalC,KAAK,CAACmC,UAAU,CAAC,SAASD,IAAIA,CAACE,OAAO,EAAEC,GAAG,EAAE;EACrE,MAAMX,KAAK,GAAGlB,eAAe,CAAC;IAC5BkB,KAAK,EAAEU,OAAO;IACdb,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;IACJe,MAAM,EAAEC,UAAU;IAClBC,QAAQ;IACRC,SAAS;IACTC,SAAS,GAAG,KAAK;IACjBvB,SAAS,EAAEwB,aAAa;IACxBC,QAAQ,EAAEC,YAAY;IACtBC,QAAQ,GAAG,KAAK;IAChBC,KAAK;IACLC,IAAI;IACJ,GAAGC;EACL,CAAC,GAAGvB,KAAK;EACT,MAAM;IACJwB,UAAU;IACVC,SAAS;IACTjC,gBAAgB;IAChBD,WAAW;IACXmC;EACF,CAAC,GAAGpD,KAAK,CAACqD,UAAU,CAAChD,cAAc,CAAC;EACpC,IAAI,CAACiC,MAAM,GAAG,KAAK,EAAEnB,SAAS,GAAG,KAAK,EAAEyB,QAAQ,GAAG,KAAK,CAAC,GAAG,CAACL,UAAU,EAAEI,aAAa,EAAEE,YAAY,CAAC;EACrG,IAAIK,UAAU,KAAKH,KAAK,EAAE;IACxBT,MAAM,GAAGC,UAAU,KAAKe,SAAS,GAAGf,UAAU,GAAG,IAAI;EACvD,CAAC,MAAM,IAAI,CAACa,SAAS,IAAIF,UAAU,GAAGH,KAAK,EAAE;IAC3C5B,SAAS,GAAGwB,aAAa,KAAKW,SAAS,GAAGX,aAAa,GAAG,IAAI;EAChE,CAAC,MAAM,IAAI,CAACS,SAAS,IAAIF,UAAU,GAAGH,KAAK,EAAE;IAC3CH,QAAQ,GAAGC,YAAY,KAAKS,SAAS,GAAGT,YAAY,GAAG,IAAI;EAC7D;EACA,MAAMU,YAAY,GAAGvD,KAAK,CAACwD,OAAO,CAAC,OAAO;IACxCT,KAAK;IACLC,IAAI;IACJF,QAAQ;IACRW,IAAI,EAAEV,KAAK,GAAG,CAAC;IACfT,MAAM;IACNnB,SAAS;IACTyB;EACF,CAAC,CAAC,EAAE,CAACG,KAAK,EAAEC,IAAI,EAAEF,QAAQ,EAAER,MAAM,EAAEnB,SAAS,EAAEyB,QAAQ,CAAC,CAAC;EACzD,MAAM7B,UAAU,GAAG;IACjB,GAAGW,KAAK;IACRY,MAAM;IACNrB,WAAW;IACXC,gBAAgB;IAChBC,SAAS;IACTyB,QAAQ;IACRE,QAAQ;IACRJ;EACF,CAAC;EACD,MAAM1B,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAM2C,WAAW,GAAG,aAAa/C,KAAK,CAACW,QAAQ,EAAE;IAC/CqC,EAAE,EAAEjB,SAAS;IACbD,SAAS,EAAEvC,IAAI,CAACc,OAAO,CAACK,IAAI,EAAEoB,SAAS,CAAC;IACxCJ,GAAG,EAAEA,GAAG;IACRtB,UAAU,EAAEA,UAAU;IACtB,GAAGkC,KAAK;IACRT,QAAQ,EAAE,CAACW,SAAS,IAAIjC,gBAAgB,IAAI6B,KAAK,KAAK,CAAC,GAAGI,SAAS,GAAG,IAAI,EAAEX,QAAQ;EACtF,CAAC,CAAC;EACF,OAAO,aAAa3B,IAAI,CAACP,WAAW,CAACsD,QAAQ,EAAE;IAC7CC,KAAK,EAAEN,YAAY;IACnBf,QAAQ,EAAEW,SAAS,IAAI,CAACjC,gBAAgB,IAAI6B,KAAK,KAAK,CAAC,GAAG,aAAapC,KAAK,CAACX,KAAK,CAAC8D,QAAQ,EAAE;MAC3FtB,QAAQ,EAAE,CAACW,SAAS,EAAEO,WAAW;IACnC,CAAC,CAAC,GAAGA;EACP,CAAC,CAAC;AACJ,CAAC,CAAC;AACFK,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG/B,IAAI,CAACgC,SAAS,CAAC,yBAAyB;EAC9E;EACA;EACA;EACA;EACA;AACF;AACA;EACE5B,MAAM,EAAErC,SAAS,CAACkE,IAAI;EACtB;AACF;AACA;EACE3B,QAAQ,EAAEvC,SAAS,CAACmE,IAAI;EACxB;AACF;AACA;EACEpD,OAAO,EAAEf,SAAS,CAACoE,MAAM;EACzB;AACF;AACA;EACE5B,SAAS,EAAExC,SAAS,CAACqE,MAAM;EAC3B;AACF;AACA;EACEnD,SAAS,EAAElB,SAAS,CAACkE,IAAI;EACzB;AACF;AACA;AACA;EACEzB,SAAS,EAAEzC,SAAS,CAACsE,WAAW;EAChC;AACF;AACA;AACA;EACE3B,QAAQ,EAAE3C,SAAS,CAACkE,IAAI;EACxB;AACF;AACA;AACA;EACErB,QAAQ,EAAE7C,SAAS,CAACkE,IAAI;EACxB;AACF;AACA;AACA;EACEpB,KAAK,EAAE5C,eAAe;EACtB;AACF;AACA;AACA;EACE6C,IAAI,EAAE/C,SAAS,CAACkE,IAAI;EACpB;AACF;AACA;EACEK,EAAE,EAAEvE,SAAS,CAACwE,SAAS,CAAC,CAACxE,SAAS,CAACyE,OAAO,CAACzE,SAAS,CAACwE,SAAS,CAAC,CAACxE,SAAS,CAAC0E,IAAI,EAAE1E,SAAS,CAACoE,MAAM,EAAEpE,SAAS,CAACkE,IAAI,CAAC,CAAC,CAAC,EAAElE,SAAS,CAAC0E,IAAI,EAAE1E,SAAS,CAACoE,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACV,eAAenC,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}