from pydantic import BaseModel, EmailStr
from typing import Optional
from datetime import datetime

# Request Models
class UserRegister(BaseModel):
    name: str
    email: EmailStr
    password: str

class UserLogin(BaseModel):
    email: EmailStr
    password: str

class GoogleLogin(BaseModel):
    token: str

# Response Models
class UserResponse(BaseModel):
    id: int
    name: str
    email: str

class AuthResponse(BaseModel):
    message: str
    token: str
    user: UserResponse

class MessageResponse(BaseModel):
    message: str

# Database Models (for reference)
class User:
    def __init__(self, id: int, name: str, email: str, password: Optional[str] = None, google_id: Optional[str] = None):
        self.id = id
        self.name = name
        self.email = email
        self.password = password
        self.google_id = google_id
