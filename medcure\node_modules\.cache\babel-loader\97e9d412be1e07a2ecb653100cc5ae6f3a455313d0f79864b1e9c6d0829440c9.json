{"ast": null, "code": "'use client';\n\nimport useEnhancedEffect from '@mui/utils/useEnhancedEffect';\nexport default useEnhancedEffect;", "map": {"version": 3, "names": ["useEnhancedEffect"], "sources": ["C:/Users/<USER>/OneDrive/Documents/AuthPages/Evolune/medcure/node_modules/@mui/material/esm/utils/useEnhancedEffect.js"], "sourcesContent": ["'use client';\n\nimport useEnhancedEffect from '@mui/utils/useEnhancedEffect';\nexport default useEnhancedEffect;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,iBAAiB,MAAM,8BAA8B;AAC5D,eAAeA,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}