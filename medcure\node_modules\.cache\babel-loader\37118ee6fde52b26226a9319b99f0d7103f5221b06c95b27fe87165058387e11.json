{"ast": null, "code": "'use strict';\n\nvar globalThis = require('../internals/global-this');\nvar apply = require('../internals/function-apply');\nvar uncurryThis = require('../internals/function-uncurry-this-clause');\nvar isCallable = require('../internals/is-callable');\nvar getOwnPropertyDescriptor = require('../internals/object-get-own-property-descriptor').f;\nvar isForced = require('../internals/is-forced');\nvar path = require('../internals/path');\nvar bind = require('../internals/function-bind-context');\nvar createNonEnumerableProperty = require('../internals/create-non-enumerable-property');\nvar hasOwn = require('../internals/has-own-property');\n// add debugging info\nrequire('../internals/shared-store');\nvar wrapConstructor = function (NativeConstructor) {\n  var Wrapper = function (a, b, c) {\n    if (this instanceof Wrapper) {\n      switch (arguments.length) {\n        case 0:\n          return new NativeConstructor();\n        case 1:\n          return new NativeConstructor(a);\n        case 2:\n          return new NativeConstructor(a, b);\n      }\n      return new NativeConstructor(a, b, c);\n    }\n    return apply(NativeConstructor, this, arguments);\n  };\n  Wrapper.prototype = NativeConstructor.prototype;\n  return Wrapper;\n};\n\n/*\n  options.target         - name of the target object\n  options.global         - target is the global object\n  options.stat           - export as static methods of target\n  options.proto          - export as prototype methods of target\n  options.real           - real prototype method for the `pure` version\n  options.forced         - export even if the native feature is available\n  options.bind           - bind methods to the target, required for the `pure` version\n  options.wrap           - wrap constructors to preventing global pollution, required for the `pure` version\n  options.unsafe         - use the simple assignment of property instead of delete + defineProperty\n  options.sham           - add a flag to not completely full polyfills\n  options.enumerable     - export as enumerable property\n  options.dontCallGetSet - prevent calling a getter on target\n  options.name           - the .name of the function if it does not match the key\n*/\nmodule.exports = function (options, source) {\n  var TARGET = options.target;\n  var GLOBAL = options.global;\n  var STATIC = options.stat;\n  var PROTO = options.proto;\n  var nativeSource = GLOBAL ? globalThis : STATIC ? globalThis[TARGET] : globalThis[TARGET] && globalThis[TARGET].prototype;\n  var target = GLOBAL ? path : path[TARGET] || createNonEnumerableProperty(path, TARGET, {})[TARGET];\n  var targetPrototype = target.prototype;\n  var FORCED, USE_NATIVE, VIRTUAL_PROTOTYPE;\n  var key, sourceProperty, targetProperty, nativeProperty, resultProperty, descriptor;\n  for (key in source) {\n    FORCED = isForced(GLOBAL ? key : TARGET + (STATIC ? '.' : '#') + key, options.forced);\n    // contains in native\n    USE_NATIVE = !FORCED && nativeSource && hasOwn(nativeSource, key);\n    targetProperty = target[key];\n    if (USE_NATIVE) if (options.dontCallGetSet) {\n      descriptor = getOwnPropertyDescriptor(nativeSource, key);\n      nativeProperty = descriptor && descriptor.value;\n    } else nativeProperty = nativeSource[key];\n\n    // export native or implementation\n    sourceProperty = USE_NATIVE && nativeProperty ? nativeProperty : source[key];\n    if (!FORCED && !PROTO && typeof targetProperty == typeof sourceProperty) continue;\n\n    // bind methods to global for calling from export context\n    if (options.bind && USE_NATIVE) resultProperty = bind(sourceProperty, globalThis);\n    // wrap global constructors for prevent changes in this version\n    else if (options.wrap && USE_NATIVE) resultProperty = wrapConstructor(sourceProperty);\n    // make static versions for prototype methods\n    else if (PROTO && isCallable(sourceProperty)) resultProperty = uncurryThis(sourceProperty);\n    // default case\n    else resultProperty = sourceProperty;\n\n    // add a flag to not completely full polyfills\n    if (options.sham || sourceProperty && sourceProperty.sham || targetProperty && targetProperty.sham) {\n      createNonEnumerableProperty(resultProperty, 'sham', true);\n    }\n    createNonEnumerableProperty(target, key, resultProperty);\n    if (PROTO) {\n      VIRTUAL_PROTOTYPE = TARGET + 'Prototype';\n      if (!hasOwn(path, VIRTUAL_PROTOTYPE)) {\n        createNonEnumerableProperty(path, VIRTUAL_PROTOTYPE, {});\n      }\n      // export virtual prototype methods\n      createNonEnumerableProperty(path[VIRTUAL_PROTOTYPE], key, sourceProperty);\n      // export real prototype methods\n      if (options.real && targetPrototype && (FORCED || !targetPrototype[key])) {\n        createNonEnumerableProperty(targetPrototype, key, sourceProperty);\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["globalThis", "require", "apply", "uncurryThis", "isCallable", "getOwnPropertyDescriptor", "f", "isForced", "path", "bind", "createNonEnumerableProperty", "hasOwn", "wrapConstructor", "NativeConstructor", "Wrapper", "a", "b", "c", "arguments", "length", "prototype", "module", "exports", "options", "source", "TARGET", "target", "GLOBAL", "global", "STATIC", "stat", "PROTO", "proto", "nativeSource", "targetPrototype", "FORCED", "USE_NATIVE", "VIRTUAL_PROTOTYPE", "key", "sourceProperty", "targetProperty", "nativeProperty", "resultProperty", "descriptor", "forced", "dontCallGetSet", "value", "wrap", "sham", "real"], "sources": ["C:/Users/<USER>/OneDrive/Documents/AuthPages/Evolune/medcure/node_modules/core-js-pure/internals/export.js"], "sourcesContent": ["'use strict';\nvar globalThis = require('../internals/global-this');\nvar apply = require('../internals/function-apply');\nvar uncurryThis = require('../internals/function-uncurry-this-clause');\nvar isCallable = require('../internals/is-callable');\nvar getOwnPropertyDescriptor = require('../internals/object-get-own-property-descriptor').f;\nvar isForced = require('../internals/is-forced');\nvar path = require('../internals/path');\nvar bind = require('../internals/function-bind-context');\nvar createNonEnumerableProperty = require('../internals/create-non-enumerable-property');\nvar hasOwn = require('../internals/has-own-property');\n// add debugging info\nrequire('../internals/shared-store');\n\nvar wrapConstructor = function (NativeConstructor) {\n  var Wrapper = function (a, b, c) {\n    if (this instanceof Wrapper) {\n      switch (arguments.length) {\n        case 0: return new NativeConstructor();\n        case 1: return new NativeConstructor(a);\n        case 2: return new NativeConstructor(a, b);\n      } return new NativeConstructor(a, b, c);\n    } return apply(NativeConstructor, this, arguments);\n  };\n  Wrapper.prototype = NativeConstructor.prototype;\n  return Wrapper;\n};\n\n/*\n  options.target         - name of the target object\n  options.global         - target is the global object\n  options.stat           - export as static methods of target\n  options.proto          - export as prototype methods of target\n  options.real           - real prototype method for the `pure` version\n  options.forced         - export even if the native feature is available\n  options.bind           - bind methods to the target, required for the `pure` version\n  options.wrap           - wrap constructors to preventing global pollution, required for the `pure` version\n  options.unsafe         - use the simple assignment of property instead of delete + defineProperty\n  options.sham           - add a flag to not completely full polyfills\n  options.enumerable     - export as enumerable property\n  options.dontCallGetSet - prevent calling a getter on target\n  options.name           - the .name of the function if it does not match the key\n*/\nmodule.exports = function (options, source) {\n  var TARGET = options.target;\n  var GLOBAL = options.global;\n  var STATIC = options.stat;\n  var PROTO = options.proto;\n\n  var nativeSource = GLOBAL ? globalThis : STATIC ? globalThis[TARGET] : globalThis[TARGET] && globalThis[TARGET].prototype;\n\n  var target = GLOBAL ? path : path[TARGET] || createNonEnumerableProperty(path, TARGET, {})[TARGET];\n  var targetPrototype = target.prototype;\n\n  var FORCED, USE_NATIVE, VIRTUAL_PROTOTYPE;\n  var key, sourceProperty, targetProperty, nativeProperty, resultProperty, descriptor;\n\n  for (key in source) {\n    FORCED = isForced(GLOBAL ? key : TARGET + (STATIC ? '.' : '#') + key, options.forced);\n    // contains in native\n    USE_NATIVE = !FORCED && nativeSource && hasOwn(nativeSource, key);\n\n    targetProperty = target[key];\n\n    if (USE_NATIVE) if (options.dontCallGetSet) {\n      descriptor = getOwnPropertyDescriptor(nativeSource, key);\n      nativeProperty = descriptor && descriptor.value;\n    } else nativeProperty = nativeSource[key];\n\n    // export native or implementation\n    sourceProperty = (USE_NATIVE && nativeProperty) ? nativeProperty : source[key];\n\n    if (!FORCED && !PROTO && typeof targetProperty == typeof sourceProperty) continue;\n\n    // bind methods to global for calling from export context\n    if (options.bind && USE_NATIVE) resultProperty = bind(sourceProperty, globalThis);\n    // wrap global constructors for prevent changes in this version\n    else if (options.wrap && USE_NATIVE) resultProperty = wrapConstructor(sourceProperty);\n    // make static versions for prototype methods\n    else if (PROTO && isCallable(sourceProperty)) resultProperty = uncurryThis(sourceProperty);\n    // default case\n    else resultProperty = sourceProperty;\n\n    // add a flag to not completely full polyfills\n    if (options.sham || (sourceProperty && sourceProperty.sham) || (targetProperty && targetProperty.sham)) {\n      createNonEnumerableProperty(resultProperty, 'sham', true);\n    }\n\n    createNonEnumerableProperty(target, key, resultProperty);\n\n    if (PROTO) {\n      VIRTUAL_PROTOTYPE = TARGET + 'Prototype';\n      if (!hasOwn(path, VIRTUAL_PROTOTYPE)) {\n        createNonEnumerableProperty(path, VIRTUAL_PROTOTYPE, {});\n      }\n      // export virtual prototype methods\n      createNonEnumerableProperty(path[VIRTUAL_PROTOTYPE], key, sourceProperty);\n      // export real prototype methods\n      if (options.real && targetPrototype && (FORCED || !targetPrototype[key])) {\n        createNonEnumerableProperty(targetPrototype, key, sourceProperty);\n      }\n    }\n  }\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,UAAU,GAAGC,OAAO,CAAC,0BAA0B,CAAC;AACpD,IAAIC,KAAK,GAAGD,OAAO,CAAC,6BAA6B,CAAC;AAClD,IAAIE,WAAW,GAAGF,OAAO,CAAC,2CAA2C,CAAC;AACtE,IAAIG,UAAU,GAAGH,OAAO,CAAC,0BAA0B,CAAC;AACpD,IAAII,wBAAwB,GAAGJ,OAAO,CAAC,iDAAiD,CAAC,CAACK,CAAC;AAC3F,IAAIC,QAAQ,GAAGN,OAAO,CAAC,wBAAwB,CAAC;AAChD,IAAIO,IAAI,GAAGP,OAAO,CAAC,mBAAmB,CAAC;AACvC,IAAIQ,IAAI,GAAGR,OAAO,CAAC,oCAAoC,CAAC;AACxD,IAAIS,2BAA2B,GAAGT,OAAO,CAAC,6CAA6C,CAAC;AACxF,IAAIU,MAAM,GAAGV,OAAO,CAAC,+BAA+B,CAAC;AACrD;AACAA,OAAO,CAAC,2BAA2B,CAAC;AAEpC,IAAIW,eAAe,GAAG,SAAAA,CAAUC,iBAAiB,EAAE;EACjD,IAAIC,OAAO,GAAG,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;IAC/B,IAAI,IAAI,YAAYH,OAAO,EAAE;MAC3B,QAAQI,SAAS,CAACC,MAAM;QACtB,KAAK,CAAC;UAAE,OAAO,IAAIN,iBAAiB,CAAC,CAAC;QACtC,KAAK,CAAC;UAAE,OAAO,IAAIA,iBAAiB,CAACE,CAAC,CAAC;QACvC,KAAK,CAAC;UAAE,OAAO,IAAIF,iBAAiB,CAACE,CAAC,EAAEC,CAAC,CAAC;MAC5C;MAAE,OAAO,IAAIH,iBAAiB,CAACE,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;IACzC;IAAE,OAAOf,KAAK,CAACW,iBAAiB,EAAE,IAAI,EAAEK,SAAS,CAAC;EACpD,CAAC;EACDJ,OAAO,CAACM,SAAS,GAAGP,iBAAiB,CAACO,SAAS;EAC/C,OAAON,OAAO;AAChB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAO,MAAM,CAACC,OAAO,GAAG,UAAUC,OAAO,EAAEC,MAAM,EAAE;EAC1C,IAAIC,MAAM,GAAGF,OAAO,CAACG,MAAM;EAC3B,IAAIC,MAAM,GAAGJ,OAAO,CAACK,MAAM;EAC3B,IAAIC,MAAM,GAAGN,OAAO,CAACO,IAAI;EACzB,IAAIC,KAAK,GAAGR,OAAO,CAACS,KAAK;EAEzB,IAAIC,YAAY,GAAGN,MAAM,GAAG3B,UAAU,GAAG6B,MAAM,GAAG7B,UAAU,CAACyB,MAAM,CAAC,GAAGzB,UAAU,CAACyB,MAAM,CAAC,IAAIzB,UAAU,CAACyB,MAAM,CAAC,CAACL,SAAS;EAEzH,IAAIM,MAAM,GAAGC,MAAM,GAAGnB,IAAI,GAAGA,IAAI,CAACiB,MAAM,CAAC,IAAIf,2BAA2B,CAACF,IAAI,EAAEiB,MAAM,EAAE,CAAC,CAAC,CAAC,CAACA,MAAM,CAAC;EAClG,IAAIS,eAAe,GAAGR,MAAM,CAACN,SAAS;EAEtC,IAAIe,MAAM,EAAEC,UAAU,EAAEC,iBAAiB;EACzC,IAAIC,GAAG,EAAEC,cAAc,EAAEC,cAAc,EAAEC,cAAc,EAAEC,cAAc,EAAEC,UAAU;EAEnF,KAAKL,GAAG,IAAId,MAAM,EAAE;IAClBW,MAAM,GAAG5B,QAAQ,CAACoB,MAAM,GAAGW,GAAG,GAAGb,MAAM,IAAII,MAAM,GAAG,GAAG,GAAG,GAAG,CAAC,GAAGS,GAAG,EAAEf,OAAO,CAACqB,MAAM,CAAC;IACrF;IACAR,UAAU,GAAG,CAACD,MAAM,IAAIF,YAAY,IAAItB,MAAM,CAACsB,YAAY,EAAEK,GAAG,CAAC;IAEjEE,cAAc,GAAGd,MAAM,CAACY,GAAG,CAAC;IAE5B,IAAIF,UAAU,EAAE,IAAIb,OAAO,CAACsB,cAAc,EAAE;MAC1CF,UAAU,GAAGtC,wBAAwB,CAAC4B,YAAY,EAAEK,GAAG,CAAC;MACxDG,cAAc,GAAGE,UAAU,IAAIA,UAAU,CAACG,KAAK;IACjD,CAAC,MAAML,cAAc,GAAGR,YAAY,CAACK,GAAG,CAAC;;IAEzC;IACAC,cAAc,GAAIH,UAAU,IAAIK,cAAc,GAAIA,cAAc,GAAGjB,MAAM,CAACc,GAAG,CAAC;IAE9E,IAAI,CAACH,MAAM,IAAI,CAACJ,KAAK,IAAI,OAAOS,cAAc,IAAI,OAAOD,cAAc,EAAE;;IAEzE;IACA,IAAIhB,OAAO,CAACd,IAAI,IAAI2B,UAAU,EAAEM,cAAc,GAAGjC,IAAI,CAAC8B,cAAc,EAAEvC,UAAU,CAAC;IACjF;IAAA,KACK,IAAIuB,OAAO,CAACwB,IAAI,IAAIX,UAAU,EAAEM,cAAc,GAAG9B,eAAe,CAAC2B,cAAc,CAAC;IACrF;IAAA,KACK,IAAIR,KAAK,IAAI3B,UAAU,CAACmC,cAAc,CAAC,EAAEG,cAAc,GAAGvC,WAAW,CAACoC,cAAc,CAAC;IAC1F;IAAA,KACKG,cAAc,GAAGH,cAAc;;IAEpC;IACA,IAAIhB,OAAO,CAACyB,IAAI,IAAKT,cAAc,IAAIA,cAAc,CAACS,IAAK,IAAKR,cAAc,IAAIA,cAAc,CAACQ,IAAK,EAAE;MACtGtC,2BAA2B,CAACgC,cAAc,EAAE,MAAM,EAAE,IAAI,CAAC;IAC3D;IAEAhC,2BAA2B,CAACgB,MAAM,EAAEY,GAAG,EAAEI,cAAc,CAAC;IAExD,IAAIX,KAAK,EAAE;MACTM,iBAAiB,GAAGZ,MAAM,GAAG,WAAW;MACxC,IAAI,CAACd,MAAM,CAACH,IAAI,EAAE6B,iBAAiB,CAAC,EAAE;QACpC3B,2BAA2B,CAACF,IAAI,EAAE6B,iBAAiB,EAAE,CAAC,CAAC,CAAC;MAC1D;MACA;MACA3B,2BAA2B,CAACF,IAAI,CAAC6B,iBAAiB,CAAC,EAAEC,GAAG,EAAEC,cAAc,CAAC;MACzE;MACA,IAAIhB,OAAO,CAAC0B,IAAI,IAAIf,eAAe,KAAKC,MAAM,IAAI,CAACD,eAAe,CAACI,GAAG,CAAC,CAAC,EAAE;QACxE5B,2BAA2B,CAACwB,eAAe,EAAEI,GAAG,EAAEC,cAAc,CAAC;MACnE;IACF;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}