{"ast": null, "code": "'use client';\n\nimport getThemeProps from \"./getThemeProps.js\";\nimport useTheme from \"../useTheme/index.js\";\nexport default function useThemeProps({\n  props,\n  name,\n  defaultTheme,\n  themeId\n}) {\n  let theme = useTheme(defaultTheme);\n  if (themeId) {\n    theme = theme[themeId] || theme;\n  }\n  return getThemeProps({\n    theme,\n    name,\n    props\n  });\n}", "map": {"version": 3, "names": ["getThemeProps", "useTheme", "useThemeProps", "props", "name", "defaultTheme", "themeId", "theme"], "sources": ["C:/Users/<USER>/OneDrive/Documents/AuthPages/Evolune/medcure/node_modules/@mui/system/esm/useThemeProps/useThemeProps.js"], "sourcesContent": ["'use client';\n\nimport getThemeProps from \"./getThemeProps.js\";\nimport useTheme from \"../useTheme/index.js\";\nexport default function useThemeProps({\n  props,\n  name,\n  defaultTheme,\n  themeId\n}) {\n  let theme = useTheme(defaultTheme);\n  if (themeId) {\n    theme = theme[themeId] || theme;\n  }\n  return getThemeProps({\n    theme,\n    name,\n    props\n  });\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,aAAa,MAAM,oBAAoB;AAC9C,OAAOC,QAAQ,MAAM,sBAAsB;AAC3C,eAAe,SAASC,aAAaA,CAAC;EACpCC,KAAK;EACLC,IAAI;EACJC,YAAY;EACZC;AACF,CAAC,EAAE;EACD,IAAIC,KAAK,GAAGN,QAAQ,CAACI,YAAY,CAAC;EAClC,IAAIC,OAAO,EAAE;IACXC,KAAK,GAAGA,KAAK,CAACD,OAAO,CAAC,IAAIC,KAAK;EACjC;EACA,OAAOP,aAAa,CAAC;IACnBO,KAAK;IACLH,IAAI;IACJD;EACF,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}