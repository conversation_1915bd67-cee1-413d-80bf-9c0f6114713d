{"ast": null, "code": "export { default } from \"./createTheme.js\";\nexport { default as private_createBreakpoints } from \"../createBreakpoints/createBreakpoints.js\";\nexport { default as unstable_applyStyles } from \"./applyStyles.js\";", "map": {"version": 3, "names": ["default", "private_createBreakpoints", "unstable_applyStyles"], "sources": ["C:/Users/<USER>/OneDrive/Documents/AuthPages/Evolune/medcure/node_modules/@mui/system/esm/createTheme/index.js"], "sourcesContent": ["export { default } from \"./createTheme.js\";\nexport { default as private_createBreakpoints } from \"../createBreakpoints/createBreakpoints.js\";\nexport { default as unstable_applyStyles } from \"./applyStyles.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,kBAAkB;AAC1C,SAASA,OAAO,IAAIC,yBAAyB,QAAQ,2CAA2C;AAChG,SAASD,OAAO,IAAIE,oBAAoB,QAAQ,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}