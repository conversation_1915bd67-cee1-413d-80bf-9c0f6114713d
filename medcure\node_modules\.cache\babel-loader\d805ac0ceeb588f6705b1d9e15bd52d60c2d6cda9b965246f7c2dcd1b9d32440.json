{"ast": null, "code": "import axios from 'axios';\n\n// API Configuration\nconst AUTH_API_BASE_URL = process.env.REACT_APP_AUTH_API_URL || 'http://localhost:8000'; // auth-backend-fastapi\nconst MEDICAL_API_BASE_URL = process.env.REACT_APP_MEDICAL_API_URL || 'http://localhost:8001'; // medcure-backend\n\n// Create axios instances for different backends\nconst authAPI = axios.create({\n  baseURL: AUTH_API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\nconst medicalAPI = axios.create({\n  baseURL: MEDICAL_API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Add request interceptors to include auth token\nconst addAuthInterceptor = apiInstance => {\n  apiInstance.interceptors.request.use(config => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  }, error => {\n    return Promise.reject(error);\n  });\n};\n\n// Add auth interceptors to both API instances\naddAuthInterceptor(authAPI);\naddAuthInterceptor(medicalAPI);\n\n// Authentication API calls (auth-backend-fastapi)\nexport const authService = {\n  // User registration\n  register: async userData => {\n    try {\n      const response = await authAPI.post('/api/register', userData);\n      const {\n        token,\n        user\n      } = response.data;\n      localStorage.setItem('token', token);\n      localStorage.setItem('user', JSON.stringify(user));\n\n      // Set default headers for future requests\n      authAPI.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n      medicalAPI.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n      return {\n        success: true,\n        user,\n        token\n      };\n    } catch (error) {\n      var _error$response;\n      let errorMessage = 'Registration failed';\n      if ((_error$response = error.response) !== null && _error$response !== void 0 && _error$response.data) {\n        if (typeof error.response.data.detail === 'string') {\n          errorMessage = error.response.data.detail;\n        } else if (Array.isArray(error.response.data.detail)) {\n          errorMessage = error.response.data.detail.map(err => err.msg).join(', ');\n        } else if (error.response.data.message) {\n          errorMessage = error.response.data.message;\n        }\n      }\n      return {\n        success: false,\n        message: errorMessage\n      };\n    }\n  },\n  // User login\n  login: async credentials => {\n    try {\n      const response = await authAPI.post('/api/login', credentials);\n      const {\n        token,\n        user\n      } = response.data;\n      localStorage.setItem('token', token);\n      localStorage.setItem('user', JSON.stringify(user));\n\n      // Set default headers for future requests\n      authAPI.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n      medicalAPI.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n      return {\n        success: true,\n        user,\n        token\n      };\n    } catch (error) {\n      var _error$response2;\n      let errorMessage = 'Login failed';\n      if ((_error$response2 = error.response) !== null && _error$response2 !== void 0 && _error$response2.data) {\n        if (typeof error.response.data.detail === 'string') {\n          errorMessage = error.response.data.detail;\n        } else if (Array.isArray(error.response.data.detail)) {\n          errorMessage = error.response.data.detail.map(err => err.msg).join(', ');\n        } else if (error.response.data.message) {\n          errorMessage = error.response.data.message;\n        }\n      }\n      return {\n        success: false,\n        message: errorMessage\n      };\n    }\n  },\n  // Google OAuth login\n  googleLogin: async tokenData => {\n    try {\n      const response = await authAPI.post('/api/google-login', tokenData);\n      const {\n        token,\n        user\n      } = response.data;\n      localStorage.setItem('token', token);\n      localStorage.setItem('user', JSON.stringify(user));\n\n      // Set default headers for future requests\n      authAPI.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n      medicalAPI.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n      return {\n        success: true,\n        user,\n        token\n      };\n    } catch (error) {\n      var _error$response3;\n      let errorMessage = 'Google login failed';\n      if ((_error$response3 = error.response) !== null && _error$response3 !== void 0 && _error$response3.data) {\n        if (typeof error.response.data.detail === 'string') {\n          errorMessage = error.response.data.detail;\n        } else if (Array.isArray(error.response.data.detail)) {\n          errorMessage = error.response.data.detail.map(err => err.msg).join(', ');\n        } else if (error.response.data.message) {\n          errorMessage = error.response.data.message;\n        }\n      }\n      return {\n        success: false,\n        message: errorMessage\n      };\n    }\n  },\n  // Get current user profile\n  getProfile: async () => {\n    try {\n      const response = await authAPI.get('/api/profile');\n      return {\n        success: true,\n        user: response.data\n      };\n    } catch (error) {\n      var _error$response4;\n      console.error('Profile fetch failed:', ((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : _error$response4.data) || error.message);\n      localStorage.removeItem('token');\n      localStorage.removeItem('user');\n      delete authAPI.defaults.headers.common['Authorization'];\n      delete medicalAPI.defaults.headers.common['Authorization'];\n      return {\n        success: false,\n        message: 'Failed to fetch profile'\n      };\n    }\n  },\n  // Update user profile\n  updateProfile: async profileData => {\n    try {\n      const response = await authAPI.put('/profile', profileData);\n      return response.data;\n    } catch (error) {\n      var _error$response5;\n      throw ((_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : _error$response5.data) || error.message;\n    }\n  },\n  // Logout\n  logout: () => {\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n    // Remove auth headers\n    delete authAPI.defaults.headers.common['Authorization'];\n    delete medicalAPI.defaults.headers.common['Authorization'];\n  },\n  // Get token from localStorage\n  getToken: () => {\n    return localStorage.getItem('token');\n  },\n  // Check if user is authenticated\n  isAuthenticated: () => {\n    return !!localStorage.getItem('token');\n  },\n  // Get current user from localStorage\n  getCurrentUser: () => {\n    const user = localStorage.getItem('user');\n    return user ? JSON.parse(user) : null;\n  }\n};\n\n// Medical AI API calls (medcure-backend)\nexport const medicalService = {\n  // Submit medical query (text only)\n  submitQuery: async queryData => {\n    try {\n      const response = await medicalAPI.post('/query', queryData);\n      return response.data;\n    } catch (error) {\n      var _error$response6;\n      throw ((_error$response6 = error.response) === null || _error$response6 === void 0 ? void 0 : _error$response6.data) || error.message;\n    }\n  },\n  // Submit medical query with file\n  submitQueryWithFile: async formData => {\n    try {\n      const response = await medicalAPI.post('/query-with-file', formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data'\n        }\n      });\n      return response.data;\n    } catch (error) {\n      var _error$response7;\n      throw ((_error$response7 = error.response) === null || _error$response7 === void 0 ? void 0 : _error$response7.data) || error.message;\n    }\n  },\n  // Get user query history\n  getQueryHistory: async (userId, limit = 20) => {\n    try {\n      const response = await medicalAPI.get(`/history/${userId}?limit=${limit}`);\n      return response.data;\n    } catch (error) {\n      var _error$response8;\n      throw ((_error$response8 = error.response) === null || _error$response8 === void 0 ? void 0 : _error$response8.data) || error.message;\n    }\n  },\n  // Get user statistics\n  getUserStats: async userId => {\n    try {\n      const response = await medicalAPI.get(`/stats/${userId}`);\n      return response.data;\n    } catch (error) {\n      var _error$response9;\n      throw ((_error$response9 = error.response) === null || _error$response9 === void 0 ? void 0 : _error$response9.data) || error.message;\n    }\n  },\n  // Fetch PubMed articles\n  fetchPubMedArticles: async (query, numArticles = 5) => {\n    try {\n      const response = await medicalAPI.post('/pubmed', {\n        query,\n        num_articles: numArticles\n      });\n      return response.data;\n    } catch (error) {\n      var _error$response0;\n      throw ((_error$response0 = error.response) === null || _error$response0 === void 0 ? void 0 : _error$response0.data) || error.message;\n    }\n  },\n  // Generate PDF report\n  generatePDF: async pdfData => {\n    try {\n      const response = await medicalAPI.post('/generate-pdf', pdfData, {\n        headers: {\n          'Content-Type': 'multipart/form-data'\n        },\n        responseType: 'blob' // Important for PDF download\n      });\n      return response.data;\n    } catch (error) {\n      var _error$response1;\n      throw ((_error$response1 = error.response) === null || _error$response1 === void 0 ? void 0 : _error$response1.data) || error.message;\n    }\n  },\n  // Test AI functionality\n  testAI: async (query = \"What is diabetes?\", language = \"English\") => {\n    try {\n      const response = await medicalAPI.post(`/test-ai?query=${encodeURIComponent(query)}&language=${language}`);\n      return response.data;\n    } catch (error) {\n      var _error$response10;\n      throw ((_error$response10 = error.response) === null || _error$response10 === void 0 ? void 0 : _error$response10.data) || error.message;\n    }\n  },\n  // Health check\n  healthCheck: async () => {\n    try {\n      const response = await medicalAPI.get('/health');\n      return response.data;\n    } catch (error) {\n      var _error$response11;\n      throw ((_error$response11 = error.response) === null || _error$response11 === void 0 ? void 0 : _error$response11.data) || error.message;\n    }\n  }\n};\n\n// Utility functions\nexport const apiUtils = {\n  // Handle API errors consistently\n  handleError: error => {\n    if (error.response) {\n      var _error$response$data, _error$response$data2;\n      // Server responded with error status\n      return ((_error$response$data = error.response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || ((_error$response$data2 = error.response.data) === null || _error$response$data2 === void 0 ? void 0 : _error$response$data2.message) || 'Server error occurred';\n    } else if (error.request) {\n      // Request was made but no response received\n      return 'Network error - please check your connection';\n    } else {\n      // Something else happened\n      return error.message || 'An unexpected error occurred';\n    }\n  },\n  // Format error messages for display\n  formatErrorMessage: error => {\n    if (typeof error === 'string') {\n      return error;\n    }\n    if (Array.isArray(error.detail)) {\n      return error.detail.map(err => err.msg).join(', ');\n    }\n    return error.detail || error.message || 'An error occurred';\n  }\n};\nexport default {\n  authService,\n  medicalService,\n  apiUtils\n};", "map": {"version": 3, "names": ["axios", "AUTH_API_BASE_URL", "process", "env", "REACT_APP_AUTH_API_URL", "MEDICAL_API_BASE_URL", "REACT_APP_MEDICAL_API_URL", "authAPI", "create", "baseURL", "headers", "medicalAPI", "addAuthInterceptor", "apiInstance", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "authService", "register", "userData", "response", "post", "user", "data", "setItem", "JSON", "stringify", "defaults", "common", "success", "_error$response", "errorMessage", "detail", "Array", "isArray", "map", "err", "msg", "join", "message", "login", "credentials", "_error$response2", "googleLogin", "tokenData", "_error$response3", "getProfile", "get", "_error$response4", "console", "removeItem", "updateProfile", "profileData", "put", "_error$response5", "logout", "getToken", "isAuthenticated", "getCurrentUser", "parse", "medicalService", "submitQuery", "queryData", "_error$response6", "submitQueryWithFile", "formData", "_error$response7", "getQueryHistory", "userId", "limit", "_error$response8", "getUserStats", "_error$response9", "fetchPubMedArticles", "query", "numArticles", "num_articles", "_error$response0", "generatePDF", "pdfData", "responseType", "_error$response1", "testAI", "language", "encodeURIComponent", "_error$response10", "healthCheck", "_error$response11", "apiUtils", "handleError", "_error$response$data", "_error$response$data2", "formatErrorMessage"], "sources": ["C:/Users/<USER>/OneDrive/Documents/AuthPages/Evolune/medcure/src/services/api.js"], "sourcesContent": ["import axios from 'axios';\n\n// API Configuration\nconst AUTH_API_BASE_URL = process.env.REACT_APP_AUTH_API_URL || 'http://localhost:8000'; // auth-backend-fastapi\nconst MEDICAL_API_BASE_URL = process.env.REACT_APP_MEDICAL_API_URL || 'http://localhost:8001'; // medcure-backend\n\n// Create axios instances for different backends\nconst authAPI = axios.create({\n  baseURL: AUTH_API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\nconst medicalAPI = axios.create({\n  baseURL: MEDICAL_API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Add request interceptors to include auth token\nconst addAuthInterceptor = (apiInstance) => {\n  apiInstance.interceptors.request.use(\n    (config) => {\n      const token = localStorage.getItem('token');\n      if (token) {\n        config.headers.Authorization = `Bearer ${token}`;\n      }\n      return config;\n    },\n    (error) => {\n      return Promise.reject(error);\n    }\n  );\n};\n\n// Add auth interceptors to both API instances\naddAuthInterceptor(authAPI);\naddAuthInterceptor(medicalAPI);\n\n// Authentication API calls (auth-backend-fastapi)\nexport const authService = {\n  // User registration\n  register: async (userData) => {\n    try {\n      const response = await authAPI.post('/api/register', userData);\n      const { token, user } = response.data;\n      localStorage.setItem('token', token);\n      localStorage.setItem('user', JSON.stringify(user));\n\n      // Set default headers for future requests\n      authAPI.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n      medicalAPI.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n\n      return { success: true, user, token };\n    } catch (error) {\n      let errorMessage = 'Registration failed';\n\n      if (error.response?.data) {\n        if (typeof error.response.data.detail === 'string') {\n          errorMessage = error.response.data.detail;\n        } else if (Array.isArray(error.response.data.detail)) {\n          errorMessage = error.response.data.detail.map(err => err.msg).join(', ');\n        } else if (error.response.data.message) {\n          errorMessage = error.response.data.message;\n        }\n      }\n\n      return {\n        success: false,\n        message: errorMessage\n      };\n    }\n  },\n\n  // User login\n  login: async (credentials) => {\n    try {\n      const response = await authAPI.post('/api/login', credentials);\n      const { token, user } = response.data;\n      localStorage.setItem('token', token);\n      localStorage.setItem('user', JSON.stringify(user));\n\n      // Set default headers for future requests\n      authAPI.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n      medicalAPI.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n\n      return { success: true, user, token };\n    } catch (error) {\n      let errorMessage = 'Login failed';\n\n      if (error.response?.data) {\n        if (typeof error.response.data.detail === 'string') {\n          errorMessage = error.response.data.detail;\n        } else if (Array.isArray(error.response.data.detail)) {\n          errorMessage = error.response.data.detail.map(err => err.msg).join(', ');\n        } else if (error.response.data.message) {\n          errorMessage = error.response.data.message;\n        }\n      }\n\n      return {\n        success: false,\n        message: errorMessage\n      };\n    }\n  },\n\n  // Google OAuth login\n  googleLogin: async (tokenData) => {\n    try {\n      const response = await authAPI.post('/api/google-login', tokenData);\n      const { token, user } = response.data;\n      localStorage.setItem('token', token);\n      localStorage.setItem('user', JSON.stringify(user));\n\n      // Set default headers for future requests\n      authAPI.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n      medicalAPI.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n\n      return { success: true, user, token };\n    } catch (error) {\n      let errorMessage = 'Google login failed';\n\n      if (error.response?.data) {\n        if (typeof error.response.data.detail === 'string') {\n          errorMessage = error.response.data.detail;\n        } else if (Array.isArray(error.response.data.detail)) {\n          errorMessage = error.response.data.detail.map(err => err.msg).join(', ');\n        } else if (error.response.data.message) {\n          errorMessage = error.response.data.message;\n        }\n      }\n\n      return {\n        success: false,\n        message: errorMessage\n      };\n    }\n  },\n\n  // Get current user profile\n  getProfile: async () => {\n    try {\n      const response = await authAPI.get('/api/profile');\n      return { success: true, user: response.data };\n    } catch (error) {\n      console.error('Profile fetch failed:', error.response?.data || error.message);\n      localStorage.removeItem('token');\n      localStorage.removeItem('user');\n      delete authAPI.defaults.headers.common['Authorization'];\n      delete medicalAPI.defaults.headers.common['Authorization'];\n      return { success: false, message: 'Failed to fetch profile' };\n    }\n  },\n\n  // Update user profile\n  updateProfile: async (profileData) => {\n    try {\n      const response = await authAPI.put('/profile', profileData);\n      return response.data;\n    } catch (error) {\n      throw error.response?.data || error.message;\n    }\n  },\n\n  // Logout\n  logout: () => {\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n    // Remove auth headers\n    delete authAPI.defaults.headers.common['Authorization'];\n    delete medicalAPI.defaults.headers.common['Authorization'];\n  },\n\n  // Get token from localStorage\n  getToken: () => {\n    return localStorage.getItem('token');\n  },\n\n  // Check if user is authenticated\n  isAuthenticated: () => {\n    return !!localStorage.getItem('token');\n  },\n\n  // Get current user from localStorage\n  getCurrentUser: () => {\n    const user = localStorage.getItem('user');\n    return user ? JSON.parse(user) : null;\n  }\n};\n\n// Medical AI API calls (medcure-backend)\nexport const medicalService = {\n  // Submit medical query (text only)\n  submitQuery: async (queryData) => {\n    try {\n      const response = await medicalAPI.post('/query', queryData);\n      return response.data;\n    } catch (error) {\n      throw error.response?.data || error.message;\n    }\n  },\n\n  // Submit medical query with file\n  submitQueryWithFile: async (formData) => {\n    try {\n      const response = await medicalAPI.post('/query-with-file', formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data',\n        },\n      });\n      return response.data;\n    } catch (error) {\n      throw error.response?.data || error.message;\n    }\n  },\n\n  // Get user query history\n  getQueryHistory: async (userId, limit = 20) => {\n    try {\n      const response = await medicalAPI.get(`/history/${userId}?limit=${limit}`);\n      return response.data;\n    } catch (error) {\n      throw error.response?.data || error.message;\n    }\n  },\n\n  // Get user statistics\n  getUserStats: async (userId) => {\n    try {\n      const response = await medicalAPI.get(`/stats/${userId}`);\n      return response.data;\n    } catch (error) {\n      throw error.response?.data || error.message;\n    }\n  },\n\n  // Fetch PubMed articles\n  fetchPubMedArticles: async (query, numArticles = 5) => {\n    try {\n      const response = await medicalAPI.post('/pubmed', {\n        query,\n        num_articles: numArticles\n      });\n      return response.data;\n    } catch (error) {\n      throw error.response?.data || error.message;\n    }\n  },\n\n  // Generate PDF report\n  generatePDF: async (pdfData) => {\n    try {\n      const response = await medicalAPI.post('/generate-pdf', pdfData, {\n        headers: {\n          'Content-Type': 'multipart/form-data',\n        },\n        responseType: 'blob', // Important for PDF download\n      });\n      return response.data;\n    } catch (error) {\n      throw error.response?.data || error.message;\n    }\n  },\n\n  // Test AI functionality\n  testAI: async (query = \"What is diabetes?\", language = \"English\") => {\n    try {\n      const response = await medicalAPI.post(`/test-ai?query=${encodeURIComponent(query)}&language=${language}`);\n      return response.data;\n    } catch (error) {\n      throw error.response?.data || error.message;\n    }\n  },\n\n  // Health check\n  healthCheck: async () => {\n    try {\n      const response = await medicalAPI.get('/health');\n      return response.data;\n    } catch (error) {\n      throw error.response?.data || error.message;\n    }\n  }\n};\n\n// Utility functions\nexport const apiUtils = {\n  // Handle API errors consistently\n  handleError: (error) => {\n    if (error.response) {\n      // Server responded with error status\n      return error.response.data?.detail || error.response.data?.message || 'Server error occurred';\n    } else if (error.request) {\n      // Request was made but no response received\n      return 'Network error - please check your connection';\n    } else {\n      // Something else happened\n      return error.message || 'An unexpected error occurred';\n    }\n  },\n\n  // Format error messages for display\n  formatErrorMessage: (error) => {\n    if (typeof error === 'string') {\n      return error;\n    }\n    if (Array.isArray(error.detail)) {\n      return error.detail.map(err => err.msg).join(', ');\n    }\n    return error.detail || error.message || 'An error occurred';\n  }\n};\n\nexport default { authService, medicalService, apiUtils };\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;;AAEzB;AACA,MAAMC,iBAAiB,GAAGC,OAAO,CAACC,GAAG,CAACC,sBAAsB,IAAI,uBAAuB,CAAC,CAAC;AACzF,MAAMC,oBAAoB,GAAGH,OAAO,CAACC,GAAG,CAACG,yBAAyB,IAAI,uBAAuB,CAAC,CAAC;;AAE/F;AACA,MAAMC,OAAO,GAAGP,KAAK,CAACQ,MAAM,CAAC;EAC3BC,OAAO,EAAER,iBAAiB;EAC1BS,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;AAEF,MAAMC,UAAU,GAAGX,KAAK,CAACQ,MAAM,CAAC;EAC9BC,OAAO,EAAEJ,oBAAoB;EAC7BK,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACA,MAAME,kBAAkB,GAAIC,WAAW,IAAK;EAC1CA,WAAW,CAACC,YAAY,CAACC,OAAO,CAACC,GAAG,CACjCC,MAAM,IAAK;IACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAIF,KAAK,EAAE;MACTD,MAAM,CAACP,OAAO,CAACW,aAAa,GAAG,UAAUH,KAAK,EAAE;IAClD;IACA,OAAOD,MAAM;EACf,CAAC,EACAK,KAAK,IAAK;IACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;EAC9B,CACF,CAAC;AACH,CAAC;;AAED;AACAV,kBAAkB,CAACL,OAAO,CAAC;AAC3BK,kBAAkB,CAACD,UAAU,CAAC;;AAE9B;AACA,OAAO,MAAMc,WAAW,GAAG;EACzB;EACAC,QAAQ,EAAE,MAAOC,QAAQ,IAAK;IAC5B,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMrB,OAAO,CAACsB,IAAI,CAAC,eAAe,EAAEF,QAAQ,CAAC;MAC9D,MAAM;QAAET,KAAK;QAAEY;MAAK,CAAC,GAAGF,QAAQ,CAACG,IAAI;MACrCZ,YAAY,CAACa,OAAO,CAAC,OAAO,EAAEd,KAAK,CAAC;MACpCC,YAAY,CAACa,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACJ,IAAI,CAAC,CAAC;;MAElD;MACAvB,OAAO,CAAC4B,QAAQ,CAACzB,OAAO,CAAC0B,MAAM,CAAC,eAAe,CAAC,GAAG,UAAUlB,KAAK,EAAE;MACpEP,UAAU,CAACwB,QAAQ,CAACzB,OAAO,CAAC0B,MAAM,CAAC,eAAe,CAAC,GAAG,UAAUlB,KAAK,EAAE;MAEvE,OAAO;QAAEmB,OAAO,EAAE,IAAI;QAAEP,IAAI;QAAEZ;MAAM,CAAC;IACvC,CAAC,CAAC,OAAOI,KAAK,EAAE;MAAA,IAAAgB,eAAA;MACd,IAAIC,YAAY,GAAG,qBAAqB;MAExC,KAAAD,eAAA,GAAIhB,KAAK,CAACM,QAAQ,cAAAU,eAAA,eAAdA,eAAA,CAAgBP,IAAI,EAAE;QACxB,IAAI,OAAOT,KAAK,CAACM,QAAQ,CAACG,IAAI,CAACS,MAAM,KAAK,QAAQ,EAAE;UAClDD,YAAY,GAAGjB,KAAK,CAACM,QAAQ,CAACG,IAAI,CAACS,MAAM;QAC3C,CAAC,MAAM,IAAIC,KAAK,CAACC,OAAO,CAACpB,KAAK,CAACM,QAAQ,CAACG,IAAI,CAACS,MAAM,CAAC,EAAE;UACpDD,YAAY,GAAGjB,KAAK,CAACM,QAAQ,CAACG,IAAI,CAACS,MAAM,CAACG,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACC,GAAG,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;QAC1E,CAAC,MAAM,IAAIxB,KAAK,CAACM,QAAQ,CAACG,IAAI,CAACgB,OAAO,EAAE;UACtCR,YAAY,GAAGjB,KAAK,CAACM,QAAQ,CAACG,IAAI,CAACgB,OAAO;QAC5C;MACF;MAEA,OAAO;QACLV,OAAO,EAAE,KAAK;QACdU,OAAO,EAAER;MACX,CAAC;IACH;EACF,CAAC;EAED;EACAS,KAAK,EAAE,MAAOC,WAAW,IAAK;IAC5B,IAAI;MACF,MAAMrB,QAAQ,GAAG,MAAMrB,OAAO,CAACsB,IAAI,CAAC,YAAY,EAAEoB,WAAW,CAAC;MAC9D,MAAM;QAAE/B,KAAK;QAAEY;MAAK,CAAC,GAAGF,QAAQ,CAACG,IAAI;MACrCZ,YAAY,CAACa,OAAO,CAAC,OAAO,EAAEd,KAAK,CAAC;MACpCC,YAAY,CAACa,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACJ,IAAI,CAAC,CAAC;;MAElD;MACAvB,OAAO,CAAC4B,QAAQ,CAACzB,OAAO,CAAC0B,MAAM,CAAC,eAAe,CAAC,GAAG,UAAUlB,KAAK,EAAE;MACpEP,UAAU,CAACwB,QAAQ,CAACzB,OAAO,CAAC0B,MAAM,CAAC,eAAe,CAAC,GAAG,UAAUlB,KAAK,EAAE;MAEvE,OAAO;QAAEmB,OAAO,EAAE,IAAI;QAAEP,IAAI;QAAEZ;MAAM,CAAC;IACvC,CAAC,CAAC,OAAOI,KAAK,EAAE;MAAA,IAAA4B,gBAAA;MACd,IAAIX,YAAY,GAAG,cAAc;MAEjC,KAAAW,gBAAA,GAAI5B,KAAK,CAACM,QAAQ,cAAAsB,gBAAA,eAAdA,gBAAA,CAAgBnB,IAAI,EAAE;QACxB,IAAI,OAAOT,KAAK,CAACM,QAAQ,CAACG,IAAI,CAACS,MAAM,KAAK,QAAQ,EAAE;UAClDD,YAAY,GAAGjB,KAAK,CAACM,QAAQ,CAACG,IAAI,CAACS,MAAM;QAC3C,CAAC,MAAM,IAAIC,KAAK,CAACC,OAAO,CAACpB,KAAK,CAACM,QAAQ,CAACG,IAAI,CAACS,MAAM,CAAC,EAAE;UACpDD,YAAY,GAAGjB,KAAK,CAACM,QAAQ,CAACG,IAAI,CAACS,MAAM,CAACG,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACC,GAAG,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;QAC1E,CAAC,MAAM,IAAIxB,KAAK,CAACM,QAAQ,CAACG,IAAI,CAACgB,OAAO,EAAE;UACtCR,YAAY,GAAGjB,KAAK,CAACM,QAAQ,CAACG,IAAI,CAACgB,OAAO;QAC5C;MACF;MAEA,OAAO;QACLV,OAAO,EAAE,KAAK;QACdU,OAAO,EAAER;MACX,CAAC;IACH;EACF,CAAC;EAED;EACAY,WAAW,EAAE,MAAOC,SAAS,IAAK;IAChC,IAAI;MACF,MAAMxB,QAAQ,GAAG,MAAMrB,OAAO,CAACsB,IAAI,CAAC,mBAAmB,EAAEuB,SAAS,CAAC;MACnE,MAAM;QAAElC,KAAK;QAAEY;MAAK,CAAC,GAAGF,QAAQ,CAACG,IAAI;MACrCZ,YAAY,CAACa,OAAO,CAAC,OAAO,EAAEd,KAAK,CAAC;MACpCC,YAAY,CAACa,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACJ,IAAI,CAAC,CAAC;;MAElD;MACAvB,OAAO,CAAC4B,QAAQ,CAACzB,OAAO,CAAC0B,MAAM,CAAC,eAAe,CAAC,GAAG,UAAUlB,KAAK,EAAE;MACpEP,UAAU,CAACwB,QAAQ,CAACzB,OAAO,CAAC0B,MAAM,CAAC,eAAe,CAAC,GAAG,UAAUlB,KAAK,EAAE;MAEvE,OAAO;QAAEmB,OAAO,EAAE,IAAI;QAAEP,IAAI;QAAEZ;MAAM,CAAC;IACvC,CAAC,CAAC,OAAOI,KAAK,EAAE;MAAA,IAAA+B,gBAAA;MACd,IAAId,YAAY,GAAG,qBAAqB;MAExC,KAAAc,gBAAA,GAAI/B,KAAK,CAACM,QAAQ,cAAAyB,gBAAA,eAAdA,gBAAA,CAAgBtB,IAAI,EAAE;QACxB,IAAI,OAAOT,KAAK,CAACM,QAAQ,CAACG,IAAI,CAACS,MAAM,KAAK,QAAQ,EAAE;UAClDD,YAAY,GAAGjB,KAAK,CAACM,QAAQ,CAACG,IAAI,CAACS,MAAM;QAC3C,CAAC,MAAM,IAAIC,KAAK,CAACC,OAAO,CAACpB,KAAK,CAACM,QAAQ,CAACG,IAAI,CAACS,MAAM,CAAC,EAAE;UACpDD,YAAY,GAAGjB,KAAK,CAACM,QAAQ,CAACG,IAAI,CAACS,MAAM,CAACG,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACC,GAAG,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;QAC1E,CAAC,MAAM,IAAIxB,KAAK,CAACM,QAAQ,CAACG,IAAI,CAACgB,OAAO,EAAE;UACtCR,YAAY,GAAGjB,KAAK,CAACM,QAAQ,CAACG,IAAI,CAACgB,OAAO;QAC5C;MACF;MAEA,OAAO;QACLV,OAAO,EAAE,KAAK;QACdU,OAAO,EAAER;MACX,CAAC;IACH;EACF,CAAC;EAED;EACAe,UAAU,EAAE,MAAAA,CAAA,KAAY;IACtB,IAAI;MACF,MAAM1B,QAAQ,GAAG,MAAMrB,OAAO,CAACgD,GAAG,CAAC,cAAc,CAAC;MAClD,OAAO;QAAElB,OAAO,EAAE,IAAI;QAAEP,IAAI,EAAEF,QAAQ,CAACG;MAAK,CAAC;IAC/C,CAAC,CAAC,OAAOT,KAAK,EAAE;MAAA,IAAAkC,gBAAA;MACdC,OAAO,CAACnC,KAAK,CAAC,uBAAuB,EAAE,EAAAkC,gBAAA,GAAAlC,KAAK,CAACM,QAAQ,cAAA4B,gBAAA,uBAAdA,gBAAA,CAAgBzB,IAAI,KAAIT,KAAK,CAACyB,OAAO,CAAC;MAC7E5B,YAAY,CAACuC,UAAU,CAAC,OAAO,CAAC;MAChCvC,YAAY,CAACuC,UAAU,CAAC,MAAM,CAAC;MAC/B,OAAOnD,OAAO,CAAC4B,QAAQ,CAACzB,OAAO,CAAC0B,MAAM,CAAC,eAAe,CAAC;MACvD,OAAOzB,UAAU,CAACwB,QAAQ,CAACzB,OAAO,CAAC0B,MAAM,CAAC,eAAe,CAAC;MAC1D,OAAO;QAAEC,OAAO,EAAE,KAAK;QAAEU,OAAO,EAAE;MAA0B,CAAC;IAC/D;EACF,CAAC;EAED;EACAY,aAAa,EAAE,MAAOC,WAAW,IAAK;IACpC,IAAI;MACF,MAAMhC,QAAQ,GAAG,MAAMrB,OAAO,CAACsD,GAAG,CAAC,UAAU,EAAED,WAAW,CAAC;MAC3D,OAAOhC,QAAQ,CAACG,IAAI;IACtB,CAAC,CAAC,OAAOT,KAAK,EAAE;MAAA,IAAAwC,gBAAA;MACd,MAAM,EAAAA,gBAAA,GAAAxC,KAAK,CAACM,QAAQ,cAAAkC,gBAAA,uBAAdA,gBAAA,CAAgB/B,IAAI,KAAIT,KAAK,CAACyB,OAAO;IAC7C;EACF,CAAC;EAED;EACAgB,MAAM,EAAEA,CAAA,KAAM;IACZ5C,YAAY,CAACuC,UAAU,CAAC,OAAO,CAAC;IAChCvC,YAAY,CAACuC,UAAU,CAAC,MAAM,CAAC;IAC/B;IACA,OAAOnD,OAAO,CAAC4B,QAAQ,CAACzB,OAAO,CAAC0B,MAAM,CAAC,eAAe,CAAC;IACvD,OAAOzB,UAAU,CAACwB,QAAQ,CAACzB,OAAO,CAAC0B,MAAM,CAAC,eAAe,CAAC;EAC5D,CAAC;EAED;EACA4B,QAAQ,EAAEA,CAAA,KAAM;IACd,OAAO7C,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EACtC,CAAC;EAED;EACA6C,eAAe,EAAEA,CAAA,KAAM;IACrB,OAAO,CAAC,CAAC9C,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EACxC,CAAC;EAED;EACA8C,cAAc,EAAEA,CAAA,KAAM;IACpB,MAAMpC,IAAI,GAAGX,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;IACzC,OAAOU,IAAI,GAAGG,IAAI,CAACkC,KAAK,CAACrC,IAAI,CAAC,GAAG,IAAI;EACvC;AACF,CAAC;;AAED;AACA,OAAO,MAAMsC,cAAc,GAAG;EAC5B;EACAC,WAAW,EAAE,MAAOC,SAAS,IAAK;IAChC,IAAI;MACF,MAAM1C,QAAQ,GAAG,MAAMjB,UAAU,CAACkB,IAAI,CAAC,QAAQ,EAAEyC,SAAS,CAAC;MAC3D,OAAO1C,QAAQ,CAACG,IAAI;IACtB,CAAC,CAAC,OAAOT,KAAK,EAAE;MAAA,IAAAiD,gBAAA;MACd,MAAM,EAAAA,gBAAA,GAAAjD,KAAK,CAACM,QAAQ,cAAA2C,gBAAA,uBAAdA,gBAAA,CAAgBxC,IAAI,KAAIT,KAAK,CAACyB,OAAO;IAC7C;EACF,CAAC;EAED;EACAyB,mBAAmB,EAAE,MAAOC,QAAQ,IAAK;IACvC,IAAI;MACF,MAAM7C,QAAQ,GAAG,MAAMjB,UAAU,CAACkB,IAAI,CAAC,kBAAkB,EAAE4C,QAAQ,EAAE;QACnE/D,OAAO,EAAE;UACP,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MACF,OAAOkB,QAAQ,CAACG,IAAI;IACtB,CAAC,CAAC,OAAOT,KAAK,EAAE;MAAA,IAAAoD,gBAAA;MACd,MAAM,EAAAA,gBAAA,GAAApD,KAAK,CAACM,QAAQ,cAAA8C,gBAAA,uBAAdA,gBAAA,CAAgB3C,IAAI,KAAIT,KAAK,CAACyB,OAAO;IAC7C;EACF,CAAC;EAED;EACA4B,eAAe,EAAE,MAAAA,CAAOC,MAAM,EAAEC,KAAK,GAAG,EAAE,KAAK;IAC7C,IAAI;MACF,MAAMjD,QAAQ,GAAG,MAAMjB,UAAU,CAAC4C,GAAG,CAAC,YAAYqB,MAAM,UAAUC,KAAK,EAAE,CAAC;MAC1E,OAAOjD,QAAQ,CAACG,IAAI;IACtB,CAAC,CAAC,OAAOT,KAAK,EAAE;MAAA,IAAAwD,gBAAA;MACd,MAAM,EAAAA,gBAAA,GAAAxD,KAAK,CAACM,QAAQ,cAAAkD,gBAAA,uBAAdA,gBAAA,CAAgB/C,IAAI,KAAIT,KAAK,CAACyB,OAAO;IAC7C;EACF,CAAC;EAED;EACAgC,YAAY,EAAE,MAAOH,MAAM,IAAK;IAC9B,IAAI;MACF,MAAMhD,QAAQ,GAAG,MAAMjB,UAAU,CAAC4C,GAAG,CAAC,UAAUqB,MAAM,EAAE,CAAC;MACzD,OAAOhD,QAAQ,CAACG,IAAI;IACtB,CAAC,CAAC,OAAOT,KAAK,EAAE;MAAA,IAAA0D,gBAAA;MACd,MAAM,EAAAA,gBAAA,GAAA1D,KAAK,CAACM,QAAQ,cAAAoD,gBAAA,uBAAdA,gBAAA,CAAgBjD,IAAI,KAAIT,KAAK,CAACyB,OAAO;IAC7C;EACF,CAAC;EAED;EACAkC,mBAAmB,EAAE,MAAAA,CAAOC,KAAK,EAAEC,WAAW,GAAG,CAAC,KAAK;IACrD,IAAI;MACF,MAAMvD,QAAQ,GAAG,MAAMjB,UAAU,CAACkB,IAAI,CAAC,SAAS,EAAE;QAChDqD,KAAK;QACLE,YAAY,EAAED;MAChB,CAAC,CAAC;MACF,OAAOvD,QAAQ,CAACG,IAAI;IACtB,CAAC,CAAC,OAAOT,KAAK,EAAE;MAAA,IAAA+D,gBAAA;MACd,MAAM,EAAAA,gBAAA,GAAA/D,KAAK,CAACM,QAAQ,cAAAyD,gBAAA,uBAAdA,gBAAA,CAAgBtD,IAAI,KAAIT,KAAK,CAACyB,OAAO;IAC7C;EACF,CAAC;EAED;EACAuC,WAAW,EAAE,MAAOC,OAAO,IAAK;IAC9B,IAAI;MACF,MAAM3D,QAAQ,GAAG,MAAMjB,UAAU,CAACkB,IAAI,CAAC,eAAe,EAAE0D,OAAO,EAAE;QAC/D7E,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACD8E,YAAY,EAAE,MAAM,CAAE;MACxB,CAAC,CAAC;MACF,OAAO5D,QAAQ,CAACG,IAAI;IACtB,CAAC,CAAC,OAAOT,KAAK,EAAE;MAAA,IAAAmE,gBAAA;MACd,MAAM,EAAAA,gBAAA,GAAAnE,KAAK,CAACM,QAAQ,cAAA6D,gBAAA,uBAAdA,gBAAA,CAAgB1D,IAAI,KAAIT,KAAK,CAACyB,OAAO;IAC7C;EACF,CAAC;EAED;EACA2C,MAAM,EAAE,MAAAA,CAAOR,KAAK,GAAG,mBAAmB,EAAES,QAAQ,GAAG,SAAS,KAAK;IACnE,IAAI;MACF,MAAM/D,QAAQ,GAAG,MAAMjB,UAAU,CAACkB,IAAI,CAAC,kBAAkB+D,kBAAkB,CAACV,KAAK,CAAC,aAAaS,QAAQ,EAAE,CAAC;MAC1G,OAAO/D,QAAQ,CAACG,IAAI;IACtB,CAAC,CAAC,OAAOT,KAAK,EAAE;MAAA,IAAAuE,iBAAA;MACd,MAAM,EAAAA,iBAAA,GAAAvE,KAAK,CAACM,QAAQ,cAAAiE,iBAAA,uBAAdA,iBAAA,CAAgB9D,IAAI,KAAIT,KAAK,CAACyB,OAAO;IAC7C;EACF,CAAC;EAED;EACA+C,WAAW,EAAE,MAAAA,CAAA,KAAY;IACvB,IAAI;MACF,MAAMlE,QAAQ,GAAG,MAAMjB,UAAU,CAAC4C,GAAG,CAAC,SAAS,CAAC;MAChD,OAAO3B,QAAQ,CAACG,IAAI;IACtB,CAAC,CAAC,OAAOT,KAAK,EAAE;MAAA,IAAAyE,iBAAA;MACd,MAAM,EAAAA,iBAAA,GAAAzE,KAAK,CAACM,QAAQ,cAAAmE,iBAAA,uBAAdA,iBAAA,CAAgBhE,IAAI,KAAIT,KAAK,CAACyB,OAAO;IAC7C;EACF;AACF,CAAC;;AAED;AACA,OAAO,MAAMiD,QAAQ,GAAG;EACtB;EACAC,WAAW,EAAG3E,KAAK,IAAK;IACtB,IAAIA,KAAK,CAACM,QAAQ,EAAE;MAAA,IAAAsE,oBAAA,EAAAC,qBAAA;MAClB;MACA,OAAO,EAAAD,oBAAA,GAAA5E,KAAK,CAACM,QAAQ,CAACG,IAAI,cAAAmE,oBAAA,uBAAnBA,oBAAA,CAAqB1D,MAAM,OAAA2D,qBAAA,GAAI7E,KAAK,CAACM,QAAQ,CAACG,IAAI,cAAAoE,qBAAA,uBAAnBA,qBAAA,CAAqBpD,OAAO,KAAI,uBAAuB;IAC/F,CAAC,MAAM,IAAIzB,KAAK,CAACP,OAAO,EAAE;MACxB;MACA,OAAO,8CAA8C;IACvD,CAAC,MAAM;MACL;MACA,OAAOO,KAAK,CAACyB,OAAO,IAAI,8BAA8B;IACxD;EACF,CAAC;EAED;EACAqD,kBAAkB,EAAG9E,KAAK,IAAK;IAC7B,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC7B,OAAOA,KAAK;IACd;IACA,IAAImB,KAAK,CAACC,OAAO,CAACpB,KAAK,CAACkB,MAAM,CAAC,EAAE;MAC/B,OAAOlB,KAAK,CAACkB,MAAM,CAACG,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACC,GAAG,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;IACpD;IACA,OAAOxB,KAAK,CAACkB,MAAM,IAAIlB,KAAK,CAACyB,OAAO,IAAI,mBAAmB;EAC7D;AACF,CAAC;AAED,eAAe;EAAEtB,WAAW;EAAE2C,cAAc;EAAE4B;AAAS,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}