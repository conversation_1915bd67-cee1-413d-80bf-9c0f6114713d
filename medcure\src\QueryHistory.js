import React, { useEffect, useState } from 'react';
import Footer from './Footer';
import './QueryHistory.css';
import { medicalService, apiUtils } from './services/api';
import { useAuth } from './context/AuthContext';

const QueryHistory = () => {
  const [queries, setQueries] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  const { requireAuth, user } = useAuth();

  useEffect(() => {
    const fetchQueryHistory = async () => {
      try {
        if (!requireAuth()) {
          setLoading(false);
          return; // requireAuth will show the auth modal
        }

        if (!user) {
          setError('User information not found. Please log in again.');
          setLoading(false);
          return;
        }

        // Fetch from API
        const historyData = await medicalService.getQueryHistory(user.id, 50);
        setQueries(historyData.history || []);

        // Also merge with localStorage data for backward compatibility
        const localQueries = JSON.parse(localStorage.getItem('queryHistory') || '[]');
        if (localQueries.length > 0) {
          // Combine and deduplicate
          const allQueries = [...(historyData.history || []), ...localQueries];
          setQueries(allQueries);
        }

      } catch (err) {
        console.error('Error fetching query history:', err);
        // Fallback to localStorage
        const savedQueries = JSON.parse(localStorage.getItem('queryHistory') || '[]');
        setQueries(savedQueries);
        if (savedQueries.length === 0) {
          setError(apiUtils.formatErrorMessage(err));
        }
      } finally {
        setLoading(false);
      }
    };

    fetchQueryHistory();
  }, [requireAuth, user]);

  if (loading) {
    return (
      <div className="query-history-container">
        <div className="loading-message">Loading your query history...</div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="query-history-container">
      {error && <div className="error-message">{error}</div>}

      {queries.length === 0 ? (
        <div className="empty-message">
          You haven't made any queries yet. <a href="/dashboard">Go to the Dashboard to start!</a>
        </div>
      ) : (
        queries.map((q, index) => (
          <div key={index} className="query-card">
            <div className="query-header">
              Query: {q.query?.substring(0, 100)}{q.query?.length > 100 ? '...' : ''}
            </div>
            <div className="query-body">
              <p><strong>Query:</strong> {q.query}</p>
              <p><strong>Role:</strong> {q.role}</p>
              <p><strong>Time:</strong> {q.timestamp || q.time}</p>
              {q.has_image && <p><strong>Included Image:</strong> Yes</p>}
              <button
                className="rerun-btn"
                onClick={() => {
                  localStorage.setItem('rerunQuery', JSON.stringify({
                    query: q.query,
                    role: q.role,
                    has_image: q.has_image
                  }));
                  window.location.href = '/dashboard'; // redirect to MedicalQuery
                }}
              >
                RERUN THIS QUERY
              </button>
            </div>
          </div>
        ))
      )}
      <Footer />
    </div>
  );
};

export default QueryHistory;
