{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\AuthPages\\\\Evolune\\\\auth-app\\\\src\\\\context\\\\AuthContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useState, useContext, useEffect } from 'react';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext();\nexport const useAuth = () => {\n  _s();\n  return useContext(AuthContext);\n};\n_s(useAuth, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\nexport const AuthProvider = ({\n  children\n}) => {\n  _s2();\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    // Check if user is logged in on app start\n    const token = localStorage.getItem('token');\n    if (token) {\n      // Verify token with backend\n      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n      fetchProfile();\n    } else {\n      setLoading(false);\n    }\n  }, []);\n  const fetchProfile = async () => {\n    try {\n      const response = await axios.get('http://localhost:8000/api/profile');\n      setUser(response.data);\n    } catch (error) {\n      localStorage.removeItem('token');\n      delete axios.defaults.headers.common['Authorization'];\n    } finally {\n      setLoading(false);\n    }\n  };\n  const login = async (email, password) => {\n    try {\n      const response = await axios.post('http://localhost:8000/api/login', {\n        email,\n        password\n      });\n      const {\n        token,\n        user\n      } = response.data;\n      localStorage.setItem('token', token);\n      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n      setUser(user);\n      return {\n        success: true\n      };\n    } catch (error) {\n      var _error$response, _error$response$data;\n      return {\n        success: false,\n        message: ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || 'Login failed'\n      };\n    }\n  };\n  const signup = async (name, email, password) => {\n    try {\n      const response = await axios.post('http://localhost:8000/api/register', {\n        name,\n        email,\n        password\n      });\n      const {\n        token,\n        user\n      } = response.data;\n      localStorage.setItem('token', token);\n      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n      setUser(user);\n      return {\n        success: true\n      };\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      return {\n        success: false,\n        message: ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.detail) || 'Signup failed'\n      };\n    }\n  };\n  const googleLogin = async googleToken => {\n    try {\n      const response = await axios.post('http://localhost:8000/api/google-login', {\n        token: googleToken\n      });\n      const {\n        token,\n        user\n      } = response.data;\n      localStorage.setItem('token', token);\n      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n      setUser(user);\n      return {\n        success: true\n      };\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      return {\n        success: false,\n        message: ((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.detail) || 'Google login failed'\n      };\n    }\n  };\n  const logout = () => {\n    localStorage.removeItem('token');\n    delete axios.defaults.headers.common['Authorization'];\n    setUser(null);\n  };\n  const value = {\n    user,\n    login,\n    signup,\n    googleLogin,\n    logout,\n    loading\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: !loading && children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 117,\n    columnNumber: 5\n  }, this);\n};\n_s2(AuthProvider, \"NiO5z6JIqzX62LS5UWDgIqbZYyY=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useState", "useContext", "useEffect", "axios", "jsxDEV", "_jsxDEV", "AuthContext", "useAuth", "_s", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "user", "setUser", "loading", "setLoading", "token", "localStorage", "getItem", "defaults", "headers", "common", "fetchProfile", "response", "get", "data", "error", "removeItem", "login", "email", "password", "post", "setItem", "success", "_error$response", "_error$response$data", "message", "detail", "signup", "name", "_error$response2", "_error$response2$data", "googleLogin", "googleToken", "_error$response3", "_error$response3$data", "logout", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Documents/AuthPages/Evolune/auth-app/src/context/AuthContext.js"], "sourcesContent": ["import React, { createContext, useState, useContext, useEffect } from 'react';\r\nimport axios from 'axios';\r\n\r\nconst AuthContext = createContext();\r\n\r\nexport const useAuth = () => {\r\n  return useContext(AuthContext);\r\n};\r\n\r\nexport const AuthProvider = ({ children }) => {\r\n  const [user, setUser] = useState(null);\r\n  const [loading, setLoading] = useState(true);\r\n\r\n  useEffect(() => {\r\n    // Check if user is logged in on app start\r\n    const token = localStorage.getItem('token');\r\n    if (token) {\r\n      // Verify token with backend\r\n      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\r\n      fetchProfile();\r\n    } else {\r\n      setLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  const fetchProfile = async () => {\r\n    try {\r\n      const response = await axios.get('http://localhost:8000/api/profile');\r\n      setUser(response.data);\r\n    } catch (error) {\r\n      localStorage.removeItem('token');\r\n      delete axios.defaults.headers.common['Authorization'];\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const login = async (email, password) => {\r\n    try {\r\n      const response = await axios.post('http://localhost:8000/api/login', {\r\n        email,\r\n        password,\r\n      });\r\n\r\n      const { token, user } = response.data;\r\n      localStorage.setItem('token', token);\r\n      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\r\n      setUser(user);\r\n\r\n      return { success: true };\r\n    } catch (error) {\r\n      return {\r\n        success: false,\r\n        message: error.response?.data?.detail || 'Login failed'\r\n      };\r\n    }\r\n  };\r\n\r\n  const signup = async (name, email, password) => {\r\n    try {\r\n      const response = await axios.post('http://localhost:8000/api/register', {\r\n        name,\r\n        email,\r\n        password,\r\n      });\r\n\r\n      const { token, user } = response.data;\r\n      localStorage.setItem('token', token);\r\n      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\r\n      setUser(user);\r\n\r\n      return { success: true };\r\n    } catch (error) {\r\n      return {\r\n        success: false,\r\n        message: error.response?.data?.detail || 'Signup failed'\r\n      };\r\n    }\r\n  };\r\n\r\n  const googleLogin = async (googleToken) => {\r\n    try {\r\n      const response = await axios.post('http://localhost:8000/api/google-login', {\r\n        token: googleToken,\r\n      });\r\n\r\n      const { token, user } = response.data;\r\n      localStorage.setItem('token', token);\r\n      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\r\n      setUser(user);\r\n\r\n      return { success: true };\r\n    } catch (error) {\r\n      return {\r\n        success: false,\r\n        message: error.response?.data?.detail || 'Google login failed'\r\n      };\r\n    }\r\n  };\r\n\r\n  const logout = () => {\r\n    localStorage.removeItem('token');\r\n    delete axios.defaults.headers.common['Authorization'];\r\n    setUser(null);\r\n  };\r\n\r\n  const value = {\r\n    user,\r\n    login,\r\n    signup,\r\n    googleLogin,\r\n    logout,\r\n    loading,\r\n  };\r\n\r\n  return (\r\n    <AuthContext.Provider value={value}>\r\n      {!loading && children}\r\n    </AuthContext.Provider>\r\n  );\r\n};"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,SAAS,QAAQ,OAAO;AAC7E,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,WAAW,gBAAGP,aAAa,CAAC,CAAC;AAEnC,OAAO,MAAMQ,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,OAAOP,UAAU,CAACK,WAAW,CAAC;AAChC,CAAC;AAACE,EAAA,CAFWD,OAAO;AAIpB,OAAO,MAAME,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC5C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EAE5CE,SAAS,CAAC,MAAM;IACd;IACA,MAAMc,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAIF,KAAK,EAAE;MACT;MACAb,KAAK,CAACgB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,GAAG,UAAUL,KAAK,EAAE;MAClEM,YAAY,CAAC,CAAC;IAChB,CAAC,MAAM;MACLP,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMO,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMpB,KAAK,CAACqB,GAAG,CAAC,mCAAmC,CAAC;MACrEX,OAAO,CAACU,QAAQ,CAACE,IAAI,CAAC;IACxB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdT,YAAY,CAACU,UAAU,CAAC,OAAO,CAAC;MAChC,OAAOxB,KAAK,CAACgB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC;IACvD,CAAC,SAAS;MACRN,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMa,KAAK,GAAG,MAAAA,CAAOC,KAAK,EAAEC,QAAQ,KAAK;IACvC,IAAI;MACF,MAAMP,QAAQ,GAAG,MAAMpB,KAAK,CAAC4B,IAAI,CAAC,iCAAiC,EAAE;QACnEF,KAAK;QACLC;MACF,CAAC,CAAC;MAEF,MAAM;QAAEd,KAAK;QAAEJ;MAAK,CAAC,GAAGW,QAAQ,CAACE,IAAI;MACrCR,YAAY,CAACe,OAAO,CAAC,OAAO,EAAEhB,KAAK,CAAC;MACpCb,KAAK,CAACgB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,GAAG,UAAUL,KAAK,EAAE;MAClEH,OAAO,CAACD,IAAI,CAAC;MAEb,OAAO;QAAEqB,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOP,KAAK,EAAE;MAAA,IAAAQ,eAAA,EAAAC,oBAAA;MACd,OAAO;QACLF,OAAO,EAAE,KAAK;QACdG,OAAO,EAAE,EAAAF,eAAA,GAAAR,KAAK,CAACH,QAAQ,cAAAW,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBT,IAAI,cAAAU,oBAAA,uBAApBA,oBAAA,CAAsBE,MAAM,KAAI;MAC3C,CAAC;IACH;EACF,CAAC;EAED,MAAMC,MAAM,GAAG,MAAAA,CAAOC,IAAI,EAAEV,KAAK,EAAEC,QAAQ,KAAK;IAC9C,IAAI;MACF,MAAMP,QAAQ,GAAG,MAAMpB,KAAK,CAAC4B,IAAI,CAAC,oCAAoC,EAAE;QACtEQ,IAAI;QACJV,KAAK;QACLC;MACF,CAAC,CAAC;MAEF,MAAM;QAAEd,KAAK;QAAEJ;MAAK,CAAC,GAAGW,QAAQ,CAACE,IAAI;MACrCR,YAAY,CAACe,OAAO,CAAC,OAAO,EAAEhB,KAAK,CAAC;MACpCb,KAAK,CAACgB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,GAAG,UAAUL,KAAK,EAAE;MAClEH,OAAO,CAACD,IAAI,CAAC;MAEb,OAAO;QAAEqB,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOP,KAAK,EAAE;MAAA,IAAAc,gBAAA,EAAAC,qBAAA;MACd,OAAO;QACLR,OAAO,EAAE,KAAK;QACdG,OAAO,EAAE,EAAAI,gBAAA,GAAAd,KAAK,CAACH,QAAQ,cAAAiB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBf,IAAI,cAAAgB,qBAAA,uBAApBA,qBAAA,CAAsBJ,MAAM,KAAI;MAC3C,CAAC;IACH;EACF,CAAC;EAED,MAAMK,WAAW,GAAG,MAAOC,WAAW,IAAK;IACzC,IAAI;MACF,MAAMpB,QAAQ,GAAG,MAAMpB,KAAK,CAAC4B,IAAI,CAAC,wCAAwC,EAAE;QAC1Ef,KAAK,EAAE2B;MACT,CAAC,CAAC;MAEF,MAAM;QAAE3B,KAAK;QAAEJ;MAAK,CAAC,GAAGW,QAAQ,CAACE,IAAI;MACrCR,YAAY,CAACe,OAAO,CAAC,OAAO,EAAEhB,KAAK,CAAC;MACpCb,KAAK,CAACgB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,GAAG,UAAUL,KAAK,EAAE;MAClEH,OAAO,CAACD,IAAI,CAAC;MAEb,OAAO;QAAEqB,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOP,KAAK,EAAE;MAAA,IAAAkB,gBAAA,EAAAC,qBAAA;MACd,OAAO;QACLZ,OAAO,EAAE,KAAK;QACdG,OAAO,EAAE,EAAAQ,gBAAA,GAAAlB,KAAK,CAACH,QAAQ,cAAAqB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBnB,IAAI,cAAAoB,qBAAA,uBAApBA,qBAAA,CAAsBR,MAAM,KAAI;MAC3C,CAAC;IACH;EACF,CAAC;EAED,MAAMS,MAAM,GAAGA,CAAA,KAAM;IACnB7B,YAAY,CAACU,UAAU,CAAC,OAAO,CAAC;IAChC,OAAOxB,KAAK,CAACgB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC;IACrDR,OAAO,CAAC,IAAI,CAAC;EACf,CAAC;EAED,MAAMkC,KAAK,GAAG;IACZnC,IAAI;IACJgB,KAAK;IACLU,MAAM;IACNI,WAAW;IACXI,MAAM;IACNhC;EACF,CAAC;EAED,oBACET,OAAA,CAACC,WAAW,CAAC0C,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAArC,QAAA,EAChC,CAACI,OAAO,IAAIJ;EAAQ;IAAAuC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAE3B,CAAC;AAACzC,GAAA,CA/GWF,YAAY;AAAA4C,EAAA,GAAZ5C,YAAY;AAAA,IAAA4C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}