import React, { useState } from 'react';
import {
  TextField,
  Button,
  Typography,
  Box,
  Alert,
  Divider,
  Link,
} from '@mui/material';

import { useAuth } from '../../context/AuthContext';
import GoogleSignIn from './GoogleSignIn';

const LoginForm = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  
  const { login, googleLogin, switchAuthMode } = useAuth();

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    const result = await login(email, password);
    
    if (!result.success) {
      setError(result.message);
    }
    
    setLoading(false);
  };

  const handleGoogleSuccess = async (response) => {
    setLoading(true);
    setError('');

    const result = await googleLogin(response.credential);
    
    if (!result.success) {
      setError(result.message);
    }
    
    setLoading(false);
  };

  const handleGoogleError = () => {
    setError('Google sign-in failed');
  };

  return (
    <Box>
      <Typography component="h1" variant="h4" align="center" gutterBottom>
        Sign In
      </Typography>
      
      {error && <Alert severity="error" sx={{ mb: 2 }}>{String(error)}</Alert>}
      
      <GoogleSignIn 
        onSuccess={handleGoogleSuccess}
        onError={handleGoogleError}
      />
      
      <Divider sx={{ my: 2 }}>OR</Divider>
      
      <Box component="form" onSubmit={handleSubmit}>
        <TextField
          margin="normal"
          required
          fullWidth
          id="email"
          label="Email Address"
          name="email"
          autoComplete="email"
          autoFocus
          value={email}
          onChange={(e) => setEmail(e.target.value)}
        />
        <TextField
          margin="normal"
          required
          fullWidth
          name="password"
          label="Password"
          type="password"
          id="password"
          autoComplete="current-password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
        />
        <Button
          type="submit"
          fullWidth
          variant="contained"
          sx={{ mt: 3, mb: 2 }}
          disabled={loading}
        >
          {loading ? 'Signing In...' : 'Sign In'}
        </Button>
        <Box textAlign="center">
          <Link
            component="button"
            variant="body2"
            onClick={(e) => {
              e.preventDefault();
              switchAuthMode('signup');
            }}
          >
            Don't have an account? Sign Up
          </Link>
        </Box>
      </Box>
    </Box>
  );
};

export default LoginForm;
