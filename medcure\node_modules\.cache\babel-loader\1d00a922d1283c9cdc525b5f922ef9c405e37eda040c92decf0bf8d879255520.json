{"ast": null, "code": "import clsx from 'clsx';\n\n/**\n * Add keys, values of `defaultProps` that does not exist in `props`\n * @param defaultProps\n * @param props\n * @param mergeClassNameAndStyle If `true`, merges `className` and `style` props instead of overriding them.\n *   When `false` (default), props override defaultProps. When `true`, `className` values are concatenated\n *   and `style` objects are merged with props taking precedence.\n * @returns resolved props\n */\nexport default function resolveProps(defaultProps, props, mergeClassNameAndStyle = false) {\n  const output = {\n    ...props\n  };\n  for (const key in defaultProps) {\n    if (Object.prototype.hasOwnProperty.call(defaultProps, key)) {\n      const propName = key;\n      if (propName === 'components' || propName === 'slots') {\n        output[propName] = {\n          ...defaultProps[propName],\n          ...output[propName]\n        };\n      } else if (propName === 'componentsProps' || propName === 'slotProps') {\n        const defaultSlotProps = defaultProps[propName];\n        const slotProps = props[propName];\n        if (!slotProps) {\n          output[propName] = defaultSlotProps || {};\n        } else if (!defaultSlotProps) {\n          output[propName] = slotProps;\n        } else {\n          output[propName] = {\n            ...slotProps\n          };\n          for (const slotKey in defaultSlotProps) {\n            if (Object.prototype.hasOwnProperty.call(defaultSlotProps, slotKey)) {\n              const slotPropName = slotKey;\n              output[propName][slotPropName] = resolveProps(defaultSlotProps[slotPropName], slotProps[slotPropName], mergeClassNameAndStyle);\n            }\n          }\n        }\n      } else if (propName === 'className' && mergeClassNameAndStyle && props.className) {\n        output.className = clsx(defaultProps?.className, props?.className);\n      } else if (propName === 'style' && mergeClassNameAndStyle && props.style) {\n        output.style = {\n          ...defaultProps?.style,\n          ...props?.style\n        };\n      } else if (output[propName] === undefined) {\n        output[propName] = defaultProps[propName];\n      }\n    }\n  }\n  return output;\n}", "map": {"version": 3, "names": ["clsx", "resolveProps", "defaultProps", "props", "mergeClassNameAndStyle", "output", "key", "Object", "prototype", "hasOwnProperty", "call", "propName", "defaultSlotProps", "slotProps", "<PERSON><PERSON><PERSON>", "slotPropName", "className", "style", "undefined"], "sources": ["C:/Users/<USER>/OneDrive/Documents/AuthPages/Evolune/medcure/node_modules/@mui/utils/esm/resolveProps/resolveProps.js"], "sourcesContent": ["import clsx from 'clsx';\n\n/**\n * Add keys, values of `defaultProps` that does not exist in `props`\n * @param defaultProps\n * @param props\n * @param mergeClassNameAndStyle If `true`, merges `className` and `style` props instead of overriding them.\n *   When `false` (default), props override defaultProps. When `true`, `className` values are concatenated\n *   and `style` objects are merged with props taking precedence.\n * @returns resolved props\n */\nexport default function resolveProps(defaultProps, props, mergeClassNameAndStyle = false) {\n  const output = {\n    ...props\n  };\n  for (const key in defaultProps) {\n    if (Object.prototype.hasOwnProperty.call(defaultProps, key)) {\n      const propName = key;\n      if (propName === 'components' || propName === 'slots') {\n        output[propName] = {\n          ...defaultProps[propName],\n          ...output[propName]\n        };\n      } else if (propName === 'componentsProps' || propName === 'slotProps') {\n        const defaultSlotProps = defaultProps[propName];\n        const slotProps = props[propName];\n        if (!slotProps) {\n          output[propName] = defaultSlotProps || {};\n        } else if (!defaultSlotProps) {\n          output[propName] = slotProps;\n        } else {\n          output[propName] = {\n            ...slotProps\n          };\n          for (const slotKey in defaultSlotProps) {\n            if (Object.prototype.hasOwnProperty.call(defaultSlotProps, slotKey)) {\n              const slotPropName = slotKey;\n              output[propName][slotPropName] = resolveProps(defaultSlotProps[slotPropName], slotProps[slotPropName], mergeClassNameAndStyle);\n            }\n          }\n        }\n      } else if (propName === 'className' && mergeClassNameAndStyle && props.className) {\n        output.className = clsx(defaultProps?.className, props?.className);\n      } else if (propName === 'style' && mergeClassNameAndStyle && props.style) {\n        output.style = {\n          ...defaultProps?.style,\n          ...props?.style\n        };\n      } else if (output[propName] === undefined) {\n        output[propName] = defaultProps[propName];\n      }\n    }\n  }\n  return output;\n}"], "mappings": "AAAA,OAAOA,IAAI,MAAM,MAAM;;AAEvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,YAAYA,CAACC,YAAY,EAAEC,KAAK,EAAEC,sBAAsB,GAAG,KAAK,EAAE;EACxF,MAAMC,MAAM,GAAG;IACb,GAAGF;EACL,CAAC;EACD,KAAK,MAAMG,GAAG,IAAIJ,YAAY,EAAE;IAC9B,IAAIK,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACR,YAAY,EAAEI,GAAG,CAAC,EAAE;MAC3D,MAAMK,QAAQ,GAAGL,GAAG;MACpB,IAAIK,QAAQ,KAAK,YAAY,IAAIA,QAAQ,KAAK,OAAO,EAAE;QACrDN,MAAM,CAACM,QAAQ,CAAC,GAAG;UACjB,GAAGT,YAAY,CAACS,QAAQ,CAAC;UACzB,GAAGN,MAAM,CAACM,QAAQ;QACpB,CAAC;MACH,CAAC,MAAM,IAAIA,QAAQ,KAAK,iBAAiB,IAAIA,QAAQ,KAAK,WAAW,EAAE;QACrE,MAAMC,gBAAgB,GAAGV,YAAY,CAACS,QAAQ,CAAC;QAC/C,MAAME,SAAS,GAAGV,KAAK,CAACQ,QAAQ,CAAC;QACjC,IAAI,CAACE,SAAS,EAAE;UACdR,MAAM,CAACM,QAAQ,CAAC,GAAGC,gBAAgB,IAAI,CAAC,CAAC;QAC3C,CAAC,MAAM,IAAI,CAACA,gBAAgB,EAAE;UAC5BP,MAAM,CAACM,QAAQ,CAAC,GAAGE,SAAS;QAC9B,CAAC,MAAM;UACLR,MAAM,CAACM,QAAQ,CAAC,GAAG;YACjB,GAAGE;UACL,CAAC;UACD,KAAK,MAAMC,OAAO,IAAIF,gBAAgB,EAAE;YACtC,IAAIL,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACE,gBAAgB,EAAEE,OAAO,CAAC,EAAE;cACnE,MAAMC,YAAY,GAAGD,OAAO;cAC5BT,MAAM,CAACM,QAAQ,CAAC,CAACI,YAAY,CAAC,GAAGd,YAAY,CAACW,gBAAgB,CAACG,YAAY,CAAC,EAAEF,SAAS,CAACE,YAAY,CAAC,EAAEX,sBAAsB,CAAC;YAChI;UACF;QACF;MACF,CAAC,MAAM,IAAIO,QAAQ,KAAK,WAAW,IAAIP,sBAAsB,IAAID,KAAK,CAACa,SAAS,EAAE;QAChFX,MAAM,CAACW,SAAS,GAAGhB,IAAI,CAACE,YAAY,EAAEc,SAAS,EAAEb,KAAK,EAAEa,SAAS,CAAC;MACpE,CAAC,MAAM,IAAIL,QAAQ,KAAK,OAAO,IAAIP,sBAAsB,IAAID,KAAK,CAACc,KAAK,EAAE;QACxEZ,MAAM,CAACY,KAAK,GAAG;UACb,GAAGf,YAAY,EAAEe,KAAK;UACtB,GAAGd,KAAK,EAAEc;QACZ,CAAC;MACH,CAAC,MAAM,IAAIZ,MAAM,CAACM,QAAQ,CAAC,KAAKO,SAAS,EAAE;QACzCb,MAAM,CAACM,QAAQ,CAAC,GAAGT,YAAY,CAACS,QAAQ,CAAC;MAC3C;IACF;EACF;EACA,OAAON,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}