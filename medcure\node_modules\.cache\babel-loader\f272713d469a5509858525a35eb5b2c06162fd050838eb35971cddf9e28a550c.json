{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\AuthPages\\\\Evolune\\\\medcure\\\\src\\\\App.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport './App.css';\nimport LeftScreen from './LeftScreen';\nimport RightScreen from './RightScreen';\nimport QueryHistory from './QueryHistory';\nimport Settings from './Settings';\nimport MedcodeBanner from './MedcodeBanner';\nimport AuthWrapper from './components/AuthWrapper';\nimport { useState, useEffect } from 'react';\nimport { Routes, Route, useLocation } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Layout({\n  sidebarOpen,\n  toggleSidebar,\n  isMobile\n}) {\n  _s();\n  const location = useLocation();\n  const getPageComponent = () => {\n    switch (location.pathname) {\n      case '/query-history':\n        return /*#__PURE__*/_jsxDEV(QueryHistory, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 16\n        }, this);\n      case '/settings':\n        return /*#__PURE__*/_jsxDEV(Settings, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(RightScreen, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"App\",\n    children: [isMobile && /*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"hamburger\",\n      onClick: toggleSidebar,\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `left-side ${sidebarOpen ? 'open' : ''}`,\n      children: /*#__PURE__*/_jsxDEV(LeftScreen, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `right-side ${sidebarOpen && isMobile ? 'blurred' : ''}`,\n      children: [/*#__PURE__*/_jsxDEV(MedcodeBanner, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 9\n      }, this), getPageComponent()]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 26,\n    columnNumber: 5\n  }, this);\n}\n_s(Layout, \"pkHmaVRPskBaU4tMJuJJpV42k1I=\", false, function () {\n  return [useLocation];\n});\n_c = Layout;\nfunction App() {\n  _s2();\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [isMobile, setIsMobile] = useState(window.innerWidth <= 1024);\n  useEffect(() => {\n    const handleResize = () => {\n      const mobile = window.innerWidth <= 1024;\n      setIsMobile(mobile);\n      if (!mobile) setSidebarOpen(false);\n    };\n    window.addEventListener('resize', handleResize);\n    return () => window.removeEventListener('resize', handleResize);\n  }, []);\n  const toggleSidebar = () => {\n    setSidebarOpen(!sidebarOpen);\n  };\n  return /*#__PURE__*/_jsxDEV(AuthWrapper, {\n    children: /*#__PURE__*/_jsxDEV(Routes, {\n      children: /*#__PURE__*/_jsxDEV(Route, {\n        path: \"*\",\n        element: /*#__PURE__*/_jsxDEV(Layout, {\n          sidebarOpen: sidebarOpen,\n          toggleSidebar: toggleSidebar,\n          isMobile: isMobile\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 66,\n    columnNumber: 5\n  }, this);\n}\n_s2(App, \"WnMSAOpZ6EyEg0vaMs0d/lLckus=\");\n_c2 = App;\nexport default App; // ✅ make sure this line exists\nvar _c, _c2;\n$RefreshReg$(_c, \"Layout\");\n$RefreshReg$(_c2, \"App\");", "map": {"version": 3, "names": ["LeftScreen", "RightScreen", "QueryHistory", "Settings", "MedcodeBanner", "AuthWrapper", "useState", "useEffect", "Routes", "Route", "useLocation", "jsxDEV", "_jsxDEV", "Layout", "sidebarOpen", "toggleSidebar", "isMobile", "_s", "location", "getPageComponent", "pathname", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "children", "onClick", "_c", "App", "_s2", "setSidebarOpen", "setIsMobile", "window", "innerWidth", "handleResize", "mobile", "addEventListener", "removeEventListener", "path", "element", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Documents/AuthPages/Evolune/medcure/src/App.js"], "sourcesContent": ["import './App.css';\nimport LeftScreen from './LeftScreen';\nimport RightScreen from './RightScreen';\nimport QueryHistory from './QueryHistory';\nimport Settings from './Settings';\nimport MedcodeBanner from './MedcodeBanner';\nimport AuthWrapper from './components/AuthWrapper';\nimport { useState, useEffect } from 'react';\nimport { Routes, Route, useLocation } from 'react-router-dom';\n\nfunction Layout({ sidebarOpen, toggleSidebar, isMobile }) {\n  const location = useLocation();\n\n  const getPageComponent = () => {\n    switch (location.pathname) {\n      case '/query-history':\n        return <QueryHistory />;\n      case '/settings':\n        return <Settings />;\n      default:\n        return <RightScreen />;\n    }\n  };\n\n  return (\n    <div className=\"App\">\n      {isMobile && (\n        <button className=\"hamburger\" onClick={toggleSidebar}>\n          <span></span>\n          <span></span>\n          <span></span>\n        </button>\n      )}\n\n      <div className={`left-side ${sidebarOpen ? 'open' : ''}`}>\n        <LeftScreen />\n      </div>\n\n      <div className={`right-side ${sidebarOpen && isMobile ? 'blurred' : ''}`}>\n        <MedcodeBanner />\n        {getPageComponent()}\n      </div>\n    </div>\n  );\n}\n\nfunction App() {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [isMobile, setIsMobile] = useState(window.innerWidth <= 1024);\n\n  useEffect(() => {\n    const handleResize = () => {\n      const mobile = window.innerWidth <= 1024;\n      setIsMobile(mobile);\n      if (!mobile) setSidebarOpen(false);\n    };\n    window.addEventListener('resize', handleResize);\n    return () => window.removeEventListener('resize', handleResize);\n  }, []);\n\n  const toggleSidebar = () => {\n    setSidebarOpen(!sidebarOpen);\n  };\n\n  return (\n    <AuthWrapper>\n      <Routes>\n        <Route\n          path=\"*\"\n          element={\n            <Layout\n              sidebarOpen={sidebarOpen}\n              toggleSidebar={toggleSidebar}\n              isMobile={isMobile}\n            />\n          }\n        />\n      </Routes>\n    </AuthWrapper>\n  );\n}\n\nexport default App; // ✅ make sure this line exists\n"], "mappings": ";;;AAAA,OAAO,WAAW;AAClB,OAAOA,UAAU,MAAM,cAAc;AACrC,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,WAAW,MAAM,0BAA0B;AAClD,SAASC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,SAASC,MAAM,EAAEC,KAAK,EAAEC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9D,SAASC,MAAMA,CAAC;EAAEC,WAAW;EAAEC,aAAa;EAAEC;AAAS,CAAC,EAAE;EAAAC,EAAA;EACxD,MAAMC,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAE9B,MAAMS,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,QAAQD,QAAQ,CAACE,QAAQ;MACvB,KAAK,gBAAgB;QACnB,oBAAOR,OAAA,CAACV,YAAY;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACzB,KAAK,WAAW;QACd,oBAAOZ,OAAA,CAACT,QAAQ;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACrB;QACE,oBAAOZ,OAAA,CAACX,WAAW;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IAC1B;EACF,CAAC;EAED,oBACEZ,OAAA;IAAKa,SAAS,EAAC,KAAK;IAAAC,QAAA,GACjBV,QAAQ,iBACPJ,OAAA;MAAQa,SAAS,EAAC,WAAW;MAACE,OAAO,EAAEZ,aAAc;MAAAW,QAAA,gBACnDd,OAAA;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbZ,OAAA;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbZ,OAAA;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CACT,eAEDZ,OAAA;MAAKa,SAAS,EAAE,aAAaX,WAAW,GAAG,MAAM,GAAG,EAAE,EAAG;MAAAY,QAAA,eACvDd,OAAA,CAACZ,UAAU;QAAAqB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACX,CAAC,eAENZ,OAAA;MAAKa,SAAS,EAAE,cAAcX,WAAW,IAAIE,QAAQ,GAAG,SAAS,GAAG,EAAE,EAAG;MAAAU,QAAA,gBACvEd,OAAA,CAACR,aAAa;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAChBL,gBAAgB,CAAC,CAAC;IAAA;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACP,EAAA,CAlCQJ,MAAM;EAAA,QACIH,WAAW;AAAA;AAAAkB,EAAA,GADrBf,MAAM;AAoCf,SAASgB,GAAGA,CAAA,EAAG;EAAAC,GAAA;EACb,MAAM,CAAChB,WAAW,EAAEiB,cAAc,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACU,QAAQ,EAAEgB,WAAW,CAAC,GAAG1B,QAAQ,CAAC2B,MAAM,CAACC,UAAU,IAAI,IAAI,CAAC;EAEnE3B,SAAS,CAAC,MAAM;IACd,MAAM4B,YAAY,GAAGA,CAAA,KAAM;MACzB,MAAMC,MAAM,GAAGH,MAAM,CAACC,UAAU,IAAI,IAAI;MACxCF,WAAW,CAACI,MAAM,CAAC;MACnB,IAAI,CAACA,MAAM,EAAEL,cAAc,CAAC,KAAK,CAAC;IACpC,CAAC;IACDE,MAAM,CAACI,gBAAgB,CAAC,QAAQ,EAAEF,YAAY,CAAC;IAC/C,OAAO,MAAMF,MAAM,CAACK,mBAAmB,CAAC,QAAQ,EAAEH,YAAY,CAAC;EACjE,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMpB,aAAa,GAAGA,CAAA,KAAM;IAC1BgB,cAAc,CAAC,CAACjB,WAAW,CAAC;EAC9B,CAAC;EAED,oBACEF,OAAA,CAACP,WAAW;IAAAqB,QAAA,eACVd,OAAA,CAACJ,MAAM;MAAAkB,QAAA,eACLd,OAAA,CAACH,KAAK;QACJ8B,IAAI,EAAC,GAAG;QACRC,OAAO,eACL5B,OAAA,CAACC,MAAM;UACLC,WAAW,EAAEA,WAAY;UACzBC,aAAa,EAAEA,aAAc;UAC7BC,QAAQ,EAAEA;QAAS;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB;MACF;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAElB;AAACM,GAAA,CAlCQD,GAAG;AAAAY,GAAA,GAAHZ,GAAG;AAoCZ,eAAeA,GAAG,CAAC,CAAC;AAAA,IAAAD,EAAA,EAAAa,GAAA;AAAAC,YAAA,CAAAd,EAAA;AAAAc,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}