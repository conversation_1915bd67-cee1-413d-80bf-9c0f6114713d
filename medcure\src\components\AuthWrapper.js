import React, { useState, useEffect } from 'react';
import { authService } from '../services/api';

const AuthWrapper = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const checkAuth = () => {
      const authenticated = authService.isAuthenticated();
      const currentUser = authService.getCurrentUser();
      
      setIsAuthenticated(authenticated);
      setUser(currentUser);
      setLoading(false);
    };

    checkAuth();
  }, []);

  if (loading) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100vh',
        fontSize: '18px'
      }}>
        Loading...
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div style={{ 
        display: 'flex', 
        flexDirection: 'column',
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100vh',
        padding: '20px',
        textAlign: 'center'
      }}>
        <h2>Authentication Required</h2>
        <p>Please log in to access the MedCure application.</p>
        <p>
          <a 
            href="http://localhost:3001" 
            style={{ 
              color: '#007bff', 
              textDecoration: 'none',
              fontSize: '18px',
              fontWeight: 'bold'
            }}
          >
            Go to Login Page
          </a>
        </p>
        <div style={{ marginTop: '20px', fontSize: '14px', color: '#666' }}>
          <p>The authentication system runs on a separate application.</p>
          <p>After logging in, return to this page to access MedCure.</p>
        </div>
      </div>
    );
  }

  return (
    <div>
      {/* Optional: Display user info */}
      <div style={{ 
        position: 'fixed', 
        top: '10px', 
        right: '10px', 
        background: '#f8f9fa', 
        padding: '5px 10px', 
        borderRadius: '5px',
        fontSize: '12px',
        zIndex: 1000
      }}>
        Welcome, {user?.name || user?.email || 'User'}
        <button 
          onClick={() => {
            authService.logout();
            window.location.reload();
          }}
          style={{ 
            marginLeft: '10px', 
            padding: '2px 8px', 
            fontSize: '10px',
            background: '#dc3545',
            color: 'white',
            border: 'none',
            borderRadius: '3px',
            cursor: 'pointer'
          }}
        >
          Logout
        </button>
      </div>
      {children}
    </div>
  );
};

export default AuthWrapper;
