{"ast": null, "code": "export { default } from \"./deepmerge.js\";\nexport * from \"./deepmerge.js\";", "map": {"version": 3, "names": ["default"], "sources": ["C:/Users/<USER>/OneDrive/Documents/AuthPages/Evolune/medcure/node_modules/@mui/utils/esm/deepmerge/index.js"], "sourcesContent": ["export { default } from \"./deepmerge.js\";\nexport * from \"./deepmerge.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,gBAAgB;AACxC,cAAc,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}