{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\AuthPages\\\\Evolune\\\\medcure\\\\src\\\\QueryHistory.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport Footer from './Footer';\nimport './QueryHistory.css';\nimport { medicalService, authService, apiUtils } from './services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst QueryHistory = () => {\n  _s();\n  const [queries, setQueries] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  useEffect(() => {\n    const fetchQueryHistory = async () => {\n      try {\n        if (!authService.isAuthenticated()) {\n          setError('Please log in to view your query history.');\n          setLoading(false);\n          return;\n        }\n        const currentUser = authService.getCurrentUser();\n        if (!currentUser) {\n          setError('User information not found. Please log in again.');\n          setLoading(false);\n          return;\n        }\n\n        // Fetch from API\n        const historyData = await medicalService.getQueryHistory(currentUser.id, 50);\n        setQueries(historyData.history || []);\n\n        // Also merge with localStorage data for backward compatibility\n        const localQueries = JSON.parse(localStorage.getItem('queryHistory') || '[]');\n        if (localQueries.length > 0) {\n          // Combine and deduplicate\n          const allQueries = [...(historyData.history || []), ...localQueries];\n          setQueries(allQueries);\n        }\n      } catch (err) {\n        console.error('Error fetching query history:', err);\n        // Fallback to localStorage\n        const savedQueries = JSON.parse(localStorage.getItem('queryHistory') || '[]');\n        setQueries(savedQueries);\n        if (savedQueries.length === 0) {\n          setError(apiUtils.formatErrorMessage(err));\n        }\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchQueryHistory();\n  }, []);\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"query-history-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-message\",\n        children: \"Loading your query history...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"query-history-container\",\n    children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error-message\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 17\n    }, this), queries.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"empty-message\",\n      children: [\"You haven't made any queries yet. \", /*#__PURE__*/_jsxDEV(\"a\", {\n        href: \"/dashboard\",\n        children: \"Go to the Dashboard to start!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 45\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 69,\n      columnNumber: 9\n    }, this) : queries.map((q, index) => {\n      var _q$query, _q$query2;\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"query-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"query-header\",\n          children: [\"Query: \", (_q$query = q.query) === null || _q$query === void 0 ? void 0 : _q$query.substring(0, 100), ((_q$query2 = q.query) === null || _q$query2 === void 0 ? void 0 : _q$query2.length) > 100 ? '...' : '']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"query-body\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Query:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 79,\n              columnNumber: 18\n            }, this), \" \", q.query]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Role:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 80,\n              columnNumber: 18\n            }, this), \" \", q.role]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Time:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 81,\n              columnNumber: 18\n            }, this), \" \", q.timestamp || q.time]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 15\n          }, this), q.has_image && /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Included Image:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 82,\n              columnNumber: 34\n            }, this), \" Yes\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 31\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"rerun-btn\",\n            onClick: () => {\n              localStorage.setItem('rerunQuery', JSON.stringify({\n                query: q.query,\n                role: q.role,\n                has_image: q.has_image\n              }));\n              window.location.href = '/dashboard'; // redirect to MedicalQuery\n            },\n            children: \"RERUN THIS QUERY\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 13\n        }, this)]\n      }, index, true, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 11\n      }, this);\n    }), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 65,\n    columnNumber: 5\n  }, this);\n};\n_s(QueryHistory, \"k3aCRdlzP3YTofyjNojZBA+k0TE=\");\n_c = QueryHistory;\nexport default QueryHistory;\nvar _c;\n$RefreshReg$(_c, \"QueryHistory\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Footer", "medicalService", "authService", "apiUtils", "jsxDEV", "_jsxDEV", "QueryHistory", "_s", "queries", "setQueries", "loading", "setLoading", "error", "setError", "fetchQueryHistory", "isAuthenticated", "currentUser", "getCurrentUser", "historyData", "getQueryHistory", "id", "history", "localQueries", "JSON", "parse", "localStorage", "getItem", "length", "allQueries", "err", "console", "savedQueries", "formatErrorMessage", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "href", "map", "q", "index", "_q$query", "_q$query2", "query", "substring", "role", "timestamp", "time", "has_image", "onClick", "setItem", "stringify", "window", "location", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Documents/AuthPages/Evolune/medcure/src/QueryHistory.js"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\r\nimport Footer from './Footer';\r\nimport './QueryHistory.css';\r\nimport { medicalService, authService, apiUtils } from './services/api';\r\n\r\nconst QueryHistory = () => {\r\n  const [queries, setQueries] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState('');\r\n\r\n  useEffect(() => {\r\n    const fetchQueryHistory = async () => {\r\n      try {\r\n        if (!authService.isAuthenticated()) {\r\n          setError('Please log in to view your query history.');\r\n          setLoading(false);\r\n          return;\r\n        }\r\n\r\n        const currentUser = authService.getCurrentUser();\r\n        if (!currentUser) {\r\n          setError('User information not found. Please log in again.');\r\n          setLoading(false);\r\n          return;\r\n        }\r\n\r\n        // Fetch from API\r\n        const historyData = await medicalService.getQueryHistory(currentUser.id, 50);\r\n        setQueries(historyData.history || []);\r\n\r\n        // Also merge with localStorage data for backward compatibility\r\n        const localQueries = JSON.parse(localStorage.getItem('queryHistory') || '[]');\r\n        if (localQueries.length > 0) {\r\n          // Combine and deduplicate\r\n          const allQueries = [...(historyData.history || []), ...localQueries];\r\n          setQueries(allQueries);\r\n        }\r\n\r\n      } catch (err) {\r\n        console.error('Error fetching query history:', err);\r\n        // Fallback to localStorage\r\n        const savedQueries = JSON.parse(localStorage.getItem('queryHistory') || '[]');\r\n        setQueries(savedQueries);\r\n        if (savedQueries.length === 0) {\r\n          setError(apiUtils.formatErrorMessage(err));\r\n        }\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchQueryHistory();\r\n  }, []);\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"query-history-container\">\r\n        <div className=\"loading-message\">Loading your query history...</div>\r\n        <Footer />\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"query-history-container\">\r\n      {error && <div className=\"error-message\">{error}</div>}\r\n\r\n      {queries.length === 0 ? (\r\n        <div className=\"empty-message\">\r\n          You haven't made any queries yet. <a href=\"/dashboard\">Go to the Dashboard to start!</a>\r\n        </div>\r\n      ) : (\r\n        queries.map((q, index) => (\r\n          <div key={index} className=\"query-card\">\r\n            <div className=\"query-header\">\r\n              Query: {q.query?.substring(0, 100)}{q.query?.length > 100 ? '...' : ''}\r\n            </div>\r\n            <div className=\"query-body\">\r\n              <p><strong>Query:</strong> {q.query}</p>\r\n              <p><strong>Role:</strong> {q.role}</p>\r\n              <p><strong>Time:</strong> {q.timestamp || q.time}</p>\r\n              {q.has_image && <p><strong>Included Image:</strong> Yes</p>}\r\n              <button\r\n                className=\"rerun-btn\"\r\n                onClick={() => {\r\n                  localStorage.setItem('rerunQuery', JSON.stringify({\r\n                    query: q.query,\r\n                    role: q.role,\r\n                    has_image: q.has_image\r\n                  }));\r\n                  window.location.href = '/dashboard'; // redirect to MedicalQuery\r\n                }}\r\n              >\r\n                RERUN THIS QUERY\r\n              </button>\r\n            </div>\r\n          </div>\r\n        ))\r\n      )}\r\n      <Footer />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default QueryHistory;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAO,oBAAoB;AAC3B,SAASC,cAAc,EAAEC,WAAW,EAAEC,QAAQ,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvE,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGV,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACW,OAAO,EAAEC,UAAU,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACa,KAAK,EAAEC,QAAQ,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EAEtCD,SAAS,CAAC,MAAM;IACd,MAAMgB,iBAAiB,GAAG,MAAAA,CAAA,KAAY;MACpC,IAAI;QACF,IAAI,CAACZ,WAAW,CAACa,eAAe,CAAC,CAAC,EAAE;UAClCF,QAAQ,CAAC,2CAA2C,CAAC;UACrDF,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;QAEA,MAAMK,WAAW,GAAGd,WAAW,CAACe,cAAc,CAAC,CAAC;QAChD,IAAI,CAACD,WAAW,EAAE;UAChBH,QAAQ,CAAC,kDAAkD,CAAC;UAC5DF,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;;QAEA;QACA,MAAMO,WAAW,GAAG,MAAMjB,cAAc,CAACkB,eAAe,CAACH,WAAW,CAACI,EAAE,EAAE,EAAE,CAAC;QAC5EX,UAAU,CAACS,WAAW,CAACG,OAAO,IAAI,EAAE,CAAC;;QAErC;QACA,MAAMC,YAAY,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC,IAAI,IAAI,CAAC;QAC7E,IAAIJ,YAAY,CAACK,MAAM,GAAG,CAAC,EAAE;UAC3B;UACA,MAAMC,UAAU,GAAG,CAAC,IAAIV,WAAW,CAACG,OAAO,IAAI,EAAE,CAAC,EAAE,GAAGC,YAAY,CAAC;UACpEb,UAAU,CAACmB,UAAU,CAAC;QACxB;MAEF,CAAC,CAAC,OAAOC,GAAG,EAAE;QACZC,OAAO,CAAClB,KAAK,CAAC,+BAA+B,EAAEiB,GAAG,CAAC;QACnD;QACA,MAAME,YAAY,GAAGR,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC,IAAI,IAAI,CAAC;QAC7EjB,UAAU,CAACsB,YAAY,CAAC;QACxB,IAAIA,YAAY,CAACJ,MAAM,KAAK,CAAC,EAAE;UAC7Bd,QAAQ,CAACV,QAAQ,CAAC6B,kBAAkB,CAACH,GAAG,CAAC,CAAC;QAC5C;MACF,CAAC,SAAS;QACRlB,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDG,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,EAAE,CAAC;EAEN,IAAIJ,OAAO,EAAE;IACX,oBACEL,OAAA;MAAK4B,SAAS,EAAC,yBAAyB;MAAAC,QAAA,gBACtC7B,OAAA;QAAK4B,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAAC;MAA6B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACpEjC,OAAA,CAACL,MAAM;QAAAmC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC;EAEV;EAEA,oBACEjC,OAAA;IAAK4B,SAAS,EAAC,yBAAyB;IAAAC,QAAA,GACrCtB,KAAK,iBAAIP,OAAA;MAAK4B,SAAS,EAAC,eAAe;MAAAC,QAAA,EAAEtB;IAAK;MAAAuB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,EAErD9B,OAAO,CAACmB,MAAM,KAAK,CAAC,gBACnBtB,OAAA;MAAK4B,SAAS,EAAC,eAAe;MAAAC,QAAA,GAAC,oCACK,eAAA7B,OAAA;QAAGkC,IAAI,EAAC,YAAY;QAAAL,QAAA,EAAC;MAA6B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrF,CAAC,GAEN9B,OAAO,CAACgC,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK;MAAA,IAAAC,QAAA,EAAAC,SAAA;MAAA,oBACnBvC,OAAA;QAAiB4B,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACrC7B,OAAA;UAAK4B,SAAS,EAAC,cAAc;UAAAC,QAAA,GAAC,SACrB,GAAAS,QAAA,GAACF,CAAC,CAACI,KAAK,cAAAF,QAAA,uBAAPA,QAAA,CAASG,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,EAAAF,SAAA,GAAAH,CAAC,CAACI,KAAK,cAAAD,SAAA,uBAAPA,SAAA,CAASjB,MAAM,IAAG,GAAG,GAAG,KAAK,GAAG,EAAE;QAAA;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnE,CAAC,eACNjC,OAAA;UAAK4B,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB7B,OAAA;YAAA6B,QAAA,gBAAG7B,OAAA;cAAA6B,QAAA,EAAQ;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACG,CAAC,CAACI,KAAK;UAAA;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxCjC,OAAA;YAAA6B,QAAA,gBAAG7B,OAAA;cAAA6B,QAAA,EAAQ;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACG,CAAC,CAACM,IAAI;UAAA;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtCjC,OAAA;YAAA6B,QAAA,gBAAG7B,OAAA;cAAA6B,QAAA,EAAQ;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACG,CAAC,CAACO,SAAS,IAAIP,CAAC,CAACQ,IAAI;UAAA;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EACpDG,CAAC,CAACS,SAAS,iBAAI7C,OAAA;YAAA6B,QAAA,gBAAG7B,OAAA;cAAA6B,QAAA,EAAQ;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,QAAI;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC3DjC,OAAA;YACE4B,SAAS,EAAC,WAAW;YACrBkB,OAAO,EAAEA,CAAA,KAAM;cACb1B,YAAY,CAAC2B,OAAO,CAAC,YAAY,EAAE7B,IAAI,CAAC8B,SAAS,CAAC;gBAChDR,KAAK,EAAEJ,CAAC,CAACI,KAAK;gBACdE,IAAI,EAAEN,CAAC,CAACM,IAAI;gBACZG,SAAS,EAAET,CAAC,CAACS;cACf,CAAC,CAAC,CAAC;cACHI,MAAM,CAACC,QAAQ,CAAChB,IAAI,GAAG,YAAY,CAAC,CAAC;YACvC,CAAE;YAAAL,QAAA,EACH;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA,GAtBEI,KAAK;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAuBV,CAAC;IAAA,CACP,CACF,eACDjC,OAAA,CAACL,MAAM;MAAAmC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAAC/B,EAAA,CAjGID,YAAY;AAAAkD,EAAA,GAAZlD,YAAY;AAmGlB,eAAeA,YAAY;AAAC,IAAAkD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}