{"ast": null, "code": "const deepOrange = {\n  50: '#fbe9e7',\n  100: '#ffccbc',\n  200: '#ffab91',\n  300: '#ff8a65',\n  400: '#ff7043',\n  500: '#ff5722',\n  600: '#f4511e',\n  700: '#e64a19',\n  800: '#d84315',\n  900: '#bf360c',\n  A100: '#ff9e80',\n  A200: '#ff6e40',\n  A400: '#ff3d00',\n  A700: '#dd2c00'\n};\nexport default deepOrange;", "map": {"version": 3, "names": ["deepOrange", "A100", "A200", "A400", "A700"], "sources": ["C:/Users/<USER>/OneDrive/Documents/AuthPages/Evolune/medcure/node_modules/@mui/material/esm/colors/deepOrange.js"], "sourcesContent": ["const deepOrange = {\n  50: '#fbe9e7',\n  100: '#ffccbc',\n  200: '#ffab91',\n  300: '#ff8a65',\n  400: '#ff7043',\n  500: '#ff5722',\n  600: '#f4511e',\n  700: '#e64a19',\n  800: '#d84315',\n  900: '#bf360c',\n  A100: '#ff9e80',\n  A200: '#ff6e40',\n  A400: '#ff3d00',\n  A700: '#dd2c00'\n};\nexport default deepOrange;"], "mappings": "AAAA,MAAMA,UAAU,GAAG;EACjB,EAAE,EAAE,SAAS;EACb,GAAG,EAAE,SAAS;EACd,GAAG,EAAE,SAAS;EACd,GAAG,EAAE,SAAS;EACd,GAAG,EAAE,SAAS;EACd,GAAG,EAAE,SAAS;EACd,GAAG,EAAE,SAAS;EACd,GAAG,EAAE,SAAS;EACd,GAAG,EAAE,SAAS;EACd,GAAG,EAAE,SAAS;EACdC,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE;AACR,CAAC;AACD,eAAeJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}