{"ast": null, "code": "\"use client\";\n\nimport createSvgIcon from \"./utils/createSvgIcon.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"M21.98 14H22zM5.35 13c1.19 0 1.42 1 3.33 1 1.95 0 2.09-1 3.33-1 1.19 0 1.42 1 3.33 1 1.95 0 2.09-1 3.33-1 .93 0 1.05.45 2.01.79.63.22 1.3-.24 1.3-.91 0-.52-.23-.83-.64-.97-.6-.22-1.15-.9-2.69-.9-1.95 0-2.09 1-3.33 1-1.19 0-1.42-1-3.33-1-1.95 0-2.09 1-3.33 1-1.19 0-1.42-1-3.33-1-1.54 0-2.13.71-2.68.91-.41.13-.65.43-.65.97 0 .67.66 1.13 1.29.91 1.06-.36 1.1-.8 2.06-.8m13.32 2c-1.95 0-2.09 1-3.33 1-1.19 0-1.42-1-3.33-1-1.95 0-2.1 1-3.34 1s-1.38-1-3.33-1c-1.53 0-2.15.71-2.69.91-.41.14-.65.45-.65.98 0 .67.66 1.13 1.3.91 1.02-.36 1.08-.8 2.04-.8 1.24 0 1.38 1 3.33 1s2.1-1 3.34-1c1.19 0 1.42 1 3.33 1 1.94 0 2.09-1 3.33-1 .94 0 1.06.46 2.03.8.63.22 1.3-.24 1.3-.91 0-.53-.24-.83-.65-.98-.53-.19-1.14-.91-2.68-.91M5.35 9c1.19 0 1.42 1 3.33 1 1.95 0 2.09-1 3.33-1 1.19 0 1.42 1 3.33 1 1.95 0 2.09-1 3.33-1 .93 0 1.05.45 2.01.79.63.22 1.3-.24 1.3-.91 0-.52-.23-.83-.64-.97-.6-.23-1.15-.91-2.69-.91-1.95 0-2.09 1-3.33 1-1.19 0-1.42-1-3.33-1-1.95 0-2.09 1-3.33 1-1.19 0-1.42-1-3.33-1-1.54 0-2.13.71-2.68.91-.41.14-.65.44-.65.98 0 .67.66 1.13 1.29.91 1.06-.36 1.1-.8 2.06-.8\"\n}), 'WaterRounded');", "map": {"version": 3, "names": ["createSvgIcon", "jsx", "_jsx", "d"], "sources": ["C:/Users/<USER>/OneDrive/Documents/AuthPages/Evolune/medcure/node_modules/@mui/icons-material/esm/WaterRounded.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from \"./utils/createSvgIcon.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"M21.98 14H22zM5.35 13c1.19 0 1.42 1 3.33 1 1.95 0 2.09-1 3.33-1 1.19 0 1.42 1 3.33 1 1.95 0 2.09-1 3.33-1 .93 0 1.05.45 2.01.79.63.22 1.3-.24 1.3-.91 0-.52-.23-.83-.64-.97-.6-.22-1.15-.9-2.69-.9-1.95 0-2.09 1-3.33 1-1.19 0-1.42-1-3.33-1-1.95 0-2.09 1-3.33 1-1.19 0-1.42-1-3.33-1-1.54 0-2.13.71-2.68.91-.41.13-.65.43-.65.97 0 .67.66 1.13 1.29.91 1.06-.36 1.1-.8 2.06-.8m13.32 2c-1.95 0-2.09 1-3.33 1-1.19 0-1.42-1-3.33-1-1.95 0-2.1 1-3.34 1s-1.38-1-3.33-1c-1.53 0-2.15.71-2.69.91-.41.14-.65.45-.65.98 0 .67.66 1.13 1.3.91 1.02-.36 1.08-.8 2.04-.8 1.24 0 1.38 1 3.33 1s2.1-1 3.34-1c1.19 0 1.42 1 3.33 1 1.94 0 2.09-1 3.33-1 .94 0 1.06.46 2.03.8.63.22 1.3-.24 1.3-.91 0-.53-.24-.83-.65-.98-.53-.19-1.14-.91-2.68-.91M5.35 9c1.19 0 1.42 1 3.33 1 1.95 0 2.09-1 3.33-1 1.19 0 1.42 1 3.33 1 1.95 0 2.09-1 3.33-1 .93 0 1.05.45 2.01.79.63.22 1.3-.24 1.3-.91 0-.52-.23-.83-.64-.97-.6-.23-1.15-.91-2.69-.91-1.95 0-2.09 1-3.33 1-1.19 0-1.42-1-3.33-1-1.95 0-2.09 1-3.33 1-1.19 0-1.42-1-3.33-1-1.54 0-2.13.71-2.68.91-.41.14-.65.44-.65.98 0 .67.66 1.13 1.29.91 1.06-.36 1.1-.8 2.06-.8\"\n}), 'WaterRounded');"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,aAAa,MAAM,0BAA0B;AACpD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,eAAeF,aAAa,CAAC,aAAaE,IAAI,CAAC,MAAM,EAAE;EACrDC,CAAC,EAAE;AACL,CAAC,CAAC,EAAE,cAAc,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}