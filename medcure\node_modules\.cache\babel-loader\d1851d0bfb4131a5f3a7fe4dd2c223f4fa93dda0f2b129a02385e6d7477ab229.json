{"ast": null, "code": "export { default } from \"./styled.js\";", "map": {"version": 3, "names": ["default"], "sources": ["C:/Users/<USER>/OneDrive/Documents/AuthPages/Evolune/medcure/node_modules/@mui/system/esm/styled/index.js"], "sourcesContent": ["export { default } from \"./styled.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}