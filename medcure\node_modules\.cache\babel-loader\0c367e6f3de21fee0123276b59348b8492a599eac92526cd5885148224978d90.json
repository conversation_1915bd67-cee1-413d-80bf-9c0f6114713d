{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport { useTheme as useThemeSystem } from '@mui/system';\nimport defaultTheme from \"./defaultTheme.js\";\nimport THEME_ID from \"./identifier.js\";\nexport default function useTheme() {\n  const theme = useThemeSystem(defaultTheme);\n  if (process.env.NODE_ENV !== 'production') {\n    // TODO: uncomment once we enable eslint-plugin-react-compiler // eslint-disable-next-line react-compiler/react-compiler\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useDebugValue(theme);\n  }\n  return theme[THEME_ID] || theme;\n}", "map": {"version": 3, "names": ["React", "useTheme", "useThemeSystem", "defaultTheme", "THEME_ID", "theme", "process", "env", "NODE_ENV", "useDebugValue"], "sources": ["C:/Users/<USER>/OneDrive/Documents/AuthPages/Evolune/medcure/node_modules/@mui/material/esm/styles/useTheme.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport { useTheme as useThemeSystem } from '@mui/system';\nimport defaultTheme from \"./defaultTheme.js\";\nimport THEME_ID from \"./identifier.js\";\nexport default function useTheme() {\n  const theme = useThemeSystem(defaultTheme);\n  if (process.env.NODE_ENV !== 'production') {\n    // TODO: uncomment once we enable eslint-plugin-react-compiler // eslint-disable-next-line react-compiler/react-compiler\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useDebugValue(theme);\n  }\n  return theme[THEME_ID] || theme;\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,IAAIC,cAAc,QAAQ,aAAa;AACxD,OAAOC,YAAY,MAAM,mBAAmB;AAC5C,OAAOC,QAAQ,MAAM,iBAAiB;AACtC,eAAe,SAASH,QAAQA,CAAA,EAAG;EACjC,MAAMI,KAAK,GAAGH,cAAc,CAACC,YAAY,CAAC;EAC1C,IAAIG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC;IACA;IACAR,KAAK,CAACS,aAAa,CAACJ,KAAK,CAAC;EAC5B;EACA,OAAOA,KAAK,CAACD,QAAQ,CAAC,IAAIC,KAAK;AACjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}