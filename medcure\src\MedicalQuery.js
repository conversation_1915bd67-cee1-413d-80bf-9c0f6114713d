import React, { useState, useEffect, useCallback } from 'react';
import './MedicalQuery.css';
import { medicalService, authService, apiUtils } from './services/api';

const MedicalQuery = () => {
  const [inputMethod, setInputMethod] = useState('Text');
  const [isRecording, setIsRecording] = useState(false);
  const [query, setQuery] = useState('');
  const [imageFile, setImageFile] = useState(null);
  const [response, setResponse] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [selectedRole, setSelectedRole] = useState('Common User');
  const [selectedLanguage, setSelectedLanguage] = useState('English');

  const handleSubmit = useCallback(async () => {
    if (!query && !imageFile) {
      setError('Please provide a question or upload a medical image.');
      return;
    }

    // Check if user is authenticated
    if (!authService.isAuthenticated()) {
      setError('Please log in to submit medical queries.');
      return;
    }

    const currentUser = authService.getCurrentUser();
    if (!currentUser) {
      setError('User information not found. Please log in again.');
      return;
    }

    setError('');
    setResponse('');
    setLoading(true);

    try {
      let result;

      if (imageFile) {
        // Submit query with file
        const formData = new FormData();
        formData.append('user_id', currentUser.id);
        formData.append('query', query || 'Please analyze this medical image');
        formData.append('role', selectedRole);
        formData.append('language', selectedLanguage);
        formData.append('file', imageFile);

        result = await medicalService.submitQueryWithFile(formData);
      } else {
        // Submit text-only query
        const queryData = {
          user_id: currentUser.id,
          query: query,
          role: selectedRole,
          language: selectedLanguage,
          has_image: false
        };

        result = await medicalService.submitQuery(queryData);
      }

      setResponse(result.response);

      // Save to localStorage for local history as well
      const existingQueries = JSON.parse(localStorage.getItem('queryHistory') || '[]');
      const newQuery = {
        query: query || '[Image Only]',
        role: selectedRole,
        time: new Date().toISOString().slice(0, 19).replace('T', ' '),
        has_image: !!imageFile
      };
      localStorage.setItem('queryHistory', JSON.stringify([newQuery, ...existingQueries]));

    } catch (err) {
      console.error('Query submission error:', err);
      setError(apiUtils.formatErrorMessage(err));
    } finally {
      setLoading(false);
    }
  }, [query, imageFile, selectedRole, selectedLanguage]);

  useEffect(() => {
    const rerun = localStorage.getItem('rerunQuery');
    if (rerun) {
      const parsed = JSON.parse(rerun);
      setQuery(parsed.query);
      setTimeout(() => {
        handleSubmit();
        localStorage.removeItem('rerunQuery');
      }, 500);
    }
  }, [handleSubmit]);

  const handleStartRecording = () => {
    setIsRecording(true);
    if ('webkitSpeechRecognition' in window) {
      const recognition = new window.webkitSpeechRecognition();
      recognition.lang = 'en-US';
      recognition.onresult = (event) => {
        const transcript = event.results[0][0].transcript;
        setQuery(transcript);
        setIsRecording(false);
      };
      recognition.onerror = () => {
        alert('Voice recognition error.');
        setIsRecording(false);
      };
      recognition.start();
    } else {
      alert('Voice recognition not supported in this browser.');
      setIsRecording(false);
    }
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file && ['image/png', 'image/jpeg', 'image/jpg'].includes(file.type)) {
      setImageFile(file);
      setError('');
    } else {
      setImageFile(null);
      setError('Only PNG, JPG, and JPEG files are allowed.');
    }
  };

  return (
    <div className="medical-query-wrapper">
      <h2>What would you like to know?</h2>

      {/* Role Selection */}
      <div className="selection-section">
        <p><strong>Select Your Role:</strong></p>
        <select
          value={selectedRole}
          onChange={(e) => setSelectedRole(e.target.value)}
          className="role-select"
        >
          <option value="Common User">Common User</option>
          <option value="Student">Medical Student</option>
          <option value="Doctor">Doctor/Medical Professional</option>
        </select>
      </div>

      {/* Language Selection */}
      <div className="selection-section">
        <p><strong>Response Language:</strong></p>
        <select
          value={selectedLanguage}
          onChange={(e) => setSelectedLanguage(e.target.value)}
          className="language-select"
        >
          <option value="English">English</option>
          <option value="Spanish">Spanish</option>
          <option value="French">French</option>
          <option value="German">German</option>
          <option value="Hindi">Hindi</option>
        </select>
      </div>

      <p><strong>Input Method:</strong></p>
      <div className="method-toggle">
        <label className={inputMethod === 'Text' ? 'selected' : ''}>
          <input
            type="radio"
            name="method"
            value="Text"
            checked={inputMethod === 'Text'}
            onChange={() => setInputMethod('Text')}
          /> Text
        </label>
        <label className={inputMethod === 'Voice' ? 'selected' : ''}>
          <input
            type="radio"
            name="method"
            value="Voice"
            checked={inputMethod === 'Voice'}
            onChange={() => setInputMethod('Voice')}
          /> Voice
        </label>
      </div>

      {inputMethod === 'Text' ? (
        <textarea
          className="text-box"
          placeholder="Enter your medical query here..."
          value={query}
          onChange={(e) => setQuery(e.target.value)}
        />
      ) : (
        <div className="voice-box">
          <button onClick={handleStartRecording} className="start-btn">🎤 Start Recording</button>
          {isRecording && <p className="recording-text">🎙️ Listening...</p>}
        </div>
      )}

      <div className="upload-section">
        <div className="folder-icon">📁</div>
        <h3>Upload Medical Image</h3>
        <p>Upload X-rays, lab results, medical scans or any relevant images</p>
        <input type="file" accept=".png,.jpg,.jpeg" className="file-upload" onChange={handleFileChange} />
        {imageFile && <p><strong>Selected:</strong> {imageFile.name}</p>}
      </div>

      {error && <p className="error-message">{error}</p>}

      <button
        className="analyze-btn"
        onClick={handleSubmit}
        disabled={loading}
      >
        {loading ? '🔄 Analyzing...' : '🔍 Generate Medical Analysis'}
      </button>

      {response && (
        <div className="response-box">
          <h3>Medical Analysis:</h3>
          <div dangerouslySetInnerHTML={{ __html: response.replace(/\n/g, '<br/>') }} />
        </div>
      )}
    </div>
  );
};

export default MedicalQuery;
