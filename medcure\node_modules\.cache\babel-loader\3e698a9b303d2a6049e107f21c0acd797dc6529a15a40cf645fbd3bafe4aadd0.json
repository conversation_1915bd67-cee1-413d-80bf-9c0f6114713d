{"ast": null, "code": "\"use client\";\n\nimport createSvgIcon from \"./utils/createSvgIcon.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"M3 22q-.825 0-1.4125-.5875T1 20V9h2v11h17v2zm4-4q-.825 0-1.4125-.5875T5 16V5h5V3q0-.825.5875-1.4125T12 1h4q.825 0 1.4125.5875T18 3v2h5v11q0 .825-.5875 1.4125T21 18zm5-13h4V3h-4z\"\n}), 'Cases');", "map": {"version": 3, "names": ["createSvgIcon", "jsx", "_jsx", "d"], "sources": ["C:/Users/<USER>/OneDrive/Documents/AuthPages/Evolune/medcure/node_modules/@mui/icons-material/esm/Cases.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from \"./utils/createSvgIcon.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"M3 22q-.825 0-1.4125-.5875T1 20V9h2v11h17v2zm4-4q-.825 0-1.4125-.5875T5 16V5h5V3q0-.825.5875-1.4125T12 1h4q.825 0 1.4125.5875T18 3v2h5v11q0 .825-.5875 1.4125T21 18zm5-13h4V3h-4z\"\n}), 'Cases');"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,aAAa,MAAM,0BAA0B;AACpD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,eAAeF,aAAa,CAAC,aAAaE,IAAI,CAAC,MAAM,EAAE;EACrDC,CAAC,EAAE;AACL,CAAC,CAAC,EAAE,OAAO,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}