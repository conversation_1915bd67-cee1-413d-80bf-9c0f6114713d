{"ast": null, "code": "export { default } from \"./positions.js\";\nexport * from \"./positions.js\";", "map": {"version": 3, "names": ["default"], "sources": ["C:/Users/<USER>/OneDrive/Documents/AuthPages/Evolune/medcure/node_modules/@mui/system/esm/positions/index.js"], "sourcesContent": ["export { default } from \"./positions.js\";\nexport * from \"./positions.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,gBAAgB;AACxC,cAAc,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}