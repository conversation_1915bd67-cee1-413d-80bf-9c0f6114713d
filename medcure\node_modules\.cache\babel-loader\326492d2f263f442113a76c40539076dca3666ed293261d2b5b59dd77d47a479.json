{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\AuthPages\\\\Evolune\\\\medcure\\\\src\\\\LeftScreen.js\";\nimport './LeftScreen.css';\nimport React, { useState } from 'react';\nimport Avatar from '@mui/material/Avatar';\nimport MenuIcon from '@mui/icons-material/Menu';\nimport { deepOrange } from '@mui/material/colors';\nimport { NavLink } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction LeftScreen() {\n  const navItems = [{\n    label: 'Dashboard',\n    path: '/'\n  }, {\n    label: 'Query History',\n    path: '/query-history'\n  }, {\n    label: 'Settings',\n    path: '/settings'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"LeftScreen\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"header\",\n      children: [/*#__PURE__*/_jsxDEV(\"img\", {\n        src: \"/medical-sign.jpg\",\n        alt: \"Medical Sign\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"MedCode AI\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"centerContainer\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"userProfile\",\n        children: [/*#__PURE__*/_jsxDEV(Avatar, {\n          sx: {\n            bgcolor: deepOrange[500],\n            width: 60,\n            height: 60\n          },\n          children: \"H\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"userDetails\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Username\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 28,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"User Category\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 29,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n      className: \"horizontal-divider\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"nav-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        className: \"nav-heading\",\n        children: \"Navigation\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n        className: \"nav-list\",\n        children: navItems.map(({\n          label,\n          path\n        }) => /*#__PURE__*/_jsxDEV(\"li\", {\n          children: /*#__PURE__*/_jsxDEV(NavLink, {\n            to: path,\n            className: ({\n              isActive\n            }) => `nav-item ${isActive ? 'active' : ''}`,\n            children: label\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 17\n          }, this)\n        }, label, false, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 17\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n      className: \"horizontal-divider\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 56,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bottom-body\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        className: \"nav-heading\",\n        children: \"Your Stats\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"body-items\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Queries\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Days Active\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n      className: \"horizontal-divider\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 74,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"logout_button\",\n      children: \"LOGOUT\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bottom\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"\\xA92025 MedCode AI | MIT License\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 79,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 17,\n    columnNumber: 5\n  }, this);\n}\n_c = LeftScreen;\nexport default LeftScreen;\nvar _c;\n$RefreshReg$(_c, \"LeftScreen\");", "map": {"version": 3, "names": ["React", "useState", "Avatar", "MenuIcon", "deepOrange", "NavLink", "jsxDEV", "_jsxDEV", "LeftScreen", "navItems", "label", "path", "className", "children", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sx", "bgcolor", "width", "height", "map", "to", "isActive", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Documents/AuthPages/Evolune/medcure/src/LeftScreen.js"], "sourcesContent": ["import './LeftScreen.css'\r\nimport React, { useState } from 'react';\r\nimport Avatar from '@mui/material/Avatar';\r\nimport MenuIcon from '@mui/icons-material/Menu';\r\nimport { deepOrange } from '@mui/material/colors';\r\nimport { NavLink } from 'react-router-dom';\r\n\r\nfunction LeftScreen () {\r\n  \r\n  const navItems = [\r\n        { label: 'Dashboard', path: '/' },\r\n        { label: 'Query History', path: '/query-history' },\r\n        { label: 'Settings', path: '/settings' }\r\n    ];\r\n\r\n  return (\r\n    <div className='LeftScreen'>\r\n      <div className='header'>\r\n        <img src=\"/medical-sign.jpg\" alt=\"Medical Sign\" />\r\n        <p>MedCode AI</p>\r\n      </div>\r\n\r\n\r\n      <div className=\"centerContainer\">\r\n        <div className=\"userProfile\">\r\n            <Avatar sx={{ bgcolor: deepOrange[500], width: 60, height: 60 }}>H</Avatar>\r\n            <div className=\"userDetails\">\r\n            <h3>Username</h3>\r\n            <p>User Category</p>\r\n            </div>\r\n        </div>\r\n      </div>\r\n    \r\n      <hr className=\"horizontal-divider\" />\r\n\r\n\r\n\r\n      <div className=\"nav-container\">\r\n        <h4 className=\"nav-heading\">Navigation</h4>\r\n        <ul className=\"nav-list\">\r\n            {navItems.map(({ label, path }) => (\r\n                <li key={label}>\r\n                <NavLink\r\n                    to={path}\r\n                    className={({ isActive }) =>\r\n                    `nav-item ${isActive ? 'active' : ''}`\r\n                    }\r\n                >\r\n                    {label}\r\n                </NavLink>\r\n                </li>\r\n            ))}\r\n        </ul>\r\n      </div>\r\n\r\n      <hr className=\"horizontal-divider\" />\r\n\r\n\r\n\r\n      <div className='bottom-body'>\r\n        <h4 className=\"nav-heading\">Your Stats</h4>\r\n        <div className='body-items'>\r\n          <span>\r\n            <p>Queries</p>\r\n            <h2>0</h2>\r\n          </span>\r\n          <span>\r\n            <p>Days Active</p>\r\n            <h2>2</h2>\r\n          </span>\r\n        </div>\r\n      </div>\r\n      \r\n      <hr className=\"horizontal-divider\" />\r\n\r\n\r\n      <div className='logout_button'>LOGOUT</div>\r\n\r\n      <div className='bottom'>\r\n        <p>©2025 MedCode AI | MIT License</p>\r\n      </div>\r\n      \r\n    </div>\r\n  )\r\n}\r\n\r\nexport default LeftScreen\r\n"], "mappings": ";AAAA,OAAO,kBAAkB;AACzB,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,SAASC,UAAU,QAAQ,sBAAsB;AACjD,SAASC,OAAO,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3C,SAASC,UAAUA,CAAA,EAAI;EAErB,MAAMC,QAAQ,GAAG,CACX;IAAEC,KAAK,EAAE,WAAW;IAAEC,IAAI,EAAE;EAAI,CAAC,EACjC;IAAED,KAAK,EAAE,eAAe;IAAEC,IAAI,EAAE;EAAiB,CAAC,EAClD;IAAED,KAAK,EAAE,UAAU;IAAEC,IAAI,EAAE;EAAY,CAAC,CAC3C;EAEH,oBACEJ,OAAA;IAAKK,SAAS,EAAC,YAAY;IAAAC,QAAA,gBACzBN,OAAA;MAAKK,SAAS,EAAC,QAAQ;MAAAC,QAAA,gBACrBN,OAAA;QAAKO,GAAG,EAAC,mBAAmB;QAACC,GAAG,EAAC;MAAc;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAClDZ,OAAA;QAAAM,QAAA,EAAG;MAAU;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CAAC,eAGNZ,OAAA;MAAKK,SAAS,EAAC,iBAAiB;MAAAC,QAAA,eAC9BN,OAAA;QAAKK,SAAS,EAAC,aAAa;QAAAC,QAAA,gBACxBN,OAAA,CAACL,MAAM;UAACkB,EAAE,EAAE;YAAEC,OAAO,EAAEjB,UAAU,CAAC,GAAG,CAAC;YAAEkB,KAAK,EAAE,EAAE;YAAEC,MAAM,EAAE;UAAG,CAAE;UAAAV,QAAA,EAAC;QAAC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC3EZ,OAAA;UAAKK,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC5BN,OAAA;YAAAM,QAAA,EAAI;UAAQ;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjBZ,OAAA;YAAAM,QAAA,EAAG;UAAa;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENZ,OAAA;MAAIK,SAAS,EAAC;IAAoB;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAIrCZ,OAAA;MAAKK,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5BN,OAAA;QAAIK,SAAS,EAAC,aAAa;QAAAC,QAAA,EAAC;MAAU;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC3CZ,OAAA;QAAIK,SAAS,EAAC,UAAU;QAAAC,QAAA,EACnBJ,QAAQ,CAACe,GAAG,CAAC,CAAC;UAAEd,KAAK;UAAEC;QAAK,CAAC,kBAC1BJ,OAAA;UAAAM,QAAA,eACAN,OAAA,CAACF,OAAO;YACJoB,EAAE,EAAEd,IAAK;YACTC,SAAS,EAAEA,CAAC;cAAEc;YAAS,CAAC,KACxB,YAAYA,QAAQ,GAAG,QAAQ,GAAG,EAAE,EACnC;YAAAb,QAAA,EAEAH;UAAK;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC,GARDT,KAAK;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OASV,CACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAENZ,OAAA;MAAIK,SAAS,EAAC;IAAoB;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAIrCZ,OAAA;MAAKK,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BN,OAAA;QAAIK,SAAS,EAAC,aAAa;QAAAC,QAAA,EAAC;MAAU;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC3CZ,OAAA;QAAKK,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBN,OAAA;UAAAM,QAAA,gBACEN,OAAA;YAAAM,QAAA,EAAG;UAAO;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACdZ,OAAA;YAAAM,QAAA,EAAI;UAAC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACPZ,OAAA;UAAAM,QAAA,gBACEN,OAAA;YAAAM,QAAA,EAAG;UAAW;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAClBZ,OAAA;YAAAM,QAAA,EAAI;UAAC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENZ,OAAA;MAAIK,SAAS,EAAC;IAAoB;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGrCZ,OAAA;MAAKK,SAAS,EAAC,eAAe;MAAAC,QAAA,EAAC;IAAM;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eAE3CZ,OAAA;MAAKK,SAAS,EAAC,QAAQ;MAAAC,QAAA,eACrBN,OAAA;QAAAM,QAAA,EAAG;MAA8B;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAEH,CAAC;AAEV;AAACQ,EAAA,GA7EQnB,UAAU;AA+EnB,eAAeA,UAAU;AAAA,IAAAmB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}