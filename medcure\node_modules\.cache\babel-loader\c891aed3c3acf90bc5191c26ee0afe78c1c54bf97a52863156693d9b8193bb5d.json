{"ast": null, "code": "export default function shouldSkipGeneratingVar(keys) {\n  return !!keys[0].match(/(cssVarPrefix|colorSchemeSelector|rootSelector|typography|mixins|breakpoints|direction|transitions)/) || !!keys[0].match(/sxConfig$/) ||\n  // ends with sxConfig\n  keys[0] === 'palette' && !!keys[1]?.match(/(mode|contrastThreshold|tonalOffset)/);\n}", "map": {"version": 3, "names": ["shouldSkipGeneratingVar", "keys", "match"], "sources": ["C:/Users/<USER>/OneDrive/Documents/AuthPages/Evolune/medcure/node_modules/@mui/material/esm/styles/shouldSkipGeneratingVar.js"], "sourcesContent": ["export default function shouldSkipGeneratingVar(keys) {\n  return !!keys[0].match(/(cssVarPrefix|colorSchemeSelector|rootSelector|typography|mixins|breakpoints|direction|transitions)/) || !!keys[0].match(/sxConfig$/) ||\n  // ends with sxConfig\n  keys[0] === 'palette' && !!keys[1]?.match(/(mode|contrastThreshold|tonalOffset)/);\n}"], "mappings": "AAAA,eAAe,SAASA,uBAAuBA,CAACC,IAAI,EAAE;EACpD,OAAO,CAAC,CAACA,IAAI,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,qGAAqG,CAAC,IAAI,CAAC,CAACD,IAAI,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,WAAW,CAAC;EAC7J;EACAD,IAAI,CAAC,CAAC,CAAC,KAAK,SAAS,IAAI,CAAC,CAACA,IAAI,CAAC,CAAC,CAAC,EAAEC,KAAK,CAAC,sCAAsC,CAAC;AACnF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}