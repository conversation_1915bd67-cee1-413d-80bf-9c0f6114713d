{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\AuthPages\\\\Evolune\\\\medcure\\\\src\\\\MedicalQuery.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport './MedicalQuery.css';\nimport { medicalService, apiUtils } from './services/api';\nimport { useAuth } from './context/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MedicalQuery = () => {\n  _s();\n  const [inputMethod, setInputMethod] = useState('Text');\n  const [isRecording, setIsRecording] = useState(false);\n  const [query, setQuery] = useState('');\n  const [imageFile, setImageFile] = useState(null);\n  const [response, setResponse] = useState('');\n  const [error, setError] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [selectedRole, setSelectedRole] = useState('Common User');\n  const [selectedLanguage, setSelectedLanguage] = useState('English');\n  const {\n    requireAuth,\n    user\n  } = useAuth();\n  const handleSubmit = useCallback(async () => {\n    if (!query && !imageFile) {\n      setError('Please provide a question or upload a medical image.');\n      return;\n    }\n\n    // Check if user is authenticated using the auth context\n    if (!requireAuth()) {\n      return; // requireAuth will show the auth modal\n    }\n    if (!user) {\n      setError('User information not found. Please log in again.');\n      return;\n    }\n    setError('');\n    setResponse('');\n    setLoading(true);\n    try {\n      let result;\n      if (imageFile) {\n        // Submit query with file\n        const formData = new FormData();\n        formData.append('user_id', user.id);\n        formData.append('query', query || 'Please analyze this medical image');\n        formData.append('role', selectedRole);\n        formData.append('language', selectedLanguage);\n        formData.append('file', imageFile);\n        result = await medicalService.submitQueryWithFile(formData);\n      } else {\n        // Submit text-only query\n        const queryData = {\n          user_id: user.id,\n          query: query,\n          role: selectedRole,\n          language: selectedLanguage,\n          has_image: false\n        };\n        result = await medicalService.submitQuery(queryData);\n      }\n      setResponse(result.response);\n\n      // Save to localStorage for local history as well\n      const existingQueries = JSON.parse(localStorage.getItem('queryHistory') || '[]');\n      const newQuery = {\n        query: query || '[Image Only]',\n        role: selectedRole,\n        time: new Date().toISOString().slice(0, 19).replace('T', ' '),\n        has_image: !!imageFile\n      };\n      localStorage.setItem('queryHistory', JSON.stringify([newQuery, ...existingQueries]));\n    } catch (err) {\n      console.error('Query submission error:', err);\n      setError(apiUtils.formatErrorMessage(err));\n    } finally {\n      setLoading(false);\n    }\n  }, [query, imageFile, selectedRole, selectedLanguage, requireAuth, user]);\n  useEffect(() => {\n    const rerun = localStorage.getItem('rerunQuery');\n    if (rerun) {\n      const parsed = JSON.parse(rerun);\n      setQuery(parsed.query);\n      setTimeout(() => {\n        handleSubmit();\n        localStorage.removeItem('rerunQuery');\n      }, 500);\n    }\n  }, [handleSubmit]);\n  const handleStartRecording = () => {\n    setIsRecording(true);\n    if ('webkitSpeechRecognition' in window) {\n      const recognition = new window.webkitSpeechRecognition();\n      recognition.lang = 'en-US';\n      recognition.onresult = event => {\n        const transcript = event.results[0][0].transcript;\n        setQuery(transcript);\n        setIsRecording(false);\n      };\n      recognition.onerror = () => {\n        alert('Voice recognition error.');\n        setIsRecording(false);\n      };\n      recognition.start();\n    } else {\n      alert('Voice recognition not supported in this browser.');\n      setIsRecording(false);\n    }\n  };\n  const handleFileChange = e => {\n    const file = e.target.files[0];\n    if (file && ['image/png', 'image/jpeg', 'image/jpg'].includes(file.type)) {\n      setImageFile(file);\n      setError('');\n    } else {\n      setImageFile(null);\n      setError('Only PNG, JPG, and JPEG files are allowed.');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"medical-query-wrapper\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"What would you like to know?\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 131,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"selection-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        children: /*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Select Your Role:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 12\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n        value: selectedRole,\n        onChange: e => setSelectedRole(e.target.value),\n        className: \"role-select\",\n        children: [/*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"Common User\",\n          children: \"Common User\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"Student\",\n          children: \"Medical Student\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"Doctor\",\n          children: \"Doctor/Medical Professional\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 134,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"selection-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        children: /*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Response Language:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 12\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n        value: selectedLanguage,\n        onChange: e => setSelectedLanguage(e.target.value),\n        className: \"language-select\",\n        children: [/*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"English\",\n          children: \"English\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"Spanish\",\n          children: \"Spanish\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"French\",\n          children: \"French\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"German\",\n          children: \"German\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"Hindi\",\n          children: \"Hindi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 148,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: /*#__PURE__*/_jsxDEV(\"strong\", {\n        children: \"Input Method:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 10\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 163,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"method-toggle\",\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        className: inputMethod === 'Text' ? 'selected' : '',\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"radio\",\n          name: \"method\",\n          value: \"Text\",\n          checked: inputMethod === 'Text',\n          onChange: () => setInputMethod('Text')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 11\n        }, this), \" Text\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n        className: inputMethod === 'Voice' ? 'selected' : '',\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"radio\",\n          name: \"method\",\n          value: \"Voice\",\n          checked: inputMethod === 'Voice',\n          onChange: () => setInputMethod('Voice')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 11\n        }, this), \" Voice\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 174,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 164,\n      columnNumber: 7\n    }, this), inputMethod === 'Text' ? /*#__PURE__*/_jsxDEV(\"textarea\", {\n      className: \"text-box\",\n      placeholder: \"Enter your medical query here...\",\n      value: query,\n      onChange: e => setQuery(e.target.value)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 186,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"voice-box\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleStartRecording,\n        className: \"start-btn\",\n        children: \"\\uD83C\\uDFA4 Start Recording\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 194,\n        columnNumber: 11\n      }, this), isRecording && /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"recording-text\",\n        children: \"\\uD83C\\uDF99\\uFE0F Listening...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 27\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 193,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"upload-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"folder-icon\",\n        children: \"\\uD83D\\uDCC1\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 200,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Upload Medical Image\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 201,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Upload X-rays, lab results, medical scans or any relevant images\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"file\",\n        accept: \".png,.jpg,.jpeg\",\n        className: \"file-upload\",\n        onChange: handleFileChange\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 203,\n        columnNumber: 9\n      }, this), imageFile && /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Selected:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 26\n        }, this), \" \", imageFile.name]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 204,\n        columnNumber: 23\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 199,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"error-message\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 207,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"analyze-btn\",\n      onClick: handleSubmit,\n      disabled: loading,\n      children: loading ? '🔄 Analyzing...' : '🔍 Generate Medical Analysis'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 209,\n      columnNumber: 7\n    }, this), response && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"response-box\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Medical Analysis:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 219,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        dangerouslySetInnerHTML: {\n          __html: response.replace(/\\n/g, '<br/>')\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 220,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 218,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 130,\n    columnNumber: 5\n  }, this);\n};\n_s(MedicalQuery, \"2qZyJnx+x8NEek72n0ZZxPVFo6s=\", false, function () {\n  return [useAuth];\n});\n_c = MedicalQuery;\nexport default MedicalQuery;\nvar _c;\n$RefreshReg$(_c, \"MedicalQuery\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "medicalService", "apiUtils", "useAuth", "jsxDEV", "_jsxDEV", "Medical<PERSON><PERSON>y", "_s", "inputMethod", "setInputMethod", "isRecording", "setIsRecording", "query", "<PERSON><PERSON><PERSON><PERSON>", "imageFile", "setImageFile", "response", "setResponse", "error", "setError", "loading", "setLoading", "selectedR<PERSON>", "setSelectedRole", "selectedLanguage", "setSelectedLanguage", "requireAuth", "user", "handleSubmit", "result", "formData", "FormData", "append", "id", "submitQueryWithFile", "queryData", "user_id", "role", "language", "has_image", "submitQuery", "existingQueries", "JSON", "parse", "localStorage", "getItem", "<PERSON><PERSON><PERSON><PERSON>", "time", "Date", "toISOString", "slice", "replace", "setItem", "stringify", "err", "console", "formatErrorMessage", "rerun", "parsed", "setTimeout", "removeItem", "handleStartRecording", "window", "recognition", "webkitSpeechRecognition", "lang", "on<PERSON>ult", "event", "transcript", "results", "onerror", "alert", "start", "handleFileChange", "e", "file", "target", "files", "includes", "type", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "value", "onChange", "name", "checked", "placeholder", "onClick", "accept", "disabled", "dangerouslySetInnerHTML", "__html", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Documents/AuthPages/Evolune/medcure/src/MedicalQuery.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\r\nimport './MedicalQuery.css';\r\nimport { medicalService, apiUtils } from './services/api';\r\nimport { useAuth } from './context/AuthContext';\r\n\r\nconst MedicalQuery = () => {\r\n  const [inputMethod, setInputMethod] = useState('Text');\r\n  const [isRecording, setIsRecording] = useState(false);\r\n  const [query, setQuery] = useState('');\r\n  const [imageFile, setImageFile] = useState(null);\r\n  const [response, setResponse] = useState('');\r\n  const [error, setError] = useState('');\r\n  const [loading, setLoading] = useState(false);\r\n  const [selectedRole, setSelectedRole] = useState('Common User');\r\n  const [selectedLanguage, setSelectedLanguage] = useState('English');\r\n\r\n  const { requireAuth, user } = useAuth();\r\n\r\n  const handleSubmit = useCallback(async () => {\r\n    if (!query && !imageFile) {\r\n      setError('Please provide a question or upload a medical image.');\r\n      return;\r\n    }\r\n\r\n    // Check if user is authenticated using the auth context\r\n    if (!requireAuth()) {\r\n      return; // requireAuth will show the auth modal\r\n    }\r\n\r\n    if (!user) {\r\n      setError('User information not found. Please log in again.');\r\n      return;\r\n    }\r\n\r\n    setError('');\r\n    setResponse('');\r\n    setLoading(true);\r\n\r\n    try {\r\n      let result;\r\n\r\n      if (imageFile) {\r\n        // Submit query with file\r\n        const formData = new FormData();\r\n        formData.append('user_id', user.id);\r\n        formData.append('query', query || 'Please analyze this medical image');\r\n        formData.append('role', selectedRole);\r\n        formData.append('language', selectedLanguage);\r\n        formData.append('file', imageFile);\r\n\r\n        result = await medicalService.submitQueryWithFile(formData);\r\n      } else {\r\n        // Submit text-only query\r\n        const queryData = {\r\n          user_id: user.id,\r\n          query: query,\r\n          role: selectedRole,\r\n          language: selectedLanguage,\r\n          has_image: false\r\n        };\r\n\r\n        result = await medicalService.submitQuery(queryData);\r\n      }\r\n\r\n      setResponse(result.response);\r\n\r\n      // Save to localStorage for local history as well\r\n      const existingQueries = JSON.parse(localStorage.getItem('queryHistory') || '[]');\r\n      const newQuery = {\r\n        query: query || '[Image Only]',\r\n        role: selectedRole,\r\n        time: new Date().toISOString().slice(0, 19).replace('T', ' '),\r\n        has_image: !!imageFile\r\n      };\r\n      localStorage.setItem('queryHistory', JSON.stringify([newQuery, ...existingQueries]));\r\n\r\n    } catch (err) {\r\n      console.error('Query submission error:', err);\r\n      setError(apiUtils.formatErrorMessage(err));\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, [query, imageFile, selectedRole, selectedLanguage, requireAuth, user]);\r\n\r\n  useEffect(() => {\r\n    const rerun = localStorage.getItem('rerunQuery');\r\n    if (rerun) {\r\n      const parsed = JSON.parse(rerun);\r\n      setQuery(parsed.query);\r\n      setTimeout(() => {\r\n        handleSubmit();\r\n        localStorage.removeItem('rerunQuery');\r\n      }, 500);\r\n    }\r\n  }, [handleSubmit]);\r\n\r\n  const handleStartRecording = () => {\r\n    setIsRecording(true);\r\n    if ('webkitSpeechRecognition' in window) {\r\n      const recognition = new window.webkitSpeechRecognition();\r\n      recognition.lang = 'en-US';\r\n      recognition.onresult = (event) => {\r\n        const transcript = event.results[0][0].transcript;\r\n        setQuery(transcript);\r\n        setIsRecording(false);\r\n      };\r\n      recognition.onerror = () => {\r\n        alert('Voice recognition error.');\r\n        setIsRecording(false);\r\n      };\r\n      recognition.start();\r\n    } else {\r\n      alert('Voice recognition not supported in this browser.');\r\n      setIsRecording(false);\r\n    }\r\n  };\r\n\r\n  const handleFileChange = (e) => {\r\n    const file = e.target.files[0];\r\n    if (file && ['image/png', 'image/jpeg', 'image/jpg'].includes(file.type)) {\r\n      setImageFile(file);\r\n      setError('');\r\n    } else {\r\n      setImageFile(null);\r\n      setError('Only PNG, JPG, and JPEG files are allowed.');\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"medical-query-wrapper\">\r\n      <h2>What would you like to know?</h2>\r\n\r\n      {/* Role Selection */}\r\n      <div className=\"selection-section\">\r\n        <p><strong>Select Your Role:</strong></p>\r\n        <select\r\n          value={selectedRole}\r\n          onChange={(e) => setSelectedRole(e.target.value)}\r\n          className=\"role-select\"\r\n        >\r\n          <option value=\"Common User\">Common User</option>\r\n          <option value=\"Student\">Medical Student</option>\r\n          <option value=\"Doctor\">Doctor/Medical Professional</option>\r\n        </select>\r\n      </div>\r\n\r\n      {/* Language Selection */}\r\n      <div className=\"selection-section\">\r\n        <p><strong>Response Language:</strong></p>\r\n        <select\r\n          value={selectedLanguage}\r\n          onChange={(e) => setSelectedLanguage(e.target.value)}\r\n          className=\"language-select\"\r\n        >\r\n          <option value=\"English\">English</option>\r\n          <option value=\"Spanish\">Spanish</option>\r\n          <option value=\"French\">French</option>\r\n          <option value=\"German\">German</option>\r\n          <option value=\"Hindi\">Hindi</option>\r\n        </select>\r\n      </div>\r\n\r\n      <p><strong>Input Method:</strong></p>\r\n      <div className=\"method-toggle\">\r\n        <label className={inputMethod === 'Text' ? 'selected' : ''}>\r\n          <input\r\n            type=\"radio\"\r\n            name=\"method\"\r\n            value=\"Text\"\r\n            checked={inputMethod === 'Text'}\r\n            onChange={() => setInputMethod('Text')}\r\n          /> Text\r\n        </label>\r\n        <label className={inputMethod === 'Voice' ? 'selected' : ''}>\r\n          <input\r\n            type=\"radio\"\r\n            name=\"method\"\r\n            value=\"Voice\"\r\n            checked={inputMethod === 'Voice'}\r\n            onChange={() => setInputMethod('Voice')}\r\n          /> Voice\r\n        </label>\r\n      </div>\r\n\r\n      {inputMethod === 'Text' ? (\r\n        <textarea\r\n          className=\"text-box\"\r\n          placeholder=\"Enter your medical query here...\"\r\n          value={query}\r\n          onChange={(e) => setQuery(e.target.value)}\r\n        />\r\n      ) : (\r\n        <div className=\"voice-box\">\r\n          <button onClick={handleStartRecording} className=\"start-btn\">🎤 Start Recording</button>\r\n          {isRecording && <p className=\"recording-text\">🎙️ Listening...</p>}\r\n        </div>\r\n      )}\r\n\r\n      <div className=\"upload-section\">\r\n        <div className=\"folder-icon\">📁</div>\r\n        <h3>Upload Medical Image</h3>\r\n        <p>Upload X-rays, lab results, medical scans or any relevant images</p>\r\n        <input type=\"file\" accept=\".png,.jpg,.jpeg\" className=\"file-upload\" onChange={handleFileChange} />\r\n        {imageFile && <p><strong>Selected:</strong> {imageFile.name}</p>}\r\n      </div>\r\n\r\n      {error && <p className=\"error-message\">{error}</p>}\r\n\r\n      <button\r\n        className=\"analyze-btn\"\r\n        onClick={handleSubmit}\r\n        disabled={loading}\r\n      >\r\n        {loading ? '🔄 Analyzing...' : '🔍 Generate Medical Analysis'}\r\n      </button>\r\n\r\n      {response && (\r\n        <div className=\"response-box\">\r\n          <h3>Medical Analysis:</h3>\r\n          <div dangerouslySetInnerHTML={{ __html: response.replace(/\\n/g, '<br/>') }} />\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default MedicalQuery;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,OAAO,oBAAoB;AAC3B,SAASC,cAAc,EAAEC,QAAQ,QAAQ,gBAAgB;AACzD,SAASC,OAAO,QAAQ,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhD,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGX,QAAQ,CAAC,MAAM,CAAC;EACtD,MAAM,CAACY,WAAW,EAAEC,cAAc,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACc,KAAK,EAAEC,QAAQ,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACgB,SAAS,EAAEC,YAAY,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACkB,QAAQ,EAAEC,WAAW,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACoB,KAAK,EAAEC,QAAQ,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACsB,OAAO,EAAEC,UAAU,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACwB,YAAY,EAAEC,eAAe,CAAC,GAAGzB,QAAQ,CAAC,aAAa,CAAC;EAC/D,MAAM,CAAC0B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG3B,QAAQ,CAAC,SAAS,CAAC;EAEnE,MAAM;IAAE4B,WAAW;IAAEC;EAAK,CAAC,GAAGxB,OAAO,CAAC,CAAC;EAEvC,MAAMyB,YAAY,GAAG5B,WAAW,CAAC,YAAY;IAC3C,IAAI,CAACY,KAAK,IAAI,CAACE,SAAS,EAAE;MACxBK,QAAQ,CAAC,sDAAsD,CAAC;MAChE;IACF;;IAEA;IACA,IAAI,CAACO,WAAW,CAAC,CAAC,EAAE;MAClB,OAAO,CAAC;IACV;IAEA,IAAI,CAACC,IAAI,EAAE;MACTR,QAAQ,CAAC,kDAAkD,CAAC;MAC5D;IACF;IAEAA,QAAQ,CAAC,EAAE,CAAC;IACZF,WAAW,CAAC,EAAE,CAAC;IACfI,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF,IAAIQ,MAAM;MAEV,IAAIf,SAAS,EAAE;QACb;QACA,MAAMgB,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;QAC/BD,QAAQ,CAACE,MAAM,CAAC,SAAS,EAAEL,IAAI,CAACM,EAAE,CAAC;QACnCH,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEpB,KAAK,IAAI,mCAAmC,CAAC;QACtEkB,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEV,YAAY,CAAC;QACrCQ,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAER,gBAAgB,CAAC;QAC7CM,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAElB,SAAS,CAAC;QAElCe,MAAM,GAAG,MAAM5B,cAAc,CAACiC,mBAAmB,CAACJ,QAAQ,CAAC;MAC7D,CAAC,MAAM;QACL;QACA,MAAMK,SAAS,GAAG;UAChBC,OAAO,EAAET,IAAI,CAACM,EAAE;UAChBrB,KAAK,EAAEA,KAAK;UACZyB,IAAI,EAAEf,YAAY;UAClBgB,QAAQ,EAAEd,gBAAgB;UAC1Be,SAAS,EAAE;QACb,CAAC;QAEDV,MAAM,GAAG,MAAM5B,cAAc,CAACuC,WAAW,CAACL,SAAS,CAAC;MACtD;MAEAlB,WAAW,CAACY,MAAM,CAACb,QAAQ,CAAC;;MAE5B;MACA,MAAMyB,eAAe,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC,IAAI,IAAI,CAAC;MAChF,MAAMC,QAAQ,GAAG;QACflC,KAAK,EAAEA,KAAK,IAAI,cAAc;QAC9ByB,IAAI,EAAEf,YAAY;QAClByB,IAAI,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAACC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;QAC7DZ,SAAS,EAAE,CAAC,CAACzB;MACf,CAAC;MACD8B,YAAY,CAACQ,OAAO,CAAC,cAAc,EAAEV,IAAI,CAACW,SAAS,CAAC,CAACP,QAAQ,EAAE,GAAGL,eAAe,CAAC,CAAC,CAAC;IAEtF,CAAC,CAAC,OAAOa,GAAG,EAAE;MACZC,OAAO,CAACrC,KAAK,CAAC,yBAAyB,EAAEoC,GAAG,CAAC;MAC7CnC,QAAQ,CAACjB,QAAQ,CAACsD,kBAAkB,CAACF,GAAG,CAAC,CAAC;IAC5C,CAAC,SAAS;MACRjC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACT,KAAK,EAAEE,SAAS,EAAEQ,YAAY,EAAEE,gBAAgB,EAAEE,WAAW,EAAEC,IAAI,CAAC,CAAC;EAEzE5B,SAAS,CAAC,MAAM;IACd,MAAM0D,KAAK,GAAGb,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;IAChD,IAAIY,KAAK,EAAE;MACT,MAAMC,MAAM,GAAGhB,IAAI,CAACC,KAAK,CAACc,KAAK,CAAC;MAChC5C,QAAQ,CAAC6C,MAAM,CAAC9C,KAAK,CAAC;MACtB+C,UAAU,CAAC,MAAM;QACf/B,YAAY,CAAC,CAAC;QACdgB,YAAY,CAACgB,UAAU,CAAC,YAAY,CAAC;MACvC,CAAC,EAAE,GAAG,CAAC;IACT;EACF,CAAC,EAAE,CAAChC,YAAY,CAAC,CAAC;EAElB,MAAMiC,oBAAoB,GAAGA,CAAA,KAAM;IACjClD,cAAc,CAAC,IAAI,CAAC;IACpB,IAAI,yBAAyB,IAAImD,MAAM,EAAE;MACvC,MAAMC,WAAW,GAAG,IAAID,MAAM,CAACE,uBAAuB,CAAC,CAAC;MACxDD,WAAW,CAACE,IAAI,GAAG,OAAO;MAC1BF,WAAW,CAACG,QAAQ,GAAIC,KAAK,IAAK;QAChC,MAAMC,UAAU,GAAGD,KAAK,CAACE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAACD,UAAU;QACjDvD,QAAQ,CAACuD,UAAU,CAAC;QACpBzD,cAAc,CAAC,KAAK,CAAC;MACvB,CAAC;MACDoD,WAAW,CAACO,OAAO,GAAG,MAAM;QAC1BC,KAAK,CAAC,0BAA0B,CAAC;QACjC5D,cAAc,CAAC,KAAK,CAAC;MACvB,CAAC;MACDoD,WAAW,CAACS,KAAK,CAAC,CAAC;IACrB,CAAC,MAAM;MACLD,KAAK,CAAC,kDAAkD,CAAC;MACzD5D,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAED,MAAM8D,gBAAgB,GAAIC,CAAC,IAAK;IAC9B,MAAMC,IAAI,GAAGD,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAIF,IAAI,IAAI,CAAC,WAAW,EAAE,YAAY,EAAE,WAAW,CAAC,CAACG,QAAQ,CAACH,IAAI,CAACI,IAAI,CAAC,EAAE;MACxEhE,YAAY,CAAC4D,IAAI,CAAC;MAClBxD,QAAQ,CAAC,EAAE,CAAC;IACd,CAAC,MAAM;MACLJ,YAAY,CAAC,IAAI,CAAC;MAClBI,QAAQ,CAAC,4CAA4C,CAAC;IACxD;EACF,CAAC;EAED,oBACEd,OAAA;IAAK2E,SAAS,EAAC,uBAAuB;IAAAC,QAAA,gBACpC5E,OAAA;MAAA4E,QAAA,EAAI;IAA4B;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAGrChF,OAAA;MAAK2E,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChC5E,OAAA;QAAA4E,QAAA,eAAG5E,OAAA;UAAA4E,QAAA,EAAQ;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACzChF,OAAA;QACEiF,KAAK,EAAEhE,YAAa;QACpBiE,QAAQ,EAAGb,CAAC,IAAKnD,eAAe,CAACmD,CAAC,CAACE,MAAM,CAACU,KAAK,CAAE;QACjDN,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAEvB5E,OAAA;UAAQiF,KAAK,EAAC,aAAa;UAAAL,QAAA,EAAC;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAChDhF,OAAA;UAAQiF,KAAK,EAAC,SAAS;UAAAL,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAChDhF,OAAA;UAAQiF,KAAK,EAAC,QAAQ;UAAAL,QAAA,EAAC;QAA2B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNhF,OAAA;MAAK2E,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChC5E,OAAA;QAAA4E,QAAA,eAAG5E,OAAA;UAAA4E,QAAA,EAAQ;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAC1ChF,OAAA;QACEiF,KAAK,EAAE9D,gBAAiB;QACxB+D,QAAQ,EAAGb,CAAC,IAAKjD,mBAAmB,CAACiD,CAAC,CAACE,MAAM,CAACU,KAAK,CAAE;QACrDN,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAE3B5E,OAAA;UAAQiF,KAAK,EAAC,SAAS;UAAAL,QAAA,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACxChF,OAAA;UAAQiF,KAAK,EAAC,SAAS;UAAAL,QAAA,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACxChF,OAAA;UAAQiF,KAAK,EAAC,QAAQ;UAAAL,QAAA,EAAC;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACtChF,OAAA;UAAQiF,KAAK,EAAC,QAAQ;UAAAL,QAAA,EAAC;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACtChF,OAAA;UAAQiF,KAAK,EAAC,OAAO;UAAAL,QAAA,EAAC;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAENhF,OAAA;MAAA4E,QAAA,eAAG5E,OAAA;QAAA4E,QAAA,EAAQ;MAAa;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC,eACrChF,OAAA;MAAK2E,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5B5E,OAAA;QAAO2E,SAAS,EAAExE,WAAW,KAAK,MAAM,GAAG,UAAU,GAAG,EAAG;QAAAyE,QAAA,gBACzD5E,OAAA;UACE0E,IAAI,EAAC,OAAO;UACZS,IAAI,EAAC,QAAQ;UACbF,KAAK,EAAC,MAAM;UACZG,OAAO,EAAEjF,WAAW,KAAK,MAAO;UAChC+E,QAAQ,EAAEA,CAAA,KAAM9E,cAAc,CAAC,MAAM;QAAE;UAAAyE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CAAC,SACJ;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACRhF,OAAA;QAAO2E,SAAS,EAAExE,WAAW,KAAK,OAAO,GAAG,UAAU,GAAG,EAAG;QAAAyE,QAAA,gBAC1D5E,OAAA;UACE0E,IAAI,EAAC,OAAO;UACZS,IAAI,EAAC,QAAQ;UACbF,KAAK,EAAC,OAAO;UACbG,OAAO,EAAEjF,WAAW,KAAK,OAAQ;UACjC+E,QAAQ,EAAEA,CAAA,KAAM9E,cAAc,CAAC,OAAO;QAAE;UAAAyE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC,UACJ;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,EAEL7E,WAAW,KAAK,MAAM,gBACrBH,OAAA;MACE2E,SAAS,EAAC,UAAU;MACpBU,WAAW,EAAC,kCAAkC;MAC9CJ,KAAK,EAAE1E,KAAM;MACb2E,QAAQ,EAAGb,CAAC,IAAK7D,QAAQ,CAAC6D,CAAC,CAACE,MAAM,CAACU,KAAK;IAAE;MAAAJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3C,CAAC,gBAEFhF,OAAA;MAAK2E,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxB5E,OAAA;QAAQsF,OAAO,EAAE9B,oBAAqB;QAACmB,SAAS,EAAC,WAAW;QAAAC,QAAA,EAAC;MAAkB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,EACvF3E,WAAW,iBAAIL,OAAA;QAAG2E,SAAS,EAAC,gBAAgB;QAAAC,QAAA,EAAC;MAAgB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/D,CACN,eAEDhF,OAAA;MAAK2E,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7B5E,OAAA;QAAK2E,SAAS,EAAC,aAAa;QAAAC,QAAA,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACrChF,OAAA;QAAA4E,QAAA,EAAI;MAAoB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC7BhF,OAAA;QAAA4E,QAAA,EAAG;MAAgE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACvEhF,OAAA;QAAO0E,IAAI,EAAC,MAAM;QAACa,MAAM,EAAC,iBAAiB;QAACZ,SAAS,EAAC,aAAa;QAACO,QAAQ,EAAEd;MAAiB;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EACjGvE,SAAS,iBAAIT,OAAA;QAAA4E,QAAA,gBAAG5E,OAAA;UAAA4E,QAAA,EAAQ;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,KAAC,EAACvE,SAAS,CAAC0E,IAAI;MAAA;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7D,CAAC,EAELnE,KAAK,iBAAIb,OAAA;MAAG2E,SAAS,EAAC,eAAe;MAAAC,QAAA,EAAE/D;IAAK;MAAAgE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAElDhF,OAAA;MACE2E,SAAS,EAAC,aAAa;MACvBW,OAAO,EAAE/D,YAAa;MACtBiE,QAAQ,EAAEzE,OAAQ;MAAA6D,QAAA,EAEjB7D,OAAO,GAAG,iBAAiB,GAAG;IAA8B;MAAA8D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvD,CAAC,EAERrE,QAAQ,iBACPX,OAAA;MAAK2E,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC3B5E,OAAA;QAAA4E,QAAA,EAAI;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC1BhF,OAAA;QAAKyF,uBAAuB,EAAE;UAAEC,MAAM,EAAE/E,QAAQ,CAACmC,OAAO,CAAC,KAAK,EAAE,OAAO;QAAE;MAAE;QAAA+B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3E,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC9E,EAAA,CA3NID,YAAY;EAAA,QAWcH,OAAO;AAAA;AAAA6F,EAAA,GAXjC1F,YAAY;AA6NlB,eAAeA,YAAY;AAAC,IAAA0F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}