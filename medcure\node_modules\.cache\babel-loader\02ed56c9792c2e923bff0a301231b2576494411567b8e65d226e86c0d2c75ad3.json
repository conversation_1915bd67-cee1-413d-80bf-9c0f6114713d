{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport TableContext from \"./TableContext.js\";\nimport { styled } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport { getTableUtilityClass } from \"./tableClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    stickyHeader\n  } = ownerState;\n  const slots = {\n    root: ['root', stickyHeader && 'stickyHeader']\n  };\n  return composeClasses(slots, getTableUtilityClass, classes);\n};\nconst TableRoot = styled('table', {\n  name: 'MuiTable',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, ownerState.stickyHeader && styles.stickyHeader];\n  }\n})(memoTheme(({\n  theme\n}) => ({\n  display: 'table',\n  width: '100%',\n  borderCollapse: 'collapse',\n  borderSpacing: 0,\n  '& caption': {\n    ...theme.typography.body2,\n    padding: theme.spacing(2),\n    color: (theme.vars || theme).palette.text.secondary,\n    textAlign: 'left',\n    captionSide: 'bottom'\n  },\n  variants: [{\n    props: ({\n      ownerState\n    }) => ownerState.stickyHeader,\n    style: {\n      borderCollapse: 'separate'\n    }\n  }]\n})));\nconst defaultComponent = 'table';\nconst Table = /*#__PURE__*/React.forwardRef(function Table(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiTable'\n  });\n  const {\n    className,\n    component = defaultComponent,\n    padding = 'normal',\n    size = 'medium',\n    stickyHeader = false,\n    ...other\n  } = props;\n  const ownerState = {\n    ...props,\n    component,\n    padding,\n    size,\n    stickyHeader\n  };\n  const classes = useUtilityClasses(ownerState);\n  const table = React.useMemo(() => ({\n    padding,\n    size,\n    stickyHeader\n  }), [padding, size, stickyHeader]);\n  return /*#__PURE__*/_jsx(TableContext.Provider, {\n    value: table,\n    children: /*#__PURE__*/_jsx(TableRoot, {\n      as: component,\n      role: component === defaultComponent ? null : 'table',\n      ref: ref,\n      className: clsx(classes.root, className),\n      ownerState: ownerState,\n      ...other\n    })\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Table.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the table, normally `TableHead` and `TableBody`.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * Allows TableCells to inherit padding of the Table.\n   * @default 'normal'\n   */\n  padding: PropTypes.oneOf(['checkbox', 'none', 'normal']),\n  /**\n   * Allows TableCells to inherit size of the Table.\n   * @default 'medium'\n   */\n  size: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['medium', 'small']), PropTypes.string]),\n  /**\n   * Set the header sticky.\n   * @default false\n   */\n  stickyHeader: PropTypes.bool,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default Table;", "map": {"version": 3, "names": ["React", "PropTypes", "clsx", "composeClasses", "TableContext", "styled", "memoTheme", "useDefaultProps", "getTableUtilityClass", "jsx", "_jsx", "useUtilityClasses", "ownerState", "classes", "<PERSON><PERSON><PERSON><PERSON>", "slots", "root", "TableRoot", "name", "slot", "overridesResolver", "props", "styles", "theme", "display", "width", "borderCollapse", "borderSpacing", "typography", "body2", "padding", "spacing", "color", "vars", "palette", "text", "secondary", "textAlign", "captionSide", "variants", "style", "defaultComponent", "Table", "forwardRef", "inProps", "ref", "className", "component", "size", "other", "table", "useMemo", "Provider", "value", "children", "as", "role", "process", "env", "NODE_ENV", "propTypes", "node", "object", "string", "elementType", "oneOf", "oneOfType", "bool", "sx", "arrayOf", "func"], "sources": ["C:/Users/<USER>/OneDrive/Documents/AuthPages/Evolune/medcure/node_modules/@mui/material/esm/Table/Table.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport TableContext from \"./TableContext.js\";\nimport { styled } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport { getTableUtilityClass } from \"./tableClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    stickyHeader\n  } = ownerState;\n  const slots = {\n    root: ['root', stickyHeader && 'stickyHeader']\n  };\n  return composeClasses(slots, getTableUtilityClass, classes);\n};\nconst TableRoot = styled('table', {\n  name: 'MuiTable',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, ownerState.stickyHeader && styles.stickyHeader];\n  }\n})(memoTheme(({\n  theme\n}) => ({\n  display: 'table',\n  width: '100%',\n  borderCollapse: 'collapse',\n  borderSpacing: 0,\n  '& caption': {\n    ...theme.typography.body2,\n    padding: theme.spacing(2),\n    color: (theme.vars || theme).palette.text.secondary,\n    textAlign: 'left',\n    captionSide: 'bottom'\n  },\n  variants: [{\n    props: ({\n      ownerState\n    }) => ownerState.stickyHeader,\n    style: {\n      borderCollapse: 'separate'\n    }\n  }]\n})));\nconst defaultComponent = 'table';\nconst Table = /*#__PURE__*/React.forwardRef(function Table(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiTable'\n  });\n  const {\n    className,\n    component = defaultComponent,\n    padding = 'normal',\n    size = 'medium',\n    stickyHeader = false,\n    ...other\n  } = props;\n  const ownerState = {\n    ...props,\n    component,\n    padding,\n    size,\n    stickyHeader\n  };\n  const classes = useUtilityClasses(ownerState);\n  const table = React.useMemo(() => ({\n    padding,\n    size,\n    stickyHeader\n  }), [padding, size, stickyHeader]);\n  return /*#__PURE__*/_jsx(TableContext.Provider, {\n    value: table,\n    children: /*#__PURE__*/_jsx(TableRoot, {\n      as: component,\n      role: component === defaultComponent ? null : 'table',\n      ref: ref,\n      className: clsx(classes.root, className),\n      ownerState: ownerState,\n      ...other\n    })\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Table.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the table, normally `TableHead` and `TableBody`.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * Allows TableCells to inherit padding of the Table.\n   * @default 'normal'\n   */\n  padding: PropTypes.oneOf(['checkbox', 'none', 'normal']),\n  /**\n   * Allows TableCells to inherit size of the Table.\n   * @default 'medium'\n   */\n  size: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['medium', 'small']), PropTypes.string]),\n  /**\n   * Set the header sticky.\n   * @default false\n   */\n  stickyHeader: PropTypes.bool,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default Table;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,YAAY,MAAM,mBAAmB;AAC5C,SAASC,MAAM,QAAQ,yBAAyB;AAChD,OAAOC,SAAS,MAAM,uBAAuB;AAC7C,SAASC,eAAe,QAAQ,kCAAkC;AAClE,SAASC,oBAAoB,QAAQ,mBAAmB;AACxD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,OAAO;IACPC;EACF,CAAC,GAAGF,UAAU;EACd,MAAMG,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAEF,YAAY,IAAI,cAAc;EAC/C,CAAC;EACD,OAAOX,cAAc,CAACY,KAAK,EAAEP,oBAAoB,EAAEK,OAAO,CAAC;AAC7D,CAAC;AACD,MAAMI,SAAS,GAAGZ,MAAM,CAAC,OAAO,EAAE;EAChCa,IAAI,EAAE,UAAU;EAChBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJV;IACF,CAAC,GAAGS,KAAK;IACT,OAAO,CAACC,MAAM,CAACN,IAAI,EAAEJ,UAAU,CAACE,YAAY,IAAIQ,MAAM,CAACR,YAAY,CAAC;EACtE;AACF,CAAC,CAAC,CAACR,SAAS,CAAC,CAAC;EACZiB;AACF,CAAC,MAAM;EACLC,OAAO,EAAE,OAAO;EAChBC,KAAK,EAAE,MAAM;EACbC,cAAc,EAAE,UAAU;EAC1BC,aAAa,EAAE,CAAC;EAChB,WAAW,EAAE;IACX,GAAGJ,KAAK,CAACK,UAAU,CAACC,KAAK;IACzBC,OAAO,EAAEP,KAAK,CAACQ,OAAO,CAAC,CAAC,CAAC;IACzBC,KAAK,EAAE,CAACT,KAAK,CAACU,IAAI,IAAIV,KAAK,EAAEW,OAAO,CAACC,IAAI,CAACC,SAAS;IACnDC,SAAS,EAAE,MAAM;IACjBC,WAAW,EAAE;EACf,CAAC;EACDC,QAAQ,EAAE,CAAC;IACTlB,KAAK,EAAEA,CAAC;MACNT;IACF,CAAC,KAAKA,UAAU,CAACE,YAAY;IAC7B0B,KAAK,EAAE;MACLd,cAAc,EAAE;IAClB;EACF,CAAC;AACH,CAAC,CAAC,CAAC,CAAC;AACJ,MAAMe,gBAAgB,GAAG,OAAO;AAChC,MAAMC,KAAK,GAAG,aAAa1C,KAAK,CAAC2C,UAAU,CAAC,SAASD,KAAKA,CAACE,OAAO,EAAEC,GAAG,EAAE;EACvE,MAAMxB,KAAK,GAAGd,eAAe,CAAC;IAC5Bc,KAAK,EAAEuB,OAAO;IACd1B,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;IACJ4B,SAAS;IACTC,SAAS,GAAGN,gBAAgB;IAC5BX,OAAO,GAAG,QAAQ;IAClBkB,IAAI,GAAG,QAAQ;IACflC,YAAY,GAAG,KAAK;IACpB,GAAGmC;EACL,CAAC,GAAG5B,KAAK;EACT,MAAMT,UAAU,GAAG;IACjB,GAAGS,KAAK;IACR0B,SAAS;IACTjB,OAAO;IACPkB,IAAI;IACJlC;EACF,CAAC;EACD,MAAMD,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAMsC,KAAK,GAAGlD,KAAK,CAACmD,OAAO,CAAC,OAAO;IACjCrB,OAAO;IACPkB,IAAI;IACJlC;EACF,CAAC,CAAC,EAAE,CAACgB,OAAO,EAAEkB,IAAI,EAAElC,YAAY,CAAC,CAAC;EAClC,OAAO,aAAaJ,IAAI,CAACN,YAAY,CAACgD,QAAQ,EAAE;IAC9CC,KAAK,EAAEH,KAAK;IACZI,QAAQ,EAAE,aAAa5C,IAAI,CAACO,SAAS,EAAE;MACrCsC,EAAE,EAAER,SAAS;MACbS,IAAI,EAAET,SAAS,KAAKN,gBAAgB,GAAG,IAAI,GAAG,OAAO;MACrDI,GAAG,EAAEA,GAAG;MACRC,SAAS,EAAE5C,IAAI,CAACW,OAAO,CAACG,IAAI,EAAE8B,SAAS,CAAC;MACxClC,UAAU,EAAEA,UAAU;MACtB,GAAGqC;IACL,CAAC;EACH,CAAC,CAAC;AACJ,CAAC,CAAC;AACFQ,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGjB,KAAK,CAACkB,SAAS,CAAC,yBAAyB;EAC/E;EACA;EACA;EACA;EACA;AACF;AACA;EACEN,QAAQ,EAAErD,SAAS,CAAC4D,IAAI;EACxB;AACF;AACA;EACEhD,OAAO,EAAEZ,SAAS,CAAC6D,MAAM;EACzB;AACF;AACA;EACEhB,SAAS,EAAE7C,SAAS,CAAC8D,MAAM;EAC3B;AACF;AACA;AACA;EACEhB,SAAS,EAAE9C,SAAS,CAAC+D,WAAW;EAChC;AACF;AACA;AACA;EACElC,OAAO,EAAE7B,SAAS,CAACgE,KAAK,CAAC,CAAC,UAAU,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;EACxD;AACF;AACA;AACA;EACEjB,IAAI,EAAE/C,SAAS,CAAC,sCAAsCiE,SAAS,CAAC,CAACjE,SAAS,CAACgE,KAAK,CAAC,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,EAAEhE,SAAS,CAAC8D,MAAM,CAAC,CAAC;EACzH;AACF;AACA;AACA;EACEjD,YAAY,EAAEb,SAAS,CAACkE,IAAI;EAC5B;AACF;AACA;EACEC,EAAE,EAAEnE,SAAS,CAACiE,SAAS,CAAC,CAACjE,SAAS,CAACoE,OAAO,CAACpE,SAAS,CAACiE,SAAS,CAAC,CAACjE,SAAS,CAACqE,IAAI,EAAErE,SAAS,CAAC6D,MAAM,EAAE7D,SAAS,CAACkE,IAAI,CAAC,CAAC,CAAC,EAAElE,SAAS,CAACqE,IAAI,EAAErE,SAAS,CAAC6D,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACV,eAAepB,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}