{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport useEnhancedEffect from '@mui/utils/useEnhancedEffect';\nimport { getThemeProps } from \"../useThemeProps/index.js\";\nimport useTheme from \"../useThemeWithoutDefault/index.js\";\n// TODO React 17: Remove `useMediaQueryOld` once React 17 support is removed\nfunction useMediaQueryOld(query, defaultMatches, matchMedia, ssrMatchMedia, noSsr) {\n  const [match, setMatch] = React.useState(() => {\n    if (noSsr && matchMedia) {\n      return matchMedia(query).matches;\n    }\n    if (ssrMatchMedia) {\n      return ssrMatchMedia(query).matches;\n    }\n\n    // Once the component is mounted, we rely on the\n    // event listeners to return the correct matches value.\n    return defaultMatches;\n  });\n  useEnhancedEffect(() => {\n    if (!matchMedia) {\n      return undefined;\n    }\n    const queryList = matchMedia(query);\n    const updateMatch = () => {\n      setMatch(queryList.matches);\n    };\n    updateMatch();\n    queryList.addEventListener('change', updateMatch);\n    return () => {\n      queryList.removeEventListener('change', updateMatch);\n    };\n  }, [query, matchMedia]);\n  return match;\n}\n\n// See https://github.com/mui/material-ui/issues/41190#issuecomment-2040873379 for why\nconst safeReact = {\n  ...React\n};\nconst maybeReactUseSyncExternalStore = safeReact.useSyncExternalStore;\nfunction useMediaQueryNew(query, defaultMatches, matchMedia, ssrMatchMedia, noSsr) {\n  const getDefaultSnapshot = React.useCallback(() => defaultMatches, [defaultMatches]);\n  const getServerSnapshot = React.useMemo(() => {\n    if (noSsr && matchMedia) {\n      return () => matchMedia(query).matches;\n    }\n    if (ssrMatchMedia !== null) {\n      const {\n        matches\n      } = ssrMatchMedia(query);\n      return () => matches;\n    }\n    return getDefaultSnapshot;\n  }, [getDefaultSnapshot, query, ssrMatchMedia, noSsr, matchMedia]);\n  const [getSnapshot, subscribe] = React.useMemo(() => {\n    if (matchMedia === null) {\n      return [getDefaultSnapshot, () => () => {}];\n    }\n    const mediaQueryList = matchMedia(query);\n    return [() => mediaQueryList.matches, notify => {\n      mediaQueryList.addEventListener('change', notify);\n      return () => {\n        mediaQueryList.removeEventListener('change', notify);\n      };\n    }];\n  }, [getDefaultSnapshot, matchMedia, query]);\n  const match = maybeReactUseSyncExternalStore(subscribe, getSnapshot, getServerSnapshot);\n  return match;\n}\n\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport function unstable_createUseMediaQuery(params = {}) {\n  const {\n    themeId\n  } = params;\n  return function useMediaQuery(queryInput, options = {}) {\n    let theme = useTheme();\n    if (theme && themeId) {\n      theme = theme[themeId] || theme;\n    }\n    // Wait for jsdom to support the match media feature.\n    // All the browsers MUI support have this built-in.\n    // This defensive check is here for simplicity.\n    // Most of the time, the match media logic isn't central to people tests.\n    const supportMatchMedia = typeof window !== 'undefined' && typeof window.matchMedia !== 'undefined';\n    const {\n      defaultMatches = false,\n      matchMedia = supportMatchMedia ? window.matchMedia : null,\n      ssrMatchMedia = null,\n      noSsr = false\n    } = getThemeProps({\n      name: 'MuiUseMediaQuery',\n      props: options,\n      theme\n    });\n    if (process.env.NODE_ENV !== 'production') {\n      if (typeof queryInput === 'function' && theme === null) {\n        console.error(['MUI: The `query` argument provided is invalid.', 'You are providing a function without a theme in the context.', 'One of the parent elements needs to use a ThemeProvider.'].join('\\n'));\n      }\n    }\n    let query = typeof queryInput === 'function' ? queryInput(theme) : queryInput;\n    query = query.replace(/^@media( ?)/m, '');\n    if (query.includes('print')) {\n      console.warn([`MUI: You have provided a \\`print\\` query to the \\`useMediaQuery\\` hook.`, 'Using the print media query to modify print styles can lead to unexpected results.', 'Consider using the `displayPrint` field in the `sx` prop instead.', 'More information about `displayPrint` on our docs: https://mui.com/system/display/#display-in-print.'].join('\\n'));\n    }\n    const useMediaQueryImplementation = maybeReactUseSyncExternalStore !== undefined ? useMediaQueryNew : useMediaQueryOld;\n    const match = useMediaQueryImplementation(query, defaultMatches, matchMedia, ssrMatchMedia, noSsr);\n    if (process.env.NODE_ENV !== 'production') {\n      // eslint-disable-next-line react-hooks/rules-of-hooks\n      React.useDebugValue({\n        query,\n        match\n      });\n    }\n    return match;\n  };\n}\nconst useMediaQuery = unstable_createUseMediaQuery();\nexport default useMediaQuery;", "map": {"version": 3, "names": ["React", "useEnhancedEffect", "getThemeProps", "useTheme", "useMediaQueryOld", "query", "defaultMatches", "matchMedia", "ssrMatchMedia", "noSsr", "match", "setMatch", "useState", "matches", "undefined", "queryList", "updateMatch", "addEventListener", "removeEventListener", "safeReact", "maybeReactUseSyncExternalStore", "useSyncExternalStore", "useMediaQueryNew", "getDefaultSnapshot", "useCallback", "getServerSnapshot", "useMemo", "getSnapshot", "subscribe", "mediaQueryList", "notify", "unstable_createUseMediaQuery", "params", "themeId", "useMediaQuery", "queryInput", "options", "theme", "supportMatchMedia", "window", "name", "props", "process", "env", "NODE_ENV", "console", "error", "join", "replace", "includes", "warn", "useMediaQueryImplementation", "useDebugValue"], "sources": ["C:/Users/<USER>/OneDrive/Documents/AuthPages/Evolune/medcure/node_modules/@mui/system/esm/useMediaQuery/useMediaQuery.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport useEnhancedEffect from '@mui/utils/useEnhancedEffect';\nimport { getThemeProps } from \"../useThemeProps/index.js\";\nimport useTheme from \"../useThemeWithoutDefault/index.js\";\n// TODO React 17: Remove `useMediaQueryOld` once React 17 support is removed\nfunction useMediaQueryOld(query, defaultMatches, matchMedia, ssrMatchMedia, noSsr) {\n  const [match, setMatch] = React.useState(() => {\n    if (noSsr && matchMedia) {\n      return matchMedia(query).matches;\n    }\n    if (ssrMatchMedia) {\n      return ssrMatchMedia(query).matches;\n    }\n\n    // Once the component is mounted, we rely on the\n    // event listeners to return the correct matches value.\n    return defaultMatches;\n  });\n  useEnhancedEffect(() => {\n    if (!matchMedia) {\n      return undefined;\n    }\n    const queryList = matchMedia(query);\n    const updateMatch = () => {\n      setMatch(queryList.matches);\n    };\n    updateMatch();\n    queryList.addEventListener('change', updateMatch);\n    return () => {\n      queryList.removeEventListener('change', updateMatch);\n    };\n  }, [query, matchMedia]);\n  return match;\n}\n\n// See https://github.com/mui/material-ui/issues/41190#issuecomment-2040873379 for why\nconst safeReact = {\n  ...React\n};\nconst maybeReactUseSyncExternalStore = safeReact.useSyncExternalStore;\nfunction useMediaQueryNew(query, defaultMatches, matchMedia, ssrMatchMedia, noSsr) {\n  const getDefaultSnapshot = React.useCallback(() => defaultMatches, [defaultMatches]);\n  const getServerSnapshot = React.useMemo(() => {\n    if (noSsr && matchMedia) {\n      return () => matchMedia(query).matches;\n    }\n    if (ssrMatchMedia !== null) {\n      const {\n        matches\n      } = ssrMatchMedia(query);\n      return () => matches;\n    }\n    return getDefaultSnapshot;\n  }, [getDefaultSnapshot, query, ssrMatchMedia, noSsr, matchMedia]);\n  const [getSnapshot, subscribe] = React.useMemo(() => {\n    if (matchMedia === null) {\n      return [getDefaultSnapshot, () => () => {}];\n    }\n    const mediaQueryList = matchMedia(query);\n    return [() => mediaQueryList.matches, notify => {\n      mediaQueryList.addEventListener('change', notify);\n      return () => {\n        mediaQueryList.removeEventListener('change', notify);\n      };\n    }];\n  }, [getDefaultSnapshot, matchMedia, query]);\n  const match = maybeReactUseSyncExternalStore(subscribe, getSnapshot, getServerSnapshot);\n  return match;\n}\n\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport function unstable_createUseMediaQuery(params = {}) {\n  const {\n    themeId\n  } = params;\n  return function useMediaQuery(queryInput, options = {}) {\n    let theme = useTheme();\n    if (theme && themeId) {\n      theme = theme[themeId] || theme;\n    }\n    // Wait for jsdom to support the match media feature.\n    // All the browsers MUI support have this built-in.\n    // This defensive check is here for simplicity.\n    // Most of the time, the match media logic isn't central to people tests.\n    const supportMatchMedia = typeof window !== 'undefined' && typeof window.matchMedia !== 'undefined';\n    const {\n      defaultMatches = false,\n      matchMedia = supportMatchMedia ? window.matchMedia : null,\n      ssrMatchMedia = null,\n      noSsr = false\n    } = getThemeProps({\n      name: 'MuiUseMediaQuery',\n      props: options,\n      theme\n    });\n    if (process.env.NODE_ENV !== 'production') {\n      if (typeof queryInput === 'function' && theme === null) {\n        console.error(['MUI: The `query` argument provided is invalid.', 'You are providing a function without a theme in the context.', 'One of the parent elements needs to use a ThemeProvider.'].join('\\n'));\n      }\n    }\n    let query = typeof queryInput === 'function' ? queryInput(theme) : queryInput;\n    query = query.replace(/^@media( ?)/m, '');\n    if (query.includes('print')) {\n      console.warn([`MUI: You have provided a \\`print\\` query to the \\`useMediaQuery\\` hook.`, 'Using the print media query to modify print styles can lead to unexpected results.', 'Consider using the `displayPrint` field in the `sx` prop instead.', 'More information about `displayPrint` on our docs: https://mui.com/system/display/#display-in-print.'].join('\\n'));\n    }\n    const useMediaQueryImplementation = maybeReactUseSyncExternalStore !== undefined ? useMediaQueryNew : useMediaQueryOld;\n    const match = useMediaQueryImplementation(query, defaultMatches, matchMedia, ssrMatchMedia, noSsr);\n    if (process.env.NODE_ENV !== 'production') {\n      // eslint-disable-next-line react-hooks/rules-of-hooks\n      React.useDebugValue({\n        query,\n        match\n      });\n    }\n    return match;\n  };\n}\nconst useMediaQuery = unstable_createUseMediaQuery();\nexport default useMediaQuery;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,iBAAiB,MAAM,8BAA8B;AAC5D,SAASC,aAAa,QAAQ,2BAA2B;AACzD,OAAOC,QAAQ,MAAM,oCAAoC;AACzD;AACA,SAASC,gBAAgBA,CAACC,KAAK,EAAEC,cAAc,EAAEC,UAAU,EAAEC,aAAa,EAAEC,KAAK,EAAE;EACjF,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGX,KAAK,CAACY,QAAQ,CAAC,MAAM;IAC7C,IAAIH,KAAK,IAAIF,UAAU,EAAE;MACvB,OAAOA,UAAU,CAACF,KAAK,CAAC,CAACQ,OAAO;IAClC;IACA,IAAIL,aAAa,EAAE;MACjB,OAAOA,aAAa,CAACH,KAAK,CAAC,CAACQ,OAAO;IACrC;;IAEA;IACA;IACA,OAAOP,cAAc;EACvB,CAAC,CAAC;EACFL,iBAAiB,CAAC,MAAM;IACtB,IAAI,CAACM,UAAU,EAAE;MACf,OAAOO,SAAS;IAClB;IACA,MAAMC,SAAS,GAAGR,UAAU,CAACF,KAAK,CAAC;IACnC,MAAMW,WAAW,GAAGA,CAAA,KAAM;MACxBL,QAAQ,CAACI,SAAS,CAACF,OAAO,CAAC;IAC7B,CAAC;IACDG,WAAW,CAAC,CAAC;IACbD,SAAS,CAACE,gBAAgB,CAAC,QAAQ,EAAED,WAAW,CAAC;IACjD,OAAO,MAAM;MACXD,SAAS,CAACG,mBAAmB,CAAC,QAAQ,EAAEF,WAAW,CAAC;IACtD,CAAC;EACH,CAAC,EAAE,CAACX,KAAK,EAAEE,UAAU,CAAC,CAAC;EACvB,OAAOG,KAAK;AACd;;AAEA;AACA,MAAMS,SAAS,GAAG;EAChB,GAAGnB;AACL,CAAC;AACD,MAAMoB,8BAA8B,GAAGD,SAAS,CAACE,oBAAoB;AACrE,SAASC,gBAAgBA,CAACjB,KAAK,EAAEC,cAAc,EAAEC,UAAU,EAAEC,aAAa,EAAEC,KAAK,EAAE;EACjF,MAAMc,kBAAkB,GAAGvB,KAAK,CAACwB,WAAW,CAAC,MAAMlB,cAAc,EAAE,CAACA,cAAc,CAAC,CAAC;EACpF,MAAMmB,iBAAiB,GAAGzB,KAAK,CAAC0B,OAAO,CAAC,MAAM;IAC5C,IAAIjB,KAAK,IAAIF,UAAU,EAAE;MACvB,OAAO,MAAMA,UAAU,CAACF,KAAK,CAAC,CAACQ,OAAO;IACxC;IACA,IAAIL,aAAa,KAAK,IAAI,EAAE;MAC1B,MAAM;QACJK;MACF,CAAC,GAAGL,aAAa,CAACH,KAAK,CAAC;MACxB,OAAO,MAAMQ,OAAO;IACtB;IACA,OAAOU,kBAAkB;EAC3B,CAAC,EAAE,CAACA,kBAAkB,EAAElB,KAAK,EAAEG,aAAa,EAAEC,KAAK,EAAEF,UAAU,CAAC,CAAC;EACjE,MAAM,CAACoB,WAAW,EAAEC,SAAS,CAAC,GAAG5B,KAAK,CAAC0B,OAAO,CAAC,MAAM;IACnD,IAAInB,UAAU,KAAK,IAAI,EAAE;MACvB,OAAO,CAACgB,kBAAkB,EAAE,MAAM,MAAM,CAAC,CAAC,CAAC;IAC7C;IACA,MAAMM,cAAc,GAAGtB,UAAU,CAACF,KAAK,CAAC;IACxC,OAAO,CAAC,MAAMwB,cAAc,CAAChB,OAAO,EAAEiB,MAAM,IAAI;MAC9CD,cAAc,CAACZ,gBAAgB,CAAC,QAAQ,EAAEa,MAAM,CAAC;MACjD,OAAO,MAAM;QACXD,cAAc,CAACX,mBAAmB,CAAC,QAAQ,EAAEY,MAAM,CAAC;MACtD,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,EAAE,CAACP,kBAAkB,EAAEhB,UAAU,EAAEF,KAAK,CAAC,CAAC;EAC3C,MAAMK,KAAK,GAAGU,8BAA8B,CAACQ,SAAS,EAAED,WAAW,EAAEF,iBAAiB,CAAC;EACvF,OAAOf,KAAK;AACd;;AAEA;AACA,OAAO,SAASqB,4BAA4BA,CAACC,MAAM,GAAG,CAAC,CAAC,EAAE;EACxD,MAAM;IACJC;EACF,CAAC,GAAGD,MAAM;EACV,OAAO,SAASE,aAAaA,CAACC,UAAU,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IACtD,IAAIC,KAAK,GAAGlC,QAAQ,CAAC,CAAC;IACtB,IAAIkC,KAAK,IAAIJ,OAAO,EAAE;MACpBI,KAAK,GAAGA,KAAK,CAACJ,OAAO,CAAC,IAAII,KAAK;IACjC;IACA;IACA;IACA;IACA;IACA,MAAMC,iBAAiB,GAAG,OAAOC,MAAM,KAAK,WAAW,IAAI,OAAOA,MAAM,CAAChC,UAAU,KAAK,WAAW;IACnG,MAAM;MACJD,cAAc,GAAG,KAAK;MACtBC,UAAU,GAAG+B,iBAAiB,GAAGC,MAAM,CAAChC,UAAU,GAAG,IAAI;MACzDC,aAAa,GAAG,IAAI;MACpBC,KAAK,GAAG;IACV,CAAC,GAAGP,aAAa,CAAC;MAChBsC,IAAI,EAAE,kBAAkB;MACxBC,KAAK,EAAEL,OAAO;MACdC;IACF,CAAC,CAAC;IACF,IAAIK,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAI,OAAOT,UAAU,KAAK,UAAU,IAAIE,KAAK,KAAK,IAAI,EAAE;QACtDQ,OAAO,CAACC,KAAK,CAAC,CAAC,gDAAgD,EAAE,8DAA8D,EAAE,0DAA0D,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;MAC1M;IACF;IACA,IAAI1C,KAAK,GAAG,OAAO8B,UAAU,KAAK,UAAU,GAAGA,UAAU,CAACE,KAAK,CAAC,GAAGF,UAAU;IAC7E9B,KAAK,GAAGA,KAAK,CAAC2C,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC;IACzC,IAAI3C,KAAK,CAAC4C,QAAQ,CAAC,OAAO,CAAC,EAAE;MAC3BJ,OAAO,CAACK,IAAI,CAAC,CAAC,yEAAyE,EAAE,oFAAoF,EAAE,mEAAmE,EAAE,sGAAsG,CAAC,CAACH,IAAI,CAAC,IAAI,CAAC,CAAC;IACzW;IACA,MAAMI,2BAA2B,GAAG/B,8BAA8B,KAAKN,SAAS,GAAGQ,gBAAgB,GAAGlB,gBAAgB;IACtH,MAAMM,KAAK,GAAGyC,2BAA2B,CAAC9C,KAAK,EAAEC,cAAc,EAAEC,UAAU,EAAEC,aAAa,EAAEC,KAAK,CAAC;IAClG,IAAIiC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC;MACA5C,KAAK,CAACoD,aAAa,CAAC;QAClB/C,KAAK;QACLK;MACF,CAAC,CAAC;IACJ;IACA,OAAOA,KAAK;EACd,CAAC;AACH;AACA,MAAMwB,aAAa,GAAGH,4BAA4B,CAAC,CAAC;AACpD,eAAeG,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}