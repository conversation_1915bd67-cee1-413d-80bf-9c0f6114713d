{"ast": null, "code": "import createPalette from \"./createPalette.js\";\nimport createThemeWithVars from \"./createThemeWithVars.js\";\nimport createThemeNoVars from \"./createThemeNoVars.js\";\n// eslint-disable-next-line consistent-return\nfunction attachColorScheme(theme, scheme, colorScheme) {\n  if (!theme.colorSchemes) {\n    return undefined;\n  }\n  if (colorScheme) {\n    theme.colorSchemes[scheme] = {\n      ...(colorScheme !== true && colorScheme),\n      palette: createPalette({\n        ...(colorScheme === true ? {} : colorScheme.palette),\n        mode: scheme\n      }) // cast type to skip module augmentation test\n    };\n  }\n}\n\n/**\n * Generate a theme base on the options received.\n * @param options Takes an incomplete theme object and adds the missing parts.\n * @param args Deep merge the arguments with the about to be returned theme.\n * @returns A complete, ready-to-use theme object.\n */\nexport default function createTheme(options = {},\n// cast type to skip module augmentation test\n...args) {\n  const {\n    palette,\n    cssVariables = false,\n    colorSchemes: initialColorSchemes = !palette ? {\n      light: true\n    } : undefined,\n    defaultColorScheme: initialDefaultColorScheme = palette?.mode,\n    ...rest\n  } = options;\n  const defaultColorSchemeInput = initialDefaultColorScheme || 'light';\n  const defaultScheme = initialColorSchemes?.[defaultColorSchemeInput];\n  const colorSchemesInput = {\n    ...initialColorSchemes,\n    ...(palette ? {\n      [defaultColorSchemeInput]: {\n        ...(typeof defaultScheme !== 'boolean' && defaultScheme),\n        palette\n      }\n    } : undefined)\n  };\n  if (cssVariables === false) {\n    if (!('colorSchemes' in options)) {\n      // Behaves exactly as v5\n      return createThemeNoVars(options, ...args);\n    }\n    let paletteOptions = palette;\n    if (!('palette' in options)) {\n      if (colorSchemesInput[defaultColorSchemeInput]) {\n        if (colorSchemesInput[defaultColorSchemeInput] !== true) {\n          paletteOptions = colorSchemesInput[defaultColorSchemeInput].palette;\n        } else if (defaultColorSchemeInput === 'dark') {\n          // @ts-ignore to prevent the module augmentation test from failing\n          paletteOptions = {\n            mode: 'dark'\n          };\n        }\n      }\n    }\n    const theme = createThemeNoVars({\n      ...options,\n      palette: paletteOptions\n    }, ...args);\n    theme.defaultColorScheme = defaultColorSchemeInput;\n    theme.colorSchemes = colorSchemesInput;\n    if (theme.palette.mode === 'light') {\n      theme.colorSchemes.light = {\n        ...(colorSchemesInput.light !== true && colorSchemesInput.light),\n        palette: theme.palette\n      };\n      attachColorScheme(theme, 'dark', colorSchemesInput.dark);\n    }\n    if (theme.palette.mode === 'dark') {\n      theme.colorSchemes.dark = {\n        ...(colorSchemesInput.dark !== true && colorSchemesInput.dark),\n        palette: theme.palette\n      };\n      attachColorScheme(theme, 'light', colorSchemesInput.light);\n    }\n    return theme;\n  }\n  if (!palette && !('light' in colorSchemesInput) && defaultColorSchemeInput === 'light') {\n    colorSchemesInput.light = true;\n  }\n  return createThemeWithVars({\n    ...rest,\n    colorSchemes: colorSchemesInput,\n    defaultColorScheme: defaultColorSchemeInput,\n    ...(typeof cssVariables !== 'boolean' && cssVariables)\n  }, ...args);\n}", "map": {"version": 3, "names": ["createPalette", "createThemeWithVars", "createThemeNoVars", "attachColorScheme", "theme", "scheme", "colorScheme", "colorSchemes", "undefined", "palette", "mode", "createTheme", "options", "args", "cssVariables", "initialColorSchemes", "light", "defaultColorScheme", "initialDefaultColorScheme", "rest", "defaultColorSchemeInput", "defaultScheme", "colorSchemesInput", "paletteOptions", "dark"], "sources": ["C:/Users/<USER>/OneDrive/Documents/AuthPages/Evolune/medcure/node_modules/@mui/material/esm/styles/createTheme.js"], "sourcesContent": ["import createPalette from \"./createPalette.js\";\nimport createThemeWithVars from \"./createThemeWithVars.js\";\nimport createThemeNoVars from \"./createThemeNoVars.js\";\n// eslint-disable-next-line consistent-return\nfunction attachColorScheme(theme, scheme, colorScheme) {\n  if (!theme.colorSchemes) {\n    return undefined;\n  }\n  if (colorScheme) {\n    theme.colorSchemes[scheme] = {\n      ...(colorScheme !== true && colorScheme),\n      palette: createPalette({\n        ...(colorScheme === true ? {} : colorScheme.palette),\n        mode: scheme\n      }) // cast type to skip module augmentation test\n    };\n  }\n}\n\n/**\n * Generate a theme base on the options received.\n * @param options Takes an incomplete theme object and adds the missing parts.\n * @param args Deep merge the arguments with the about to be returned theme.\n * @returns A complete, ready-to-use theme object.\n */\nexport default function createTheme(options = {},\n// cast type to skip module augmentation test\n...args) {\n  const {\n    palette,\n    cssVariables = false,\n    colorSchemes: initialColorSchemes = !palette ? {\n      light: true\n    } : undefined,\n    defaultColorScheme: initialDefaultColorScheme = palette?.mode,\n    ...rest\n  } = options;\n  const defaultColorSchemeInput = initialDefaultColorScheme || 'light';\n  const defaultScheme = initialColorSchemes?.[defaultColorSchemeInput];\n  const colorSchemesInput = {\n    ...initialColorSchemes,\n    ...(palette ? {\n      [defaultColorSchemeInput]: {\n        ...(typeof defaultScheme !== 'boolean' && defaultScheme),\n        palette\n      }\n    } : undefined)\n  };\n  if (cssVariables === false) {\n    if (!('colorSchemes' in options)) {\n      // Behaves exactly as v5\n      return createThemeNoVars(options, ...args);\n    }\n    let paletteOptions = palette;\n    if (!('palette' in options)) {\n      if (colorSchemesInput[defaultColorSchemeInput]) {\n        if (colorSchemesInput[defaultColorSchemeInput] !== true) {\n          paletteOptions = colorSchemesInput[defaultColorSchemeInput].palette;\n        } else if (defaultColorSchemeInput === 'dark') {\n          // @ts-ignore to prevent the module augmentation test from failing\n          paletteOptions = {\n            mode: 'dark'\n          };\n        }\n      }\n    }\n    const theme = createThemeNoVars({\n      ...options,\n      palette: paletteOptions\n    }, ...args);\n    theme.defaultColorScheme = defaultColorSchemeInput;\n    theme.colorSchemes = colorSchemesInput;\n    if (theme.palette.mode === 'light') {\n      theme.colorSchemes.light = {\n        ...(colorSchemesInput.light !== true && colorSchemesInput.light),\n        palette: theme.palette\n      };\n      attachColorScheme(theme, 'dark', colorSchemesInput.dark);\n    }\n    if (theme.palette.mode === 'dark') {\n      theme.colorSchemes.dark = {\n        ...(colorSchemesInput.dark !== true && colorSchemesInput.dark),\n        palette: theme.palette\n      };\n      attachColorScheme(theme, 'light', colorSchemesInput.light);\n    }\n    return theme;\n  }\n  if (!palette && !('light' in colorSchemesInput) && defaultColorSchemeInput === 'light') {\n    colorSchemesInput.light = true;\n  }\n  return createThemeWithVars({\n    ...rest,\n    colorSchemes: colorSchemesInput,\n    defaultColorScheme: defaultColorSchemeInput,\n    ...(typeof cssVariables !== 'boolean' && cssVariables)\n  }, ...args);\n}"], "mappings": "AAAA,OAAOA,aAAa,MAAM,oBAAoB;AAC9C,OAAOC,mBAAmB,MAAM,0BAA0B;AAC1D,OAAOC,iBAAiB,MAAM,wBAAwB;AACtD;AACA,SAASC,iBAAiBA,CAACC,KAAK,EAAEC,MAAM,EAAEC,WAAW,EAAE;EACrD,IAAI,CAACF,KAAK,CAACG,YAAY,EAAE;IACvB,OAAOC,SAAS;EAClB;EACA,IAAIF,WAAW,EAAE;IACfF,KAAK,CAACG,YAAY,CAACF,MAAM,CAAC,GAAG;MAC3B,IAAIC,WAAW,KAAK,IAAI,IAAIA,WAAW,CAAC;MACxCG,OAAO,EAAET,aAAa,CAAC;QACrB,IAAIM,WAAW,KAAK,IAAI,GAAG,CAAC,CAAC,GAAGA,WAAW,CAACG,OAAO,CAAC;QACpDC,IAAI,EAAEL;MACR,CAAC,CAAC,CAAC;IACL,CAAC;EACH;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASM,WAAWA,CAACC,OAAO,GAAG,CAAC,CAAC;AAChD;AACA,GAAGC,IAAI,EAAE;EACP,MAAM;IACJJ,OAAO;IACPK,YAAY,GAAG,KAAK;IACpBP,YAAY,EAAEQ,mBAAmB,GAAG,CAACN,OAAO,GAAG;MAC7CO,KAAK,EAAE;IACT,CAAC,GAAGR,SAAS;IACbS,kBAAkB,EAAEC,yBAAyB,GAAGT,OAAO,EAAEC,IAAI;IAC7D,GAAGS;EACL,CAAC,GAAGP,OAAO;EACX,MAAMQ,uBAAuB,GAAGF,yBAAyB,IAAI,OAAO;EACpE,MAAMG,aAAa,GAAGN,mBAAmB,GAAGK,uBAAuB,CAAC;EACpE,MAAME,iBAAiB,GAAG;IACxB,GAAGP,mBAAmB;IACtB,IAAIN,OAAO,GAAG;MACZ,CAACW,uBAAuB,GAAG;QACzB,IAAI,OAAOC,aAAa,KAAK,SAAS,IAAIA,aAAa,CAAC;QACxDZ;MACF;IACF,CAAC,GAAGD,SAAS;EACf,CAAC;EACD,IAAIM,YAAY,KAAK,KAAK,EAAE;IAC1B,IAAI,EAAE,cAAc,IAAIF,OAAO,CAAC,EAAE;MAChC;MACA,OAAOV,iBAAiB,CAACU,OAAO,EAAE,GAAGC,IAAI,CAAC;IAC5C;IACA,IAAIU,cAAc,GAAGd,OAAO;IAC5B,IAAI,EAAE,SAAS,IAAIG,OAAO,CAAC,EAAE;MAC3B,IAAIU,iBAAiB,CAACF,uBAAuB,CAAC,EAAE;QAC9C,IAAIE,iBAAiB,CAACF,uBAAuB,CAAC,KAAK,IAAI,EAAE;UACvDG,cAAc,GAAGD,iBAAiB,CAACF,uBAAuB,CAAC,CAACX,OAAO;QACrE,CAAC,MAAM,IAAIW,uBAAuB,KAAK,MAAM,EAAE;UAC7C;UACAG,cAAc,GAAG;YACfb,IAAI,EAAE;UACR,CAAC;QACH;MACF;IACF;IACA,MAAMN,KAAK,GAAGF,iBAAiB,CAAC;MAC9B,GAAGU,OAAO;MACVH,OAAO,EAAEc;IACX,CAAC,EAAE,GAAGV,IAAI,CAAC;IACXT,KAAK,CAACa,kBAAkB,GAAGG,uBAAuB;IAClDhB,KAAK,CAACG,YAAY,GAAGe,iBAAiB;IACtC,IAAIlB,KAAK,CAACK,OAAO,CAACC,IAAI,KAAK,OAAO,EAAE;MAClCN,KAAK,CAACG,YAAY,CAACS,KAAK,GAAG;QACzB,IAAIM,iBAAiB,CAACN,KAAK,KAAK,IAAI,IAAIM,iBAAiB,CAACN,KAAK,CAAC;QAChEP,OAAO,EAAEL,KAAK,CAACK;MACjB,CAAC;MACDN,iBAAiB,CAACC,KAAK,EAAE,MAAM,EAAEkB,iBAAiB,CAACE,IAAI,CAAC;IAC1D;IACA,IAAIpB,KAAK,CAACK,OAAO,CAACC,IAAI,KAAK,MAAM,EAAE;MACjCN,KAAK,CAACG,YAAY,CAACiB,IAAI,GAAG;QACxB,IAAIF,iBAAiB,CAACE,IAAI,KAAK,IAAI,IAAIF,iBAAiB,CAACE,IAAI,CAAC;QAC9Df,OAAO,EAAEL,KAAK,CAACK;MACjB,CAAC;MACDN,iBAAiB,CAACC,KAAK,EAAE,OAAO,EAAEkB,iBAAiB,CAACN,KAAK,CAAC;IAC5D;IACA,OAAOZ,KAAK;EACd;EACA,IAAI,CAACK,OAAO,IAAI,EAAE,OAAO,IAAIa,iBAAiB,CAAC,IAAIF,uBAAuB,KAAK,OAAO,EAAE;IACtFE,iBAAiB,CAACN,KAAK,GAAG,IAAI;EAChC;EACA,OAAOf,mBAAmB,CAAC;IACzB,GAAGkB,IAAI;IACPZ,YAAY,EAAEe,iBAAiB;IAC/BL,kBAAkB,EAAEG,uBAAuB;IAC3C,IAAI,OAAON,YAAY,KAAK,SAAS,IAAIA,YAAY;EACvD,CAAC,EAAE,GAAGD,IAAI,CAAC;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}